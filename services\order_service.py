from utils.api_utils import (
    get_initial_order_list, get_order_details, process_orders,
    get_common_params
)
from utils.session import session
import config
import requests
import json

MANUAL_INVOICE_FILE = 'manual_invoice.json'

def get_manual_invoice(order_sn=None):
    """Load manual invoice data from file"""
    try:
        with open(MANUAL_INVOICE_FILE, 'r') as f:
            invoices = json.load(f)
            if order_sn:
                # Find invoice by reference_id
                for invoice in invoices.values():
                    if invoice.get('reference_id') == order_sn:
                        return invoice
                return None
            return invoices
    except FileNotFoundError:
        return {} if not order_sn else None

def process_manual_invoice(order_sn):
    """Convert manual invoice to order format"""
    invoice = get_manual_invoice(order_sn)
    if not invoice:
        return None
        
    # Create order data structure matching Shopee format
    order_data = {
        'data': {
            'card_list': [{
                'order_card': {
                    'card_header': {
                        'order_sn': invoice['reference_id']
                    },
                    'order_ext_info': {
                        'order_id': invoice['id']
                    },
                    'status_info': {
                        'status': 'To Ship' if invoice['status'] == 'paid' else 'Unpaid'
                    },
                    'buyer_info': {
                        'buyer_user': {
                            'user_name': invoice['customer']['name']
                        }
                    }
                }
            }]
        }
    }
    
    return order_data

def get_to_ship_orders():
    initial_data = get_initial_order_list("to_ship")
    return process_orders(initial_data, "to_ship")

def get_shipped_orders():
    initial_data = get_initial_order_list("shipped")
    return process_orders(initial_data, "shipped", status_filter="Shipped")

def get_completed_orders():
    initial_data = get_initial_order_list("completed")
    return process_orders(initial_data, "completed", status_filter="Completed")

def search_order(order_sn):
    # First check manual invoices
    manual_order = process_manual_invoice(order_sn)
    if manual_order:
        return manual_order
        
    # If not found, proceed with normal Shopee order search
    initial_data = get_initial_order_list("all", order_sn)
    return process_orders(initial_data, "all")

def ship_order(order_sn):
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return {"error": "Order not found"}, 404

    order = order_data['data']['card_list'][0]
    
    # Handle both "To Ship" and "Completed"/"Shipped" cases
    if 'package_level_order_card' in order:
        package = order['package_level_order_card']['package_list'][0]
        order_id = order['package_level_order_card']['order_ext_info']['order_id']
        package_number = package['package_ext_info']['package_number']
    elif 'order_card' in order:
        order_id = order['order_card']['order_ext_info']['order_id']
        package_number = order['order_card']['package_ext_info_list'][0]['package_number']
    else:
        return {"error": "Unexpected order structure"}, 500

    payload = {
        "order_id": order_id,
        "package_number": package_number,
        "shipping_proof": "",
        "integrated": 0,
        "shipping_carrier": None,
        "shipping_mode": "non_integrated"
    }

    ship_response = session.post(
        config.INIT_ORDER_URL,
        params=get_common_params(),
        json=payload
    )

    if ship_response.status_code != 200:
        return {"error": "Failed to initiate shipping"}, 500

    ship_data = ship_response.json()
    if ship_data['code'] != 0:
        return {"error": ship_data['message']}, 400

    return {"message": "Order shipped successfully", "data": ship_data['data']}, 200

def get_order_status(order_sn):
    # First check if this is a manual invoice
    invoice = get_manual_invoice(order_sn)
    if invoice:
        return {
            "order_sn": invoice['reference_id'],
            "status": 'To Ship' if invoice['status'] == 'paid' else 'Unpaid'
        }, 200

    # If not a manual invoice, proceed with normal Shopee order status check
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return {"error": "Order not found"}, 404

    order = order_data['data']['card_list'][0]
    
    # Handle "To Ship" status
    if 'package_level_order_card' in order:
        package = order['package_level_order_card']['package_list'][0]
        status = package['status_info']['status']
        order_sn = order['package_level_order_card']['card_header']['order_sn']
    # Handle "Completed" or "Shipped" status
    elif 'order_card' in order:
        if('return_id' in order['order_card']['order_ext_info']):
            status = "Refunded"
        else:
            status = order['order_card']['status_info']['status']
    else:
        return {"error": "Unexpected order structure"}, 500

    return {
        "order_sn": order_sn,
        "status": status
    }, 200

def get_order_details(order_sn):
    """
    Fetch detailed information for a specific order using order_sn.
    Now supports both Shopee orders and manual invoices.
    """
    # First check if this is a manual invoice
    invoice = get_manual_invoice(order_sn)
    if invoice:
        # Convert invoice to order details format
        order_data = {
            'message': 'Order details fetched successfully',
            'data': {
                'order_sn': invoice['reference_id'],
                'buyer_user': {
                    'user_name': invoice['customer']['name']
                },
                'order_items': [{
                    'var_sku': invoice.get('var_sku', 'N/A'),
                    'product': {
                        'name': invoice.get('description', 'Manual Invoice Item')
                    },
                    'item_model': {
                        'sku': invoice.get('var_sku', 'N/A')
                    }
                }],
                'total_price': invoice['amount'],
                'buyer_address_name': invoice['customer']['name'],
                'buyer_address_phone': invoice['customer']['contact'],
                'create_time': invoice['created_at'],
                'status': invoice['status'],
                'is_manual_invoice': True
            }
        }
        return order_data, 200

    # If not a manual invoice, proceed with normal Shopee order details
    order_id_response, status_code = get_order_id_by_sn(order_sn)
    
    if status_code != 200:
        # Propagate the error
        return order_id_response, status_code
    
    order_id = order_id_response.get('order_id')
    if not order_id:
        return {"error": "order_id could not be retrieved"}, 500

    # Step 2: Proceed to fetch order details using order_id
    # Prepare the GET request parameters
    params = {
        "SPC_CDS": config.SPC_CDS,
        "SPC_CDS_VER": config.SPC_CDS_VER,
        "order_id": order_id
    }

    try:
        response = session.get(
            config.ORDER_DETAILS_URL_SPECIFIC_ORDER,
            params=params,
            timeout=config.REQUEST_TIMEOUT
        )
        response.raise_for_status()
    except requests.RequestException as e:
        return {"error": f"Failed to fetch order details: {str(e)}"}, 500

    data = response.json()

    if data.get('code') != 0:
        return {"error": data.get('message', 'Unknown error')}, 400

    order_data = data.get('data', {})
    
    # Extract var_sku from order items
    order_items = order_data.get('order_items', [])
    for item in order_items:
        var_sku = item.get('var_sku')
        if var_sku:
            item['var_sku'] = var_sku
        else:
            # Fetch var_sku if not present
            # This example assumes var_sku can be derived or is nested; adjust as needed
            item['var_sku'] = "N/A"

    return {"message": "Order details fetched successfully", "data": order_data}, 200

def get_order_id_by_sn(order_sn):
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return {"error": "Order not found"}, 404

    order = order_data['data']['card_list'][0]
    
    # Extract order_id based on the order structure
    if 'package_level_order_card' in order:
        order_id = order['package_level_order_card']['order_ext_info']['order_id']
    elif 'order_card' in order:
        order_id = order['order_card']['order_ext_info']['order_id']
    else:
        return {"error": "Unexpected order structure"}, 500

    return {"order_id": order_id}, 200
    