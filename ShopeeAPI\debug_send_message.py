#!/usr/bin/env python3
"""
Debug script for testing send_message endpoint with different usernames.
This script will help identify why "syahmialfabet" fails while "me0tn_14qo" works.
"""

import sys
import os
import json
import requests
from typing import Dict, Any

# Add the parent directory to the path so we can import ShopeeAPI modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ShopeeAPI.client import Shopee<PERSON><PERSON>
from ShopeeAPI.config import Config


def test_conversation_search(api: ShopeeAPI, username: str) -> Dict[str, Any]:
    """Test the conversation search API directly."""
    print(f"\n=== Testing conversation search for username: {username} ===")
    
    # Prepare the params for the conversation search request
    params = {
        "per_page": "20",
        "keyword": username,
        "type": "3",
        "_uid": f"0-{api.config.shop_id}",
        "_v": "8.5.6",
        "csrf_token": api.session.credential_manager.get_csrf_token(),
        "SPC_CDS_CHAT": api.session.credential_manager.get_spc_cds_chat(),
        "x-shop-region": api.config.region_id
    }
    
    print(f"Search URL: {api.config.urls['conversation_search']}")
    print(f"Search params: {params}")
    
    try:
        # Send the GET request to search for conversation info
        response = api.session.get(
            api.config.urls["conversation_search"],
            params=params
        )
        
        print(f"Search response status: {response.status_code}")
        
        if response.status_code == 200:
            search_results = response.json()
            print(f"Search results structure: {json.dumps(search_results, indent=2, ensure_ascii=False)}")
            return search_results
        else:
            print(f"Search failed with status {response.status_code}: {response.text}")
            return {"error": f"Search failed with status {response.status_code}"}
            
    except Exception as e:
        print(f"Exception during search: {str(e)}")
        return {"error": f"Exception during search: {str(e)}"}


def test_recent_conversations(api: ShopeeAPI, username: str) -> Dict[str, Any]:
    """Test finding the user in recent conversations."""
    print(f"\n=== Testing recent conversations for username: {username} ===")
    
    try:
        recent_conversations, status_code = api.chat_service.get_recent_conversations()
        print(f"Recent conversations status: {status_code}")
        
        if status_code == 200:
            print(f"Recent conversations structure: {json.dumps(recent_conversations, indent=2, ensure_ascii=False)}")
            
            # Check if we have conversations in the response
            conversations = []
            if 'conversations' in recent_conversations:
                conversations = recent_conversations['conversations']
            elif isinstance(recent_conversations, list):
                conversations = recent_conversations
            
            print(f"Found {len(conversations)} conversations")
            
            # Look for the user in the conversations
            for i, conv in enumerate(conversations):
                conv_username = conv.get('to_name', '').lower()
                print(f"Conversation {i}: to_name='{conv.get('to_name')}', to_id={conv.get('to_id')}")
                
                if conv_username == username.lower():
                    print(f"*** FOUND USER '{username}' in recent conversations! ***")
                    print(f"User details: {json.dumps(conv, indent=2, ensure_ascii=False)}")
                    return conv
            
            print(f"User '{username}' NOT found in recent conversations")
            return {"error": f"User '{username}' not found in recent conversations"}
            
        else:
            print(f"Failed to get recent conversations: {recent_conversations}")
            return {"error": "Failed to get recent conversations"}
            
    except Exception as e:
        print(f"Exception getting recent conversations: {str(e)}")
        return {"error": f"Exception getting recent conversations: {str(e)}"}


def test_conversation_info_by_user_id(api: ShopeeAPI, user_id: int) -> Dict[str, Any]:
    """Test getting conversation info by user ID."""
    print(f"\n=== Testing conversation info for user_id: {user_id} ===")
    
    try:
        conversation_info, error = api.chat_service._get_conversation_by_user_id(user_id)
        
        if error:
            print(f"Error getting conversation info: {error}")
            return error
        else:
            print(f"Conversation info: {json.dumps(conversation_info, indent=2, ensure_ascii=False)}")
            return conversation_info
            
    except Exception as e:
        print(f"Exception getting conversation info: {str(e)}")
        return {"error": f"Exception getting conversation info: {str(e)}"}


def test_send_message_endpoint(username: str, message: str = "Test message from debug script") -> Dict[str, Any]:
    """Test the send_message endpoint via HTTP API."""
    print(f"\n=== Testing send_message endpoint for username: {username} ===")
    
    # Test via HTTP API
    api_url = "http://localhost:456"  # Adjust port if needed
    endpoint = f"{api_url}/chat/send_message"
    
    payload = {
        "text": message,
        "username": username,
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    
    print(f"Sending POST request to: {endpoint}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(endpoint, json=payload)
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}
            
    except Exception as e:
        print(f"Exception calling API: {str(e)}")
        return {"error": f"Exception calling API: {str(e)}"}


def main():
    """Main debug function."""
    print("=== ShopeeAPI Send Message Debug Script ===")
    
    # Test usernames
    working_username = "me0tn_14qo"
    failing_username = "syahmialfabet"
    
    try:
        # Initialize ShopeeAPI
        print("\nInitializing ShopeeAPI...")
        config = Config()
        api = ShopeeAPI(config)
        
        print(f"Shop ID: {api.config.shop_id}")
        print(f"Region ID: {api.config.region_id}")
        
        # Test both usernames
        for username in [working_username, failing_username]:
            print(f"\n{'='*60}")
            print(f"TESTING USERNAME: {username}")
            print(f"{'='*60}")
            
            # Step 1: Test conversation search
            search_results = test_conversation_search(api, username)
            
            # Step 2: Test recent conversations
            recent_conv_result = test_recent_conversations(api, username)
            
            # Step 3: If we found user_id, test getting conversation info
            user_id = None
            if isinstance(recent_conv_result, dict) and 'to_id' in recent_conv_result:
                user_id = recent_conv_result['to_id']
                print(f"\nFound user_id from recent conversations: {user_id}")
                test_conversation_info_by_user_id(api, user_id)
            elif isinstance(search_results, dict) and 'data' in search_results:
                # Try to extract user_id from search results
                if 'users' in search_results['data']:
                    for user in search_results['data']['users']:
                        if user.get('username', '').lower() == username.lower():
                            user_id = user.get('id')
                            print(f"\nFound user_id from search results: {user_id}")
                            test_conversation_info_by_user_id(api, user_id)
                            break
            
            # Step 4: Test the actual send_message endpoint
            test_send_message_endpoint(username)
            
            print(f"\n{'='*60}")
            print(f"COMPLETED TESTING FOR: {username}")
            print(f"{'='*60}")
    
    except Exception as e:
        print(f"Error in main: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
