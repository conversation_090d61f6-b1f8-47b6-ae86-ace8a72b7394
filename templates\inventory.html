{% extends "base.html" %}

{% block title %}Inventory Management{% endblock %}

{% block header %}Inventory Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                        <i class="fas fa-box text-white text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Total Items Redeemed
                            </dt>
                            <dd class="text-3xl font-semibold text-gray-900" id="totalRedeemed">
                                0
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                        <i class="fas fa-warehouse text-white text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Total Stock Items
                            </dt>
                            <dd class="text-3xl font-semibold text-gray-900" id="totalStock">
                                0
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                        <i class="fas fa-clipboard-list text-white text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Manual Orders
                            </dt>
                            <dd class="text-3xl font-semibold text-gray-900" id="totalManualOrders">
                                0
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Redeemed Items Table -->
    <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h2 class="text-2xl font-bold mb-4">Redeemed Items</h2>
            <table id="redeemedItemsTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="selectAllRedeemed" class="form-checkbox h-4 w-4">
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order
                            SN</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAR
                            SKU</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Replace Count</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h2 class="text-2xl font-bold mb-4">Replacement Rankings</h2>
            <table id="replacementRankingsTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order
                            SN</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAR
                            SKU</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total
                            Replacements</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Current Stock Table -->
    <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h2 class="text-2xl font-bold mb-4">Current Stock</h2>
            <table id="currentStockTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAR
                            SKU</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Quantity</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Is
                            Unlimited</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Manual Orders Table -->
    <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h2 class="text-2xl font-bold mb-4">Manual Orders</h2>
            <table id="manualOrdersTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="selectAllManual" class="form-checkbox h-4 w-4">
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order
                            SN</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAR
                            SKU</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created At</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <style>
        /* DataTable Custom Styling */
        .dataTables_wrapper {
            font-family: 'Arial', sans-serif;
            padding-left: 30px;
            padding-right: 30px;
        }

        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            margin: 15px 0;
            font-size: 14px;
        }

        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 5px 10px;
        }

        .dataTables_wrapper .dataTables_length select {
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 5px 25px 5px 10px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23000' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E") no-repeat right 10px center/8px 8px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            border: none;
            padding: 5px 10px;
            margin: 0 2px;
            border-radius: 4px;
            background-color: #f7fafc;
            color: #4a5568;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background-color: #4a5568;
            color: white !important;
        }

        table.dataTable thead th {
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
        }

        table.dataTable tbody td {
            padding: 12px 10px;
        }

        table.dataTable tbody tr:hover {
            background-color: #f7fafc;
        }
    </style>

    <div id="replaceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Replace Content</h3>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Replace Type</label>
                    <select id="replaceType"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="single">Single Order</option>
                        <option value="var_sku">All Orders with Same VAR SKU</option>
                        <option value="content">All Orders with Same Content</option>
                    </select>
                </div>

                <!-- Content Fields -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Header</label>
                        <textarea id="headerContent" rows="2"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 whitespace-pre-wrap"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Content</label>
                        <textarea id="mainContent" rows="4"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 whitespace-pre-wrap"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Footer</label>
                        <textarea id="footerContent" rows="2"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 whitespace-pre-wrap"></textarea>
                    </div>
                    <div class="mt-2 text-sm text-gray-600 mb-4">
                        <p>Available variables for message customization:</p>
                        <ul class="list-disc pl-5 mt-2">
                            <li>{order_sn} - Order serial number</li>
                            <li>{buyer_username} - Buyer's username</li>
                            <li>{item_name} - Name of the item</li>
                            <li>{item_price} - Price of the item</li>
                            <li>{buyer_name} - Buyer's name</li>
                            <li>{buyer_phone} - Buyer's phone number</li>
                            <li>{create_time} - Order creation time</li>
                            <li>{shipping_address} - Shipping address</li>
                            <li>{item_sku} - Item SKU</li>
                            <li>{item_quantity} - Quantity of items</li>
                            <li>{payment_method} - Payment method</li>
                            <li>{shop_name} - Shop name</li>
                            <li>{escrow_release_time} - Escrow release time</li>
                            <li>{buyer_rating} - Buyer's rating</li>
                            <li>{order_status} - Order status</li>
                        </ul>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex justify-end mt-4 space-x-3">
                    <button id="cancelReplace" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                        Cancel
                    </button>
                    <button id="confirmReplace" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                        Replace
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function initializeDataTable(tableId, orderColumn = 0, orderDir = 'desc') {
            return $(`#${tableId}`).DataTable({
                responsive: true,
                order: [[orderColumn, orderDir]],
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                language: {
                    paginate: {
                        previous: "&#8592;",
                        next: "&#8594;"
                    },
                    lengthMenu: "_MENU_ per page",
                    search: "",
                    searchPlaceholder: "Search...",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries"
                },
                dom: "<'flex justify-between items-center'<l><f>>" +
                    "<'overflow-x-auto'tr>" +
                    "<'flex justify-between items-center'<i><p>>"
            });
        }

        function fetchInventoryData() {
            fetch('/admin/get_inventory_data')
                .then(response => response.json())
                .then(data => {
                    // Update summary cards
                    $('#totalRedeemed').text(data.redeemed_items.length);
                    $('#totalStock').text(data.current_stock.length);
                    $('#totalManualOrders').text(data.manual_orders.length);

                    // Destroy existing DataTables FIRST
                    ['redeemedItemsTable', 'currentStockTable', 'manualOrdersTable', 'replacementRankingsTable'].forEach(tableId => {
                        if ($.fn.DataTable.isDataTable(`#${tableId}`)) {
                            $(`#${tableId}`).DataTable().destroy();
                        }
                        // Clear the table contents
                        $(`#${tableId} tbody`).empty();
                    });

                    // THEN populate the tables with new data
                    populateRedeemedItems(data.redeemed_items);
                    populateCurrentStock(data.current_stock);
                    populateManualOrders(data.manual_orders);
                    populateReplacementRankings(data.redeemed_items);

                    // FINALLY reinitialize DataTables
                    const tables = {
                        'redeemedItemsTable': [4, 'desc'],
                        'currentStockTable': [1, 'desc'],
                        'manualOrdersTable': [3, 'desc'],
                        'replacementRankingsTable': [4, 'desc']
                    };

                    Object.entries(tables).forEach(([tableId, [orderColumn, orderDir]]) => {
                        initializeDataTable(tableId, orderColumn, orderDir);
                    });
                })
                .catch(error => console.error('Error fetching inventory data:', error));
        }

        function populateRedeemedItems(items) {
            const tbody = $('#redeemedItemsTable tbody');
            tbody.empty();
            items.forEach(item => {
                const escapedOrderSn = encodeURIComponent(item.order_sn);
                const escapedVarSku = encodeURIComponent(item.var_sku);
                const escapedItem = encodeURIComponent(item.item);

                // Improved content processing
                let displayHtml = (item.item || '')
                    // Replace <br> tags with single newline
                    .replace(/<br\s*\/?>/gi, '\n')
                    // Replace input elements with their values
                    .replace(/<input[^>]*value=["']([^"']*?)["'][^>]*>/g, '$1')
                    // Remove all other HTML tags but keep their content
                    .replace(/<[^>]+>/g, '')
                    // Replace multiple spaces with single space
                    .replace(/\s+/g, ' ')
                    // Replace multiple newlines with single newline
                    .replace(/\n{2,}/g, '\n')
                    // Remove spaces around newlines
                    .replace(/\s*\n\s*/g, '\n')
                    // Remove spaces at start/end of content
                    .trim();

                tbody.append(`
            <tr>
                <td class="px-6 py-4">
                    <input type="checkbox" class="form-checkbox h-4 w-4 select-item" 
                           data-order-sn="${item.order_sn}" 
                           data-var-sku="${item.var_sku}"
                           data-item="${escapedItem}">
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.order_sn}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.var_sku}</td>
                <td class="px-6 py-4 text-sm text-gray-500 whitespace-pre-line" data-search="${displayHtml}">
                    ${displayHtml}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.replace_count || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button 
                        onclick="showReplaceModal('${escapedOrderSn}', '${escapedVarSku}', '${escapedItem}')"
                        class="text-indigo-600 hover:text-indigo-900">
                        Replace
                    </button>
                </td>
            </tr>
        `);
            });
        }

        function populateReplacementRankings(items) {
            const tbody = $('#replacementRankingsTable tbody');
            tbody.empty();

            // Sort items by replace_count in descending order
            const sortedItems = [...items].sort((a, b) => (b.replace_count || 0) - (a.replace_count || 0));

            sortedItems.forEach((item, index) => {
                if ((item.replace_count || 0) > 0) {  // Only show items that have been replaced at least once
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = item.item || '';
                    let searchText = tempDiv.textContent || tempDiv.innerText || '';

                    tbody.append(`
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${index + 1}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.order_sn}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.var_sku}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.item}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.replace_count || 0}</td>
                </tr>
            `);
                }
            });
        }


        function populateManualOrders(orders) {
            const tbody = $('#manualOrdersTable tbody');
            tbody.empty();
            orders.forEach(order => {
                // Use encodeURIComponent for proper escaping
                const escapedOrderSn = encodeURIComponent(order.order_sn);
                const escapedVarSku = encodeURIComponent(order.var_sku);

                tbody.append(`
            <tr>
                <td class="px-6 py-4">
                    <input type="checkbox" class="form-checkbox h-4 w-4 select-item" 
                           data-order-sn="${order.order_sn}" 
                           data-var-sku="${order.var_sku}"
                           data-item="">
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${order.order_sn}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${order.var_sku}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${order.status}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${order.created_at}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button 
                        onclick="showReplaceModal('${escapedOrderSn}', '${escapedVarSku}', '')"
                        class="text-indigo-600 hover:text-indigo-900">
                        Replace
                    </button>
                </td>
            </tr>
        `);
            });
        }

        function populateCurrentStock(stock) {
            const tbody = $('#currentStockTable tbody');
            tbody.empty();
            stock.forEach(item => {
                tbody.append(`
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.var_sku}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.quantity}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.is_unlimited ? 'Yes' : 'No'}</td>
            </tr>
        `);
            });
        }

        // Add these new functions for handling the replace functionality
        let currentReplaceData = {};

        function showReplaceModal(orderSn, varSku, item) {
            currentReplaceData = {
                order_sn: decodeURIComponent(orderSn),
                var_sku: decodeURIComponent(varSku),
                item: decodeURIComponent(item || '')
            };

            // Create temporary div to handle HTML content
            const tempDiv = document.createElement('div');
            tempDiv.style.whiteSpace = 'pre-wrap';
            tempDiv.innerHTML = currentReplaceData.item;

            // Process text content while preserving line breaks
            let plainText = '';
            const processNode = (node) => {
                if (node.nodeType === Node.TEXT_NODE) {
                    plainText += node.textContent;
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    if (node.tagName === 'BR' || node.tagName === 'P' || node.tagName === 'DIV') {
                        plainText += '\n';
                    }
                    if (node.tagName === 'INPUT' && node.hasAttribute('value')) {
                        plainText += node.getAttribute('value') + '\n';
                    }
                    node.childNodes.forEach(processNode);
                }
            };

            processNode(tempDiv);

            // Clean up and preserve legitimate line breaks
            plainText = plainText.replace(/\n\s*\n/g, '\n\n').trim();

            // Set values in modal
            document.getElementById('headerContent').value = '';
            document.getElementById('mainContent').value = plainText;
            document.getElementById('footerContent').value = '';
            document.getElementById('replaceType').value = 'single';

            const modal = document.getElementById('replaceModal');
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function hideReplaceModal() {
            const modal = document.getElementById('replaceModal');
            modal.classList.add('hidden');

            // Restore scrolling
            document.body.style.overflow = 'auto';

            // Clear the form
            document.getElementById('headerContent').value = '';
            document.getElementById('mainContent').value = '';
            document.getElementById('footerContent').value = '';
            currentReplaceData = {};
        }

        function handleReplace() {
            const replaceType = document.getElementById('replaceType').value;
            const header = document.getElementById('headerContent').value.trim();
            const content = document.getElementById('mainContent').value.trim();
            const footer = document.getElementById('footerContent').value.trim();

            // Validate inputs
            if (!content) {
                alert('Please enter content');
                return;
            }

            const replacementData = {
                type: replaceType,
                order_sn: currentReplaceData.order_sn,
                var_sku: currentReplaceData.var_sku,
                item: currentReplaceData.item,
                new_content: {
                    header: header,
                    content: content,
                    footer: footer
                }
            };

            // Add CSRF token if you're using it
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            fetch('/admin/replace_content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken, // Include if using CSRF protection
                },
                body: JSON.stringify(replacementData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('Content replaced successfully');
                        hideReplaceModal();
                    } else {
                        alert('Error: ' + (data.message || 'Unknown error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error replacing content: ' + error.message);
                });
        }

        function initializeDataTable(tableId, orderColumn = 0, orderDir = 'desc') {
            return $(`#${tableId}`).DataTable({
                responsive: true,
                order: [[orderColumn, orderDir]],
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                language: {
                    paginate: {
                        previous: "&#8592;",
                        next: "&#8594;"
                    },
                    lengthMenu: "_MENU_ per page",
                    search: "",
                    searchPlaceholder: "Search...",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries"
                },
                dom: "<'flex justify-between items-center'<l><f>>" +
                    "<'overflow-x-auto'tr>" +
                    "<'flex justify-between items-center'<i><p>>"
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            fetchInventoryData();

            document.getElementById('cancelReplace').addEventListener('click', hideReplaceModal);
            document.getElementById('confirmReplace').addEventListener('click', handleReplace);

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    // Prevent default escape key behavior
                    e.preventDefault();
                    // Don't hide modal on escape
                    return false;
                }
            });

            // Initialize DataTables with custom search handling
            const redeemedTable = $('#redeemedItemsTable').DataTable({
                responsive: true,
                order: [[3, 'desc']],
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                language: {
                    paginate: {
                        previous: "&#8592;",
                        next: "&#8594;"
                    },
                    lengthMenu: "_MENU_ per page",
                    search: "",
                    searchPlaceholder: "Search...",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries"
                },
                dom: "<'flex justify-between items-center'<l><f>>" +
                    "<'overflow-x-auto'tr>" +
                    "<'flex justify-between items-center'<i><p>>",
                columnDefs: [{
                    targets: 3,
                    render: function (data, type, row) {
                        if (type === 'filter' || type === 'sort') {
                            const cell = $(row[3]);
                            return cell.data('search') || '';
                        }
                        return data;
                    }
                }],
                search: {
                    "smart": true,
                    "caseInsensitive": true
                }
            });

            let selectedItems = new Set();

            function updateBulkReplaceButton() {
                const btn = document.getElementById('bulkReplaceBtn');
                const count = document.getElementById('selectedCount');
                count.textContent = selectedItems.size;
                btn.classList.toggle('hidden', selectedItems.size === 0);
            }

            // Handle select all checkbox
            ['selectAllRedeemed', 'selectAllManual'].forEach(id => {
                document.getElementById(id).addEventListener('change', (e) => {
                    const table = e.target.closest('table');
                    const checkboxes = table.querySelectorAll('.select-item');
                    checkboxes.forEach(cb => {
                        cb.checked = e.target.checked;
                        const orderSn = cb.dataset.orderSn;
                        if (e.target.checked) {
                            selectedItems.add(orderSn);
                        } else {
                            selectedItems.delete(orderSn);
                        }
                    });
                    updateBulkReplaceButton();
                });
            });

            // Handle individual checkboxes
            document.addEventListener('change', (e) => {
                if (e.target.classList.contains('select-item')) {
                    const orderSn = e.target.dataset.orderSn;
                    if (e.target.checked) {
                        selectedItems.add(orderSn);
                    } else {
                        selectedItems.delete(orderSn);
                    }
                    updateBulkReplaceButton();
                }
            });

            // Handle bulk replace button
            document.getElementById('bulkReplaceBtn').addEventListener('click', () => {
                const firstChecked = document.querySelector('.select-item:checked');
                if (firstChecked) {
                    showReplaceModal(
                        firstChecked.dataset.orderSn,
                        firstChecked.dataset.varSku,
                        firstChecked.dataset.item
                    );
                    // Store all selected order SNs for bulk operation
                    currentReplaceData.selectedOrders = Array.from(selectedItems);
                }
            });
        });
    </script>
    {% endblock %}