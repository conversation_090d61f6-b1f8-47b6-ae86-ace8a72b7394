let bearerToken = '';
let fullCookieText = '';
let isTokenCaptured = false;
let isCookieCaptured = false;

chrome.webRequest.onBeforeSendHeaders.addListener(
  (details) => {
    if (isTokenCaptured) return;

    for (let header of details.requestHeaders) {
      if (header.name.toLowerCase() === 'authorization' && header.value.startsWith('Bearer ')) {
        bearerToken = header.value;
        chrome.storage.local.set({ bearerToken });
        isTokenCaptured = true;
        chrome.webRequest.onBeforeSendHeaders.removeListener(arguments.callee);
        console.log('Bearer token captured, listener removed.');
        break;
      }
    }
  },
  { urls: ["*://seller.shopee.com.my/*"] },
  ["requestHeaders", "extraHeaders"]
);

function updateCookies(domain) {
  if (isCookieCaptured) return; 
  chrome.cookies.getAll({ domain }, (cookies) => {
    fullCookieText = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    chrome.storage.local.set({ fullCookieText });
    isCookieCaptured = true;
    checkAndRemoveListeners();
  });
}

updateCookies("seller.shopee.com.my");
updateCookies("shopee.com.my");


const cookieListener = (changeInfo) => {
  if (isCookieCaptured) return;

  const domain = changeInfo.cookie.domain;
  if (domain.includes("seller.shopee.com.my") || domain.includes("shopee.com.my")) {
    updateCookies(domain);
  }
};

chrome.cookies.onChanged.addListener(cookieListener);

function checkAndRemoveListeners() {
  if (isTokenCaptured && isCookieCaptured) {
    chrome.webRequest.onBeforeSendHeaders.removeListener(arguments.callee);
    chrome.cookies.onChanged.removeListener(cookieListener);
    console.log('All data captured, listeners removed.');
  }
}