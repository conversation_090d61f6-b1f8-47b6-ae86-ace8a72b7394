#!/usr/bin/env python3
"""
API endpoint debug script to understand the send_message issue.
"""

import requests
import json


def test_auth_status():
    """Test the auth status endpoint."""
    print("=== Testing auth status ===")
    
    try:
        response = requests.get("http://localhost:456/auth/check", timeout=10)
        print(f"Auth status: {response.status_code}")
        print(f"Auth response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error checking auth: {e}")
        return False


def test_recent_conversations():
    """Test the recent conversations endpoint."""
    print("\n=== Testing recent conversations ===")
    
    try:
        response = requests.get("http://localhost:456/chat/conversations/recent", timeout=30)
        print(f"Recent conversations status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # Look for our test usernames
            conversations = data.get('conversations', [])
            print(f"\nFound {len(conversations)} conversations")
            
            for username in ["me0tn_14qo", "syahmialfabet"]:
                found = False
                for conv in conversations:
                    if conv.get('to_name', '').lower() == username.lower():
                        print(f"Found {username}: {conv}")
                        found = True
                        break
                if not found:
                    print(f"{username} NOT found in recent conversations")
            
            return data
        else:
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"Error getting recent conversations: {e}")
        return None


def test_conversation_messages_by_username(username: str):
    """Test getting conversation messages by username."""
    print(f"\n=== Testing conversation messages for username: {username} ===")
    
    try:
        url = f"http://localhost:456/chat/conversations/username/{username}/messages"
        response = requests.get(url, timeout=30)
        print(f"Messages status: {response.status_code}")
        print(f"Messages response: {response.text}")
        
        if response.status_code == 200:
            return response.json()
        else:
            return None
            
    except Exception as e:
        print(f"Error getting messages for {username}: {e}")
        return None


def test_send_message_with_debug(username: str, message: str = "Debug test"):
    """Test sending a message with detailed debugging."""
    print(f"\n=== Testing send_message for username: {username} ===")
    
    payload = {
        "text": message,
        "username": username,
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:456/chat/send_message", 
            json=payload, 
            timeout=30
        )
        print(f"Send message status: {response.status_code}")
        print(f"Send message response: {response.text}")
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": response.text, "status_code": response.status_code}
            
    except Exception as e:
        print(f"Error sending message to {username}: {e}")
        return {"error": str(e)}


def analyze_differences():
    """Analyze the differences between working and failing usernames."""
    print("\n" + "="*80)
    print("ANALYZING DIFFERENCES BETWEEN USERNAMES")
    print("="*80)
    
    working_username = "me0tn_14qo"
    failing_username = "syahmialfabet"
    
    # Test recent conversations first
    recent_convs = test_recent_conversations()
    
    # Test conversation messages for both
    for username in [working_username, failing_username]:
        test_conversation_messages_by_username(username)
    
    # Test send message for both
    working_result = test_send_message_with_debug(working_username)
    failing_result = test_send_message_with_debug(failing_username)
    
    print(f"\n=== COMPARISON RESULTS ===")
    print(f"Working username ({working_username}): {'SUCCESS' if 'error' not in working_result else 'FAILED'}")
    print(f"Failing username ({failing_username}): {'SUCCESS' if 'error' not in failing_result else 'FAILED'}")
    
    if 'error' in failing_result:
        print(f"Failing error: {failing_result['error']}")
    
    # Check if both users are in recent conversations
    if recent_convs and 'conversations' in recent_convs:
        conversations = recent_convs['conversations']
        working_in_recent = any(conv.get('to_name', '').lower() == working_username.lower() for conv in conversations)
        failing_in_recent = any(conv.get('to_name', '').lower() == failing_username.lower() for conv in conversations)
        
        print(f"\nIn recent conversations:")
        print(f"  {working_username}: {'YES' if working_in_recent else 'NO'}")
        print(f"  {failing_username}: {'YES' if failing_in_recent else 'NO'}")
        
        if not failing_in_recent:
            print(f"\n*** ROOT CAUSE: {failing_username} is not in recent conversations ***")
            print("This means the user either:")
            print("1. Has never had a conversation with the shop")
            print("2. The conversation is too old to appear in recent conversations")
            print("3. The username is incorrect or has changed")


def main():
    """Main debug function."""
    print("=== ShopeeAPI Send Message Debug Analysis ===")
    
    # Check if server is running and auth is working
    if not test_auth_status():
        print("ERROR: Authentication failed or server not running")
        return
    
    # Analyze the differences
    analyze_differences()


if __name__ == "__main__":
    main()
