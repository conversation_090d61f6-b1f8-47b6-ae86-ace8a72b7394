from flask import Blueprint, jsonify, request
from services.chat_service import send_chat_message, send_image_message, send_order_message, get_recent_conversations, get_recent_latest_messages, get_conversation_messages
from utils.auth import require_api_key

chat_bp = Blueprint('chat', __name__)

@chat_bp.route('/send_chat_message', methods=['POST'])
@require_api_key
def api_send_chat_message():
    payload = request.json
    if not payload:
        return jsonify({"error": "No payload provided"}), 400
    return jsonify(send_chat_message(payload))

@chat_bp.route('/send_image_message', methods=['POST'])
@require_api_key
def api_send_image_message():
    payload = request.json
    if not payload:
        return jsonify({"error": "No payload provided"}), 400
    return jsonify(send_image_message(payload))

@chat_bp.route('/send_order_message', methods=['POST'])
@require_api_key
def api_send_order_message():
    order_sn = request.json.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn is required"}), 400
    return jsonify(send_order_message(order_sn))

@chat_bp.route('/get_recent_conversations', methods=['GET'])
@require_api_key
def api_get_recent_conversations():
    return jsonify(get_recent_conversations())

@chat_bp.route('/get_recent_latest_messages', methods=['GET'])
@require_api_key
def api_get_recent_latest_messages():
    messages = get_recent_latest_messages()
    return jsonify(messages)

@chat_bp.route('/get_conversation_messages/<conversation_id>', methods=['GET'])
@require_api_key
def api_get_conversation_messages(conversation_id):
    if not conversation_id:
        return jsonify({"error": "conversation_id is required"}), 400
    return jsonify(get_conversation_messages(conversation_id))