<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netflix Sign-in Code</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='spinkit.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='shepherd.css') }}">
    <script src="{{ url_for('static', filename='shepherd.js') }}"></script>
    <style>
        .modal-enter {
            opacity: 0;
            transform: scale(0.9);
        }

        .modal-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: opacity 300ms, transform 300ms;
        }

        .modal-exit {
            opacity: 1;
        }

        .modal-exit-active {
            opacity: 0;
            transform: scale(0.9);
            transition: opacity 300ms, transform 300ms;
        }

        .floating-label-input {
            position: relative;
        }

        .floating-label-input input {
            height: 3rem;
            padding-top: 1rem;
        }

        .floating-label-input label {
            position: absolute;
            top: 0.5rem;
            left: 0.75rem;
            transition: all 0.2s ease-out;
            pointer-events: none;
        }

        .floating-label-input input:focus+label,
        .floating-label-input input:not(:placeholder-shown)+label {
            font-size: 0.75rem;
            top: 0;
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            transform: rotate(180deg);
        }

        .wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 150px;
        }

        .wave .shape-fill {
            fill: #FFFFFF;
        }
    </style>
</head>

<body
    class="bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 min-h-screen flex items-center justify-center relative">
    <div class="wave">
        <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path
                d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
                class="shape-fill"></path>
        </svg>
    </div>
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
        <h1 class="text-2xl font-bold text-purple-800 mb-6">Netflix Sign In Code</h1>
        <div class="mb-6">
            <img src="{{ url_for('static', filename='netflix_demo.gif') }}" alt="How to use" class="w-full rounded-lg shadow-md">
            <p class="text-sm text-gray-600 text-start mt-2">⚠️ Watch Tutorial</p>
            <pre class="text-sm text-gray-600 text-start mt-2">⚠️ Guna <b>Handphone Netflix App</b> Untuk Sign In</pre>
            <pre class="text-sm text-gray-600 text-start mt-2">⚠️ Lepastu <b>Handphone Scan QR Code Kat TV</b></pre>
        </div>
        <form id="netflixForm" class="space-y-4">
            <div class="floating-label-input">
                <input type="text" id="orderId" name="orderId" required
                    class="px-3 mt-1 block w-full rounded-md border-purple-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 placeholder-transparent">
                <label for="orderId" class="text-sm font-medium text-purple-700">Order ID</label>
            </div>
            <button type="submit"
                class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 disabled:bg-purple-400 disabled:text-gray-200 disabled:cursor-not-allowed">
                Get Sign In Code
            </button>
            <button id="statusButton" type="button"
                class="mt-4 w-full bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50"
                disabled>
                Check Request Status
            </button>
        </form>
        <div class="flex space-x-2">
            <button id="helpButton"
                class="mt-4 w-1/2 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50">
                Help
            </button>
            <button id="watchTutorialButton"
                class="mt-4 w-1/2 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50">
                Watch Tutorial
            </button>
        </div>
    </div>

    <footer class="fixed bottom-0 w-full text-center py-2 bg-white bg-opacity-80 text-purple-800 z-20">
        &copy; 2024 Copyright. Powered by MTYB Official
    </footer>

    <!-- Add this new modal for the video -->
    <div id="videoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" onclick="handleVideoModalClick(event)">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">How to Get Sign-In Code</h3>
                <video id="tutorialVideo" class="w-full rounded-lg shadow-md" controls>
                    <source src="{{ url_for('static', filename='netflix_demo.mp4') }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <button onclick="closeVideoModal()" class="mt-4 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden"
        onclick="handleModalClick(event)">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div id="modalContent" class="mt-3 text-center">
                <div id="loadingIndicator" class="mx-auto flex items-center justify-center h-12 w-12">
                    <div class="sk-wave sk-primary">
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                    </div>
                </div>
                <div id="successIndicator"
                    class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 hidden">
                    <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div id="errorIndicator"
                    class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 hidden">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2" id="modalTitle">Loading</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500" id="modalMessage">
                        Please wait, we're fetching your Netflix sign-in code.
                    </p>
                    <div id="credentialsContainer" class="mt-4 text-left hidden">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-small">Account:</span>
                            <div class="flex items-center">
                                <span id="netflixAccount" class="mr-2"></span>
                                <button onclick="copyToClipboard('netflixAccount')"
                                    class="text-purple-600 hover:text-purple-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                        <path
                                            d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="font-small">Sign-In Code:</span>
                            <div class="flex items-center">
                                <span id="signInCode" class="mr-2"></span>
                                <button onclick="copyToClipboard('signInCode')"
                                    class="text-purple-600 hover:text-purple-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                        <path
                                            d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('netflixForm');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const successIndicator = document.getElementById('successIndicator');
        const errorIndicator = document.getElementById('errorIndicator');
        const submitButton = form.querySelector('button[type="submit"]');
        const checkStatusButton = document.getElementById('statusButton');

        const ongoingRequests = new Set();
        let isRequestOngoing = false;

        document.getElementById('statusButton').addEventListener('click', async () => {
            const orderId = document.getElementById('orderId').value.trim();
            if (ongoingRequests.size > 0 && ongoingRequests.has(getRequestKey(orderId))) {
                showModal();
                loadingIndicator.classList.remove('hidden');
                successIndicator.classList.add('hidden');
                errorIndicator.classList.add('hidden');
                modalTitle.textContent = 'Loading';
                modalMessage.textContent = 'Checking request status...';

                try {
                    const response = await axios.get(`/api/get_netflix_signin_code_status/${orderId}`);
                    console.log('Status response:', response.data);

                    if (response.data.status === 'in_progress') {
                        modalTitle.textContent = 'In Progress';
                        modalMessage.textContent = 'Your request is still being processed. Please check again later.';
                        document.getElementById('credentialsContainer').classList.add('hidden');
                    } else if (response.data.status === 'completed') {
                        handleSuccessResponse(response.data.result);
                    } else {
                        handleErrorResponse({ response: { data: { error: 'Request failed or not found.' } } });
                    }
                } catch (error) {
                    console.error('Error in status check:', error);
                    handleErrorResponse(error);
                }
            } else {
                alert('No ongoing request for this Order ID.');
            }
        });

        function showVideoModal() {
            const videoModal = document.getElementById('videoModal');
            videoModal.classList.remove('hidden');
            videoModal.classList.add('modal-enter');
            setTimeout(() => {
                videoModal.classList.remove('modal-enter');
                videoModal.classList.add('modal-enter-active');
            }, 10);
        }

        function closeVideoModal() {
            const videoModal = document.getElementById('videoModal');
            const tutorialVideo = document.getElementById('tutorialVideo');
            tutorialVideo.pause();
            videoModal.classList.add('modal-exit');
            setTimeout(() => {
                videoModal.classList.add('modal-exit-active');
                setTimeout(() => {
                    videoModal.classList.add('hidden');
                    videoModal.classList.remove('modal-exit', 'modal-exit-active');
                }, 300);
            }, 10);
        }

        function handleVideoModalClick(event) {
            if (event.target.id === 'videoModal') {
                closeVideoModal();
            }
        }

        function getRequestKey(orderId) {
            return `netflix:${orderId}`;
        }

        function showModal() {
            modal.classList.remove('hidden');
            modal.classList.add('modal-enter');
            setTimeout(() => {
                modal.classList.remove('modal-enter');
                modal.classList.add('modal-enter-active');
            }, 10);
        }

        function hideModal() {
            modal.classList.add('modal-exit');
            setTimeout(() => {
                modal.classList.add('modal-exit-active');
                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.classList.remove('modal-exit', 'modal-exit-active');
                }, 300);
            }, 10);
        }

        function handleModalClick(event) {
            if (event.target === modal) {
                hideModal();
            }
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const orderId = document.getElementById('orderId').value.trim();

            const requestKey = getRequestKey(orderId);

            ongoingRequests.add(requestKey);
            submitButton.disabled = true;
            isRequestOngoing = true;
            checkStatusButton.disabled = false;

            showModal();
            loadingIndicator.classList.remove('hidden');
            successIndicator.classList.add('hidden');
            errorIndicator.classList.add('hidden');
            modalTitle.textContent = 'Processing';
            modalMessage.textContent = 'Verifying order and fetching Netflix sign in code...';

            try {
                const response = await axios.post('/api/get_netflix_signin_code', { order_sn: orderId });
                if (response.status === 202) {
                    // Display Netflix account immediately if available
                    if (response.data.netflix_account) {
                        document.getElementById('credentialsContainer').classList.remove('hidden');
                        document.getElementById('netflixAccount').textContent = response.data.netflix_account;
                        modalMessage.textContent = 'Netflix account is ready. Waiting for sign-in code...';
                    }
                    pollForResult(orderId);
                } else {
                    handleSuccessResponse(response.data);
                }
            } catch (error) {
                handleErrorResponse(error);
            }
        });

        async function pollForResult(orderId) {
            try {
                const response = await axios.get(`/api/get_netflix_signin_code_status/${orderId}`);
                if (response.data.status === 'completed') {
                    handleSuccessResponse(response.data.result);
                } else if (response.data.status === 'failed') {
                    handleErrorResponse({ response: { data: { error: response.data.error } } });
                } else if (response.data.status === 'not_found') {
                    handleErrorResponse({ response: { data: { error: 'No request found for this order. Please try submitting again.' } } });
                } else if (response.data.status === 'in_progress') {
                    // Update Netflix account if available
                    if (response.data.netflix_account) {
                        document.getElementById('credentialsContainer').classList.remove('hidden');
                        document.getElementById('netflixAccount').textContent = response.data.netflix_account;
                    }
                    setTimeout(() => pollForResult(orderId), 5000);
                } else {
                    handleErrorResponse({ response: { data: { error: 'Unknown status received' } } });
                }
            } catch (error) {
                handleErrorResponse(error);
            }
        }

        function handleSuccessResponse(data) {
            console.log('Success response data:', data);
            loadingIndicator.classList.add('hidden');
            successIndicator.classList.remove('hidden');
            modalTitle.textContent = 'Status';

            if (data.cooldown_time) {
                // Order is on cooldown
                modalMessage.innerHTML = `
                <p>This order has already been redeemed recently.</p>
                <p>Cooldown time: ${formatCooldownTime(data.cooldown_time)}</p>
                <p>Please try again after the cooldown period.</p>
            `;
                document.getElementById('credentialsContainer').classList.add('hidden');
            } else {
                // Normal success response
                const signInCode = data.signin_code;
                const netflixAccount = data.netflix_account;

                document.getElementById('credentialsContainer').classList.remove('hidden');
                document.getElementById('netflixAccount').textContent = netflixAccount || 'Not available';
                document.getElementById('signInCode').textContent = signInCode || 'Not available';

                modalMessage.innerHTML = `
                <p>Your Netflix sign-in code is ready.</p>
                <p>Redeem Period: ${data.redeem_period ? data.redeem_period + ' days' : 'Not specified'}</p>
                <p>${data.order_shipped ? 'Order has been successfully shipped.' : 'Order shipping failed. Please contact support.'}</p>
            `;
            }

            finalizeRequest();
        }

        // Show video modal on first visit
        document.addEventListener('DOMContentLoaded', function() {
            if (!localStorage.getItem('hasVisited')) {
                showVideoModal();
                localStorage.setItem('hasVisited', 'true');
            }
        });

        // Watch Tutorial button click handler
        document.getElementById('watchTutorialButton').addEventListener('click', showVideoModal);


        function formatCooldownTime(cooldownSeconds) {
            const hours = Math.floor(cooldownSeconds / 3600);
            const minutes = Math.floor((cooldownSeconds % 3600) / 60);
            const seconds = cooldownSeconds % 60;

            let formattedTime = '';
            if (hours > 0) formattedTime += `${hours} hour${hours > 1 ? 's' : ''} `;
            if (minutes > 0) formattedTime += `${minutes} minute${minutes > 1 ? 's' : ''} `;
            if (seconds > 0) formattedTime += `${seconds} second${seconds > 1 ? 's' : ''}`;

            return formattedTime.trim();
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                element.classList.add('text-green-600');
                setTimeout(() => {
                    element.classList.remove('text-green-600');
                }, 1000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        function handleErrorResponse(error) {
            loadingIndicator.classList.add('hidden');
            errorIndicator.classList.remove('hidden');
            modalTitle.textContent = 'Error';
            modalMessage.innerHTML = error.response?.data?.error || 'An error occurred';

            finalizeRequest();
        }

        function finalizeRequest() {
            const orderId = document.getElementById('orderId').value.trim();
            const requestKey = getRequestKey(orderId);
            ongoingRequests.delete(requestKey);
            isRequestOngoing = false;
            submitButton.disabled = false;
            checkStatusButton.disabled = true;
        }

        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        window.addEventListener('load', function () {
            var orderId = getUrlParameter('orderId');

            if (orderId) {
                document.getElementById('orderId').value = orderId;
            }

            // 如果两个参数都存在，可以选择自动提交表单
            if (orderId) {
                // 取消下面的注释如果你想自动提交表单
                // document.getElementById('authForm').submit();
            }
        });

        const tour = new Shepherd.Tour({
            defaultStepOptions: {
                useModalOverlay: true,
                cancelIcon: {
                    enabled: true
                },
                classes: 'shadow-md bg-purple-50',
                scrollTo: { behavior: 'smooth', block: 'center' },
                highlightClass: 'highlight-element',
                modalOverlayOpeningPadding: 10,
                modalOverlayOpeningRadius: 4
            }
        });

        tour.addStep({
            id: 'order-id',
            text: `
                <div class="mb-4">
                    <img src="https://cf.shopee.com.my/file/sg-11134209-7rdx0-m0qynirez5ve7a" alt="Shopee Order ID" class="w-full rounded-lg shadow-md">
                </div>
                <p>Enter your Shopee order ID here. You can find this in your Shopee order details.</p>
            `,
            attachTo: {
                element: '#orderId',
                on: 'bottom'
            },
            buttons: [
                {
                    text: 'Done',
                    action: tour.complete
                }
            ]
        });

        document.getElementById('helpButton').addEventListener('click', () => {
            tour.start();
        });
    </script>
</body>

</html>