# ShopeeAPI Docker Deployment Guide

This guide provides instructions for deploying ShopeeAPI using Docker.

## Quick Start

```bash
# Pull the image
docker pull limjianhui789/shopee-api:latest

# Create config directory and file
mkdir -p /path/to/config
mkdir -p /path/to/logs

# Create and edit your config.json file
nano /path/to/config.json

# Run the container
docker run -d \
  --name shopeeapi \
  -p 8000:8000 \
  -v /path/to/config.json:/app/config.json \
  -v /path/to/logs:/app/logs \
  --restart unless-stopped \
  -e PORT=8000 \
  -e ENVIRONMENT=production \
  limjianhui789/shopee-api:latest
```

## Required Configuration

### Port Configuration

ShopeeAPI requires the following port to be exposed:

- **8000**: Main API port (HTTP)

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| PORT | No | 8000 | The port on which the API will listen |
| ENVIRONMENT | No | production | Deployment environment (production/development) |
| LOG_LEVEL | No | info | Logging level (debug/info/warning/error) |

### Volume Mounts

ShopeeAPI requires the following volumes to be mounted:

1. **Configuration File**:
   - Container path: `/app/config.json`
   - Host path: Path to your configuration file on the host

2. **Logs Directory**:
   - Container path: `/app/logs`
   - Host path: Directory where you want to store logs

## Configuration File Format

Create a `config.json` file with the following structure:

```json
{
  "authorization_code": "your-shopee-authorization-code",
  "cookies": {
    "SPC_EC": "your-spc-ec-cookie",
    "SPC_U": "your-spc-u-cookie",
    "SPC_ST": "your-spc-st-cookie"
  },
  "webhook": {
    "enabled": false,
    "url": "https://your-webhook-url",
    "events": ["message_received", "message_sent"]
  },
  "cache": {
    "enabled": true,
    "max_messages": 100
  }
}
```

## Verifying Deployment

Test the API to ensure it's working correctly:

```bash
curl http://localhost:8000/status
```

## Using Docker Compose

You can also use Docker Compose for easier deployment:

```yaml
version: '3'

services:
  shopee-api:
    image: limjianhui789/shopee-api:latest
    container_name: shopeeapi
    ports:
      - "8000:8000"
    volumes:
      - ./config.json:/app/config.json
      - ./logs:/app/logs
    restart: unless-stopped
    environment:
      - PORT=8000
      - ENVIRONMENT=production
```

Save this as `docker-compose.yml` and run:

```bash
docker-compose up -d
```

## Troubleshooting

1. **Container fails to start**:
   - Check the container logs: `docker logs shopeeapi`
   - Verify the configuration file exists and has correct permissions
   - Ensure the ports are not already in use

2. **API returns authentication errors**:
   - Check your `config.json` file for correct credentials
   - Update the authorization code and cookies if they've expired

3. **Volume mount issues**:
   - Verify the paths on your host system exist
   - Check permissions on the host directories

4. **Permission denied errors when updating config**:
   - If you see errors like `[Errno 13] Permission denied: '/app/ShopeeAPI/config.json'` when updating credentials or configuration:
     1. The latest version includes automatic permission fixing, but if you're using an older version, you can fix it manually:
        ```bash
        # Get the container ID
        docker ps

        # Enter the container
        docker exec -it CONTAINER_ID bash

        # Fix permissions (as root)
        chown shopeeapi:shopeeapi /app/ShopeeAPI/config.json
        chmod 644 /app/ShopeeAPI/config.json
        ```
     2. Alternatively, ensure the config.json file has the correct permissions before mounting it:
        ```bash
        # On your host system
        touch /path/to/config.json
        chmod 666 /path/to/config.json  # Make it writable by anyone
        ```
     3. For production environments, consider using Docker secrets or environment variables instead of mounted config files

5. **ImportError: attempted relative import with no known parent package** or **ModuleNotFoundError: No module named 'ShopeeAPI'**:
   - These errors occur when the Python path is not set correctly or the application is not being run from the correct directory
   - The latest Docker image fixes this by:
     1. Setting the PYTHONPATH environment variable: `ENV PYTHONPATH=/app`
     2. Using a run script that ensures the correct working directory and Python path:
        ```bash
        #!/bin/bash
        export PYTHONPATH=/app
        cd /app
        exec uvicorn ShopeeAPI.api:app --host 0.0.0.0 --port $PORT
        ```
   - If you're building your own image, make sure to include these settings
   - If you're running the container with a custom command, use:
     ```bash
     docker run -e PYTHONPATH=/app -v /path/to/config.json:/app/ShopeeAPI/config.json -v /path/to/logs:/app/logs limjianhui789/shopee-api:latest /app/run.sh
     ```
   - For manual execution inside the container, use:
     ```bash
     export PYTHONPATH=/app
     cd /app
     uvicorn ShopeeAPI.api:app --host 0.0.0.0 --port 8000
     ```

## Updating the Container

When a new version is available:

```bash
# Pull the latest image
docker pull limjianhui789/shopee-api:latest

# Stop and remove the existing container
docker stop shopeeapi
docker rm shopeeapi

# Run the new container with the same settings
docker run -d \
  --name shopeeapi \
  -p 8000:8000 \
  -v /path/to/config.json:/app/config.json \
  -v /path/to/logs:/app/logs \
  --restart unless-stopped \
  -e PORT=8000 \
  -e ENVIRONMENT=production \
  limjianhui789/shopee-api:latest
```

Or with Docker Compose:

```bash
docker-compose pull
docker-compose up -d
```
