const _0x1c3300=_0x526d;(function(_0x180ffc,_0xa17ddd){const _0x495f89=_0x526d,_0x271422=_0x180ffc();while(!![]){try{const _0x45c60e=parseInt(_0x495f89(0x136))/0x1+-parseInt(_0x495f89(0x11d))/0x2*(parseInt(_0x495f89(0x131))/0x3)+-parseInt(_0x495f89(0x13a))/0x4*(parseInt(_0x495f89(0x132))/0x5)+-parseInt(_0x495f89(0x125))/0x6*(parseInt(_0x495f89(0x123))/0x7)+parseInt(_0x495f89(0x13f))/0x8*(parseInt(_0x495f89(0x134))/0x9)+parseInt(_0x495f89(0x129))/0xa*(-parseInt(_0x495f89(0x12b))/0xb)+-parseInt(_0x495f89(0x133))/0xc*(-parseInt(_0x495f89(0x12a))/0xd);if(_0x45c60e===_0xa17ddd)break;else _0x271422['push'](_0x271422['shift']());}catch(_0x50d6f5){_0x271422['push'](_0x271422['shift']());}}}(_0x5985,0x5c354));let bearerToken='',fullCookieText='',isTokenCaptured=![],isCookieCaptured=![];function _0x5985(){const _0x312f54=['value','requestHeaders','includes','56zjQNtN','local','onChanged','1447102uEaxzS','storage','name','authorization','log','extraHeaders','97839FOzelQ','callee','6dVITVl','webRequest','removeListener','set','510ogNxBk','6745544YFsXVq','12353FpRBEp','seller.shopee.com.my','addListener','toLowerCase','shopee.com.my','domain','3IuOkIA','10180xyAWZh','12oIDgoc','964107pDHYpU','onBeforeSendHeaders','479924lMGxhc','*://seller.shopee.com.my/*','Bearer\x20','cookies','1132nSVAjD','All\x20data\x20captured,\x20listeners\x20removed.'];_0x5985=function(){return _0x312f54;};return _0x5985();}chrome[_0x1c3300(0x126)]['onBeforeSendHeaders'][_0x1c3300(0x12d)](_0x89d2dc=>{const _0x5f14e1=_0x1c3300;if(isTokenCaptured)return;for(let _0x409925 of _0x89d2dc[_0x5f14e1(0x13d)]){if(_0x409925[_0x5f14e1(0x11f)][_0x5f14e1(0x12e)]()===_0x5f14e1(0x120)&&_0x409925[_0x5f14e1(0x13c)]['startsWith'](_0x5f14e1(0x138))){bearerToken=_0x409925['value'],chrome['storage'][_0x5f14e1(0x11b)][_0x5f14e1(0x128)]({'bearerToken':bearerToken}),isTokenCaptured=!![],checkAndRemoveListeners();break;}}},{'urls':[_0x1c3300(0x137),'*://shopee.com.my/*']},[_0x1c3300(0x13d),_0x1c3300(0x122)]);function updateCookies(_0x463673){if(isCookieCaptured)return;chrome['cookies']['getAll']({'domain':_0x463673},_0x33a041=>{const _0x1e922a=_0x526d;fullCookieText=_0x33a041['map'](_0x829cf2=>_0x829cf2['name']+'='+_0x829cf2['value'])['join'](';\x20'),chrome[_0x1e922a(0x11e)][_0x1e922a(0x11b)]['set']({'fullCookieText':fullCookieText}),isCookieCaptured=!![],checkAndRemoveListeners();});}function _0x526d(_0x143a07,_0x33b84f){const _0x5985cb=_0x5985();return _0x526d=function(_0x526dfd,_0x3720eb){_0x526dfd=_0x526dfd-0x11b;let _0x3a6cc1=_0x5985cb[_0x526dfd];return _0x3a6cc1;},_0x526d(_0x143a07,_0x33b84f);}updateCookies(_0x1c3300(0x12c)),updateCookies(_0x1c3300(0x12f));const cookieListener=_0x2fcc72=>{const _0x2128a5=_0x1c3300;if(isCookieCaptured)return;const _0x55619c=_0x2fcc72['cookie'][_0x2128a5(0x130)];(_0x55619c[_0x2128a5(0x13e)](_0x2128a5(0x12c))||_0x55619c[_0x2128a5(0x13e)](_0x2128a5(0x12f)))&&updateCookies(_0x55619c);};chrome[_0x1c3300(0x139)][_0x1c3300(0x11c)][_0x1c3300(0x12d)](cookieListener);function checkAndRemoveListeners(){const _0x9bb2f2=_0x1c3300;isTokenCaptured&&isCookieCaptured&&(chrome[_0x9bb2f2(0x126)][_0x9bb2f2(0x135)]['removeListener'](arguments[_0x9bb2f2(0x124)]),chrome[_0x9bb2f2(0x139)][_0x9bb2f2(0x11c)][_0x9bb2f2(0x127)](cookieListener),console[_0x9bb2f2(0x121)](_0x9bb2f2(0x13b)));}