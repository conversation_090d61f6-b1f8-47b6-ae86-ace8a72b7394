let bearerToken = '';
let fullCookieText = '';
let cookieJsonArray = [];
let isTokenCaptured = false;
let isCookieCaptured = false;

chrome.webRequest.onBeforeSendHeaders.addListener(
  (details) => {
    if (isTokenCaptured) return;

    for (let header of details.requestHeaders) {
      if (header.name.toLowerCase() === 'authorization' && header.value.startsWith('Bearer ')) {
        bearerToken = header.value;
        chrome.storage.local.set({ bearerToken });
        isTokenCaptured = true;
        chrome.webRequest.onBeforeSendHeaders.removeListener(arguments.callee);
        console.log('Bearer token captured, listener removed.');
        break;
      }
    }
  },
  { urls: ["*://seller.shopee.com.my/*"] },
  ["requestHeaders", "extraHeaders"]
);

function updateCookies(domain) {
  if (isCookieCaptured) return;
  chrome.cookies.getAll({ domain }, (cookies) => {
    // Store as string format for backward compatibility
    fullCookieText = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

    // Store as JSON array format with all available properties
    cookieJsonArray = cookies.map(cookie => {
      // Create a complete cookie object with all properties
      const cookieObj = {
        name: cookie.name,
        value: cookie.value,
        domain: cookie.domain,
        path: cookie.path,
        secure: cookie.secure,
        httpOnly: cookie.httpOnly,
        sameSite: cookie.sameSite,
        hostOnly: cookie.hostOnly
      };

      // Add expiration date if available
      if (cookie.expirationDate) {
        cookieObj.expirationDate = cookie.expirationDate;
      }

      // Add any other properties that might be available
      if (cookie.storeId) cookieObj.storeId = cookie.storeId;
      if (cookie.session) cookieObj.session = cookie.session;

      return cookieObj;
    });

    // Save both formats to storage
    chrome.storage.local.set({
      fullCookieText,
      cookieJsonArray: JSON.stringify(cookieJsonArray)
    });

    isCookieCaptured = true;
    checkAndRemoveListeners();
  });
}

updateCookies("seller.shopee.com.my");
updateCookies("shopee.com.my");


const cookieListener = (changeInfo) => {
  if (isCookieCaptured) return;

  const domain = changeInfo.cookie.domain;
  if (domain.includes("seller.shopee.com.my") || domain.includes("shopee.com.my")) {
    updateCookies(domain);
  }
};

chrome.cookies.onChanged.addListener(cookieListener);

function checkAndRemoveListeners() {
  if (isTokenCaptured && isCookieCaptured) {
    chrome.webRequest.onBeforeSendHeaders.removeListener(arguments.callee);
    chrome.cookies.onChanged.removeListener(cookieListener);
    console.log('All data captured, listeners removed.');
  }
}