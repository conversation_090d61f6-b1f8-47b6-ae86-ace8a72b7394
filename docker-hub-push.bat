@echo off
REM Docker Hub deployment script for ShopeeAPI
REM This script builds and pushes the ShopeeAPI Docker image to a private Docker Hub repository

REM Configuration
set DOCKER_HUB_USERNAME=limjianhui789
set DOCKER_HUB_REPO=shopee-api
set IMAGE_NAME=%DOCKER_HUB_USERNAME%/%DOCKER_HUB_REPO%

REM Check if Docker is running
docker info >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Docker is not running. Please start Docker Desktop and try again.
    exit /b 1
)

REM Parse command line arguments
set BUILD=false
set PUSH=false
set ALL=false

:parse_args
if "%~1"=="" goto check_args
if "%~1"=="--build" (
    set BUILD=true
    shift
    goto parse_args
)
if "%~1"=="--push" (
    set PUSH=true
    shift
    goto parse_args
)
if "%~1"=="--all" (
    set ALL=true
    shift
    goto parse_args
)
echo Unknown option: %~1
echo Usage: %0 [--build] [--push] [--all]
exit /b 1

:check_args
REM If no arguments are provided, show help
if "%BUILD%"=="false" if "%PUSH%"=="false" if "%ALL%"=="false" (
    echo Usage: %0 [--build] [--push] [--all]
    echo   --build: Build the Docker image
    echo   --push: Push the Docker image to Docker Hub
    echo   --all: Build and push the Docker image
    exit /b 0
)

REM Set version based on date and time
for /f "tokens=2 delims==" %%a in ('wmic os get localdatetime /value') do set datetime=%%a
set VERSION=%datetime:~0,4%.%datetime:~4,2%.%datetime:~6,2%.%datetime:~8,4%
echo Using version: %VERSION%

REM Build the Docker image
if "%BUILD%"=="true" goto build
if "%ALL%"=="true" goto build_all
goto check_push

:build_all
REM For --all flag, we need to build and then continue to push
set BUILD_SUCCESS=false
goto build

:after_build_all
REM After building with --all flag, continue to push if build was successful
if "%BUILD_SUCCESS%"=="true" goto check_push
echo Build failed, not proceeding to push.
exit /b 1

:build
echo Building Docker image...
REM Use absolute paths for Docker context to avoid issues
set "CURRENT_DIR=%CD%"

REM Check if Dockerfile exists
if not exist "%CURRENT_DIR%\ShopeeAPI\Dockerfile" (
    echo Error: Dockerfile not found at %CURRENT_DIR%\ShopeeAPI\Dockerfile
    echo Make sure you're running this script from the root of your project.
    if "%ALL%"=="true" goto after_build_all
    exit /b 1
)

echo Using Dockerfile at: %CURRENT_DIR%\ShopeeAPI\Dockerfile
docker build -t %IMAGE_NAME%:%VERSION% -t %IMAGE_NAME%:latest -f "%CURRENT_DIR%\ShopeeAPI\Dockerfile" "%CURRENT_DIR%"
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to build Docker image. Please check Docker Desktop and try again.
    if "%ALL%"=="true" goto after_build_all
    exit /b 1
)
echo Docker image built: %IMAGE_NAME%:%VERSION%
set BUILD_SUCCESS=true

REM If this was called from build_all, continue to push
if "%ALL%"=="true" goto after_build_all

:check_push
REM Push the Docker image to Docker Hub
if "%PUSH%"=="true" goto check_image_exists
if "%ALL%"=="true" goto check_image_exists
goto end

:check_image_exists
REM Check if the image exists locally before pushing
echo Checking if image exists locally...
docker image inspect %IMAGE_NAME%:%VERSION% >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Image %IMAGE_NAME%:%VERSION% does not exist locally. Build the image first.
    echo Run: %0 --build
    exit /b 1
)
goto push

:push
echo Logging in to Docker Hub...
docker login
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to log in to Docker Hub. Please check your credentials and try again.
    exit /b 1
)

echo Pushing Docker image to Docker Hub...
docker push %IMAGE_NAME%:%VERSION%
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to push image tag %VERSION%. Please check Docker Desktop connection and try again.
    exit /b 1
)

docker push %IMAGE_NAME%:latest
if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to push latest tag. Please check Docker Desktop connection and try again.
    exit /b 1
)

echo Docker image successfully pushed to Docker Hub: %IMAGE_NAME%:%VERSION%

:end
echo Process completed!
