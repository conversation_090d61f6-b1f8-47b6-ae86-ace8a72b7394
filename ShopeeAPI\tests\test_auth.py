"""
Tests for the authentication functionality of the ShopeeAPI.
"""
import unittest
import requests
import json
import sys
import os
import time
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from ShopeeAPI.client import ShopeeAPI
except ImportError:
    from client import ShopeeAPI


class TestAuthAPI(unittest.TestCase):
    """Test cases for the authentication API endpoints."""
    
    API_URL = "http://localhost:8000"
    
    def setUp(self):
        """Set up test environment."""
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        # Test credentials with timestamp to ensure uniqueness
        self.test_credentials = {
            "authorization_code": f"test_auth_code_{int(time.time())}",
            "cookie": f"test_cookie_{int(time.time())}"
        }
    
    def test_auth_endpoint(self):
        """Test the /auth endpoint and verify config.json is updated."""
        # Skip this test if the API server is not running
        try:
            requests.get(f"{self.API_URL}/status", timeout=1)
        except requests.RequestException:
            self.skipTest("API server is not running")
            
        # Call the /auth endpoint
        response = requests.post(
            f"{self.API_URL}/auth", 
            headers=self.headers, 
            json=self.test_credentials
        )
        
        # Check if the request was successful
        self.assertEqual(response.status_code, 200)
        
        # Verify config.json was updated
        config_path = Path("ShopeeAPI/config.json")
        if config_path.exists():
            with open(config_path, "r") as f:
                config_data = json.load(f)
                
            self.assertEqual(config_data.get("AUTHORIZATION_CODE"), self.test_credentials["authorization_code"])
            self.assertEqual(config_data.get("COOKIE"), self.test_credentials["cookie"])
    
    def test_config_reload(self):
        """Test that changes to config.json are detected and applied."""
        # Skip this test if the API server is not running
        try:
            requests.get(f"{self.API_URL}/status", timeout=1)
        except requests.RequestException:
            self.skipTest("API server is not running")
            
        # Get current time for unique values
        timestamp = int(time.time())
        
        # Update config.json directly
        config_path = Path("ShopeeAPI/config.json")
        if not config_path.exists():
            self.skipTest("Config file not found")
            
        with open(config_path, "r") as f:
            config_data = json.load(f)
        
        # Save original values to restore later
        original_auth = config_data.get("AUTHORIZATION_CODE", "")
        original_cookie = config_data.get("COOKIE", "")
        
        try:
            # Update values
            config_data["AUTHORIZATION_CODE"] = f"direct_update_auth_{timestamp}"
            config_data["COOKIE"] = f"direct_update_cookie_{timestamp}"
            
            with open(config_path, "w") as f:
                json.dump(config_data, f, indent=2)
            
            # Wait for the file watcher to detect changes
            time.sleep(2)
            
            # Make a request to verify the changes were applied
            response = requests.get(f"{self.API_URL}/orders/to_ship")
            
            # Check if the request was successful
            self.assertEqual(response.status_code, 200)
        finally:
            # Restore original values
            config_data["AUTHORIZATION_CODE"] = original_auth
            config_data["COOKIE"] = original_cookie
            
            with open(config_path, "w") as f:
                json.dump(config_data, f, indent=2)


class TestAuthClient(unittest.TestCase):
    """Test cases for the authentication functionality in the ShopeeAPI client."""
    
    def setUp(self):
        """Set up test environment."""
        # Test credentials
        self.authorization_code = "Bearer test_auth_code"
        self.cookie = "test_cookie"
    
    def test_update_credentials(self):
        """Test updating credentials in the ShopeeAPI client."""
        # Create a real ShopeeAPI instance with empty credentials
        api = ShopeeAPI()
        
        # Update credentials
        is_valid = api.update_credentials(self.authorization_code, self.cookie)
        
        # Assertions
        self.assertTrue(is_valid)
        self.assertEqual(api.credential_manager.authorization_code, self.authorization_code)
        self.assertEqual(api.credential_manager.cookie, self.cookie)


if __name__ == "__main__":
    unittest.main()
