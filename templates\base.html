<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Panel{% endblock %}</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <script src="//unpkg.com/alpinejs" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .nav-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
    </style>
    {% block extra_head %}{% endblock %}
</head>

<body class="bg-gray-100 min-h-screen flex">
    <!-- Sidebar -->
    <nav
        class="gradient-bg text-white w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition duration-200 ease-in-out">
        <ul class="space-y-2">
            <li>
                <a href="{{ url_for('admin.dashboard') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
            </li>


            <!-- Stock Management -->
            <li>
                <a href="{{ url_for('admin.stock') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-boxes mr-2"></i>Stock Management
                </a>
            </li>

            <li>
                <a href="{{ url_for('admin.inventory') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-boxes mr-2"></i>Shipment Record
                </a>
            </li>

            <!-- Steam Category -->
            <li x-data="{ open: false }">
                <button @click="open = !open"
                    class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                    <span><i class="fab fa-steam mr-2"></i>Steam</span>
                    <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                </button>
                <ul x-show="open" class="pl-4 space-y-2 mt-2">
                    <li>
                        <a href="{{ url_for('admin.steam_settings') }}"
                            class="block py-2 px-4 rounded transition duration-200 nav-item">
                            <i class="fas fa-cog mr-2"></i>Settings
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('admin.credentials') }}"
                            class="block py-2 px-4 rounded transition duration-200 nav-item">
                            <i class="fas fa-key mr-2"></i>Credentials
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Netflix Category -->
            <li x-data="{ open: false }">
                <button @click="open = !open"
                    class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                    <span><i class="fas fa-film mr-2"></i>Netflix</span>
                    <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                </button>
                <ul x-show="open" class="pl-4 space-y-2 mt-2">
                    <li>
                        <a href="{{ url_for('admin.netflix_accounts') }}"
                            class="block py-2 px-4 rounded transition duration-200 nav-item">
                            <i class="fas fa-user-circle mr-2"></i>Accounts
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('admin.netflix_settings') }}"
                            class="block py-2 px-4 rounded transition duration-200 nav-item">
                            <i class="fas fa-cog mr-2"></i>Settings
                        </a>
                    </li>
                </ul>
            </li>
            

            <!-- Auto Chat -->
            <li>
                <a href="{{ url_for('admin.auto_chat') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-comments mr-2"></i>Auto Chat
                </a>
            </li>
            <li>
                <a href="{{ url_for('admin.auto_reply') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-reply-all mr-2"></i>Auto Reply
                </a>
            </li>
            <li>
                <a href="{{ url_for('admin.system_config') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-cog mr-2"></i>System Config
                </a>
            </li>

            <!-- VPN Category -->
            <li x-data="{ open: false }">
                <button @click="open = !open"
                    class="w-full flex items-center justify-between py-2.5 px-4 rounded transition duration-200 nav-item">
                    <span><i class="fas fa-shield-alt mr-2"></i>VPN</span>
                    <i class="fas fa-chevron-down" x-bind:class="{ 'transform rotate-180': open }"></i>
                </button>
                <ul x-show="open" class="pl-4 space-y-2 mt-2">
                    <li>
                        <a href="{{ url_for('vpn.vpn_clients_page') }}"
                            class="block py-2 px-4 rounded transition duration-200 nav-item">
                            <i class="fas fa-users mr-2"></i>Users
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('vpn.vpn_servers_page') }}"
                            class="block py-2 px-4 rounded transition duration-200 nav-item">
                            <i class="fas fa-server mr-2"></i>Servers
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('vpn.vpn_inbounds_page') }}"
                            class="block py-2 px-4 rounded transition duration-200 nav-item">
                            <i class="fas fa-network-wired mr-2"></i>Inbounds
                        </a>
                    </li>
                </ul>
            </li>

            <li x-data="{ showInvoiceMenu: false }" x-init="
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        showInvoiceMenu = !!(data.CURLEC_API_KEY && data.CURLEC_SECRET_KEY);
                    })">
                <template x-if="showInvoiceMenu">
                    <a href="{{ url_for('admin.manual_invoice') }}"
                        class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                        <i class="fas fa-file-invoice mr-2"></i>Manual Invoice
                    </a>
                </template>
            </li>
            <li>
                <a href="{{ url_for('admin.canva_manage') }}"
                    class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-palette mr-2"></i>Canva Manage
                </a>
            </li>
            <li>
                <a href="{{ url_for('admin.ai_chat') }}" class="block py-2.5 px-4 rounded transition duration-200 nav-item">
                    <i class="fas fa-comments mr-2"></i>AI Chat Settings
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <header class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900">
                    {% block header %}{% endblock %}
                </h1>
                <button class="md:hidden gradient-bg text-white px-3 py-2 rounded-md text-sm font-medium"
                    onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </header>
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200">
            <div class="container mx-auto px-6 py-8">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('nav');
            sidebar.classList.toggle('-translate-x-full');
        }
    </script>
    {% block extra_scripts %}{% endblock %}
</body>

</html>