{% extends "base.html" %}

{% block title %}Auto Chat Settings{% endblock %}
{% block header %}Auto Chat Settings{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="autoChatConfigData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Main content -->
        <div class="flex-1">
            <div class="section-content">
                <h2 class="text-2xl font-bold mb-4">Auto Chat VAR_SKUs</h2>
                <div class="mb-4 config-item">
                    <div class="mt-2">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        VAR_SKU
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Custom Message
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(sku, index) in config.AUTO_REDEEM_VAR_SKUS" :key="index">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" 
                                                   x-model="sku.sku"
                                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <textarea x-model="sku.message"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                rows="3" 
                                                placeholder="Leave blank to use global message"></textarea>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click.prevent="removeVarSKU(index)"
                                                class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <button @click.prevent="addVarSKU"
                            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add VAR_SKU
                        </button>
                    </div>
                </div>

                <div class="mb-4 config-item">
                    <label for="auto_redeem_message" class="block text-sm font-medium text-gray-700">
                        Global Auto Chat Message
                    </label>
                    <textarea id="auto_redeem_message" 
                            name="auto_redeem_message" 
                            x-model="config.AUTO_REDEEM_MESSAGE"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            rows="10" 
                            style="white-space: pre-wrap;"></textarea>
                    
                    <div class="mt-2 text-sm text-gray-600">
                        <p>Available variables for message customization:</p>
                        <ul class="list-disc pl-5 mt-2">
                            <li>{order_sn} - Order serial number</li>
                            <li>{buyer_username} - Buyer's username</li>
                            <li>{item_name} - Name of the item</li>
                            <li>{item_price} - Price of the item</li>
                            <li>{buyer_name} - Buyer's name</li>
                            <li>{buyer_phone} - Buyer's phone number</li>
                            <li>{create_time} - Order creation time</li>
                            <li>{shipping_address} - Shipping address</li>
                            <li>{item_sku} - Item SKU</li>
                            <li>{item_quantity} - Quantity of items</li>
                            <li>{payment_method} - Payment method</li>
                            <li>{shop_name} - Shop name</li>
                            <li>{escrow_release_time} - Escrow release time</li>
                            <li>{buyer_rating} - Buyer's rating</li>
                            <li>{order_status} - Order status</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function autoChatConfigData() {
        return {
            config: {},
            isLoaded: false,
            init() {
                this.loadConfig();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        if (!this.config.AUTO_REDEEM_VAR_SKUS) {
                            this.config.AUTO_REDEEM_VAR_SKUS = [];
                        }
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            addVarSKU() {
                if (!this.config.AUTO_REDEEM_VAR_SKUS) {
                    this.config.AUTO_REDEEM_VAR_SKUS = [];
                }
                this.config.AUTO_REDEEM_VAR_SKUS.push({ sku: '', message: '' });
            },
            removeVarSKU(index) {
                this.config.AUTO_REDEEM_VAR_SKUS.splice(index, 1);
            },
            saveConfig() {
                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    this.animateSaveButton();
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the configuration.');
                });
            },
            animateInitialLoad() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}