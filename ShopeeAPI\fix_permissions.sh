#!/bin/bash
# Script to fix permissions for config.json in Docker container

# Check if running as root
if [ "$(id -u)" -ne 0 ]; then
    echo "This script must be run as root"
    exit 1
fi

# Set proper permissions for config.json
CONFIG_PATH="/app/ShopeeAPI/config.json"
if [ -f "$CONFIG_PATH" ]; then
    echo "Setting permissions for $CONFIG_PATH"
    chown shopeeapi:shopeeapi "$CONFIG_PATH"
    chmod 644 "$CONFIG_PATH"
    echo "Permissions set successfully"
else
    echo "Config file not found at $CONFIG_PATH"
    
    # Check if it's mounted at /app/config.json instead
    ALT_CONFIG_PATH="/app/config.json"
    if [ -f "$ALT_CONFIG_PATH" ]; then
        echo "Found config at $ALT_CONFIG_PATH"
        chown shopeeapi:shopeeapi "$ALT_CONFIG_PATH"
        chmod 644 "$ALT_CONFIG_PATH"
        echo "Permissions set successfully"
    else
        echo "No config file found"
    fi
fi

# Set permissions for logs directory
LOGS_PATH="/app/logs"
if [ -d "$LOGS_PATH" ]; then
    echo "Setting permissions for $LOGS_PATH"
    chown -R shopeeapi:shopeeapi "$LOGS_PATH"
    chmod -R 755 "$LOGS_PATH"
    echo "Log directory permissions set successfully"
fi

echo "Permission fix completed"
