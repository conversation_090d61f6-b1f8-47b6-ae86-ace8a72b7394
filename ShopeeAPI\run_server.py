#!/usr/bin/env python3
"""
Simple script to run the ShopeeAPI server.
"""

import sys
import os

# Add the parent directory to the path so we can import ShopeeAPI modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Now import and run the API
if __name__ == "__main__":
    import uvicorn
    from ShopeeAPI.api import app
    
    print("Starting ShopeeAPI server on port 456...")
    uvicorn.run(app, host="0.0.0.0", port=456)
