{% extends "base.html" %}

{% block title %}Credentials Management{% endblock %}

{% block header %}Credentials Management{% endblock %}

{% block content %}
<div class="md:grid md:grid-cols-2 md:gap-6">
    <div class="mt-5 md:mt-0 md:col-span-1 relative">
        <div class="shadow sm:rounded-md sm:overflow-hidden">
            <div class="px-4 py-5 bg-white space-y-6 sm:p-6">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Email Configurations</h3>
                <div id="emailConfigTable" class="overflow-x-auto">
                    <!-- Email configurations will be dynamically added here -->
                </div>
                <button id="addEmailButton" onclick="showAddEmailModal()"
                    class="mt-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out transform hover:scale-105 relative overflow-hidden transform">
                    <span id="emailButtonText">Add Email Config</span>
                    <span id="emailCircle"
                        class="absolute inset-0 rounded-full bg-indigo-700 opacity-0 transform scale-0 z-10"></span>
                </button>
            </div>
        </div>
    </div>

    <div class="mt-5 md:mt-0 md:col-span-1 relative">
        <div class="shadow sm:rounded-md sm:overflow-hidden">
            <div class="px-4 py-5 bg-white space-y-6 sm:p-6">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Steam Credentials</h3>
                <div id="steamCredentialTable" class="overflow-x-auto">
                    <!-- Steam credentials will be dynamically added here -->
                </div>
                <button id="addSteamButton" onclick="showAddSteamModal()"
                    class="mt-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out transform hover:scale-105 relative overflow-hidden transform">
                    <span id="steamButtonText">Add Steam Credential</span>
                    <span id="steamCircle"
                        class="absolute inset-0 rounded-full bg-indigo-700 opacity-0 transform scale-0 z-10"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Email Modal -->
<div id="addEmailModal" class="fixed z-20 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div
            class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="emailModalTitle">
                    Add Email Configuration
                </h3>
                <div class="mt-2">
                    <input type="text" id="emailKey" placeholder="Steam Username"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <input type="text" id="email" placeholder="Email"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <input type="password" id="appPassword" placeholder="App Password"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="emailModalAction" onclick="addEmailConfig()"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm transition duration-300 ease-in-out transform hover:scale-105">
                    Add
                </button>
                <button type="button" onclick="hideAddEmailModal()"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition duration-300 ease-in-out transform hover:scale-95">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Steam Modal -->
<div id="addSteamModal" class="fixed z-20 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div
            class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="steamModalTitle">
                    Add Steam Credential
                </h3>
                <div class="mt-2">
                    <input type="text" id="steamKey" placeholder="Steam Username"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <input type="password" id="steamPassword" placeholder="Password"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="steamModalAction" onclick="addSteamCredential()"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm transition duration-300 ease-in-out transform hover:scale-105">
                    Add
                </button>
                <button type="button" onclick="hideAddSteamModal()"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition duration-300 ease-in-out transform hover:scale-95">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Include Anime.js via CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    let config = {};

    function renderEmailConfigs(emailConfigs) {
        const table = document.getElementById('emailConfigTable');
        table.innerHTML = `
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Steam Username</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                ${Object.entries(emailConfigs).map(([key, config]) => `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${key}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${config.email}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editEmailConfig('${key}')" class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                            <button onclick="deleteEmailConfig('${key}')" class="text-red-600 hover:text-red-900">Delete</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    }

    function renderSteamCredentials(steamCredentials) {
        const table = document.getElementById('steamCredentialTable');
        table.innerHTML = `
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Steam Username</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                ${Object.entries(steamCredentials).map(([key, credential]) => `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${key}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editSteamCredential('${key}')" class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                            <button onclick="deleteSteamCredential('${key}')" class="text-red-600 hover:text-red-900">Delete</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    }

    function animateTableRows(tableId) {
        anime({
            targets: `#${tableId} tbody tr`,
            translateY: [50, 0],
            opacity: [0, 1],
            delay: anime.stagger(100),
            duration: 500,
            easing: 'easeOutExpo'
        });
    }

    function animateModal(modalId, isShowing, callback) {
        const modal = document.getElementById(modalId);
        if (isShowing) {
            anime({
                targets: modal,
                opacity: [0, 1],
                scale: [0.9, 1],
                duration: 300,
                easing: 'easeInOutSine',
                complete: () => {
                    if (callback) callback();
                }
            });
        } else {
            anime({
                targets: modal,
                opacity: [1, 0],
                scale: [1, 0.9],
                duration: 300,
                easing: 'easeInOutSine',
                complete: () => {
                    if (!isShowing) {
                        modal.classList.add('hidden');
                        if (callback) callback();
                    }
                }
            });
        }
    }

    function showAddEmailModal() {
        const button = document.getElementById('addEmailButton');
        const circle = document.getElementById('emailCircle');
        const buttonText = document.getElementById('emailButtonText');
        const rect = button.getBoundingClientRect();
        const diameter = Math.max(window.innerWidth, window.innerHeight) * 2;

        circle.style.width = `${diameter}px`;
        circle.style.height = `${diameter}px`;
        circle.style.left = '50%';
        circle.style.top = '50%';
        circle.style.transform = 'translate(-50%, -50%) scale(0)';
        circle.style.opacity = 1;

        buttonText.style.opacity = 0;

        anime({
            targets: circle,
            scale: [0, 1],
            duration: 600,
            easing: 'easeOutExpo',
            complete: () => {
                document.getElementById('emailModalTitle').textContent = 'Add Email Configuration';
                document.getElementById('emailModalAction').textContent = 'Add';
                document.getElementById('emailModalAction').onclick = addEmailConfig;
                document.getElementById('emailKey').value = '';
                document.getElementById('email').value = '';
                document.getElementById('appPassword').value = '';
                // Fade in form fields
                anime({
                    targets: '#addEmailModal .bg-white > div',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    duration: 300,
                    easing: 'easeOutQuad'
                });
                modalShow('addEmailModal');
            }
        });
    }

    function hideAddEmailModal() {
        const circle = document.getElementById('emailCircle');
        const buttonText = document.getElementById('emailButtonText');

        // Fade out form fields
        anime({
            targets: '#addEmailModal .bg-white > div',
            opacity: [1, 0],
            translateY: [0, 20],
            duration: 300,
            easing: 'easeInQuad',
            complete: () => {
                animateModal('addEmailModal', false, () => {
                    // Reset the button
                    anime({
                        targets: circle,
                        scale: [1, 0],
                        opacity: [1, 0],
                        duration: 300,
                        easing: 'easeInQuad',
                        complete: () => {
                            buttonText.style.opacity = 1;
                        }
                    });
                });
            }
        });
    }
    function showAddSteamModal() {
        const button = document.getElementById('addSteamButton');
        const circle = document.getElementById('steamCircle');
        const buttonText = document.getElementById('steamButtonText');
        const rect = button.getBoundingClientRect();
        const diameter = Math.max(window.innerWidth, window.innerHeight) * 2;

        circle.style.width = `${diameter}px`;
        circle.style.height = `${diameter}px`;
        circle.style.left = '50%';
        circle.style.top = '50%';
        circle.style.transform = 'translate(-50%, -50%) scale(0)';
        circle.style.opacity = 1;

        buttonText.style.opacity = 0;

        anime({
            targets: circle,
            scale: [0, 1],
            duration: 600,
            easing: 'easeOutExpo',
            complete: () => {
                document.getElementById('steamModalTitle').textContent = 'Add Steam Credential';
                document.getElementById('steamModalAction').textContent = 'Add';
                document.getElementById('steamModalAction').onclick = addSteamCredential;
                document.getElementById('steamKey').value = '';
                document.getElementById('steamPassword').value = '';
                // Fade in form fields
                anime({
                    targets: '#addSteamModal .bg-white > div',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    duration: 300,
                    easing: 'easeOutQuad'
                });
                modalShow('addSteamModal');
            }
        });
    }

    function hideAddSteamModal() {
        const circle = document.getElementById('steamCircle');
        const buttonText = document.getElementById('steamButtonText');

        // Fade out form fields
        anime({
            targets: '#addSteamModal .bg-white > div',
            opacity: [1, 0],
            translateY: [0, 20],
            duration: 300,
            easing: 'easeInQuad',
            complete: () => {
                animateModal('addSteamModal', false, () => {
                    // Reset the button
                    anime({
                        targets: circle,
                        scale: [1, 0],
                        opacity: [1, 0],
                        duration: 300,
                        easing: 'easeInQuad',
                        complete: () => {
                            buttonText.style.opacity = 1;
                        }
                    });
                });
            }
        });
    }

    function modalShow(modalId) {
        const modal = document.getElementById(modalId);
        modal.classList.remove('hidden');
        animateModal(modalId, true);
    }

    function addEmailConfig() {
        const key = document.getElementById('emailKey').value;
        const email = document.getElementById('email').value;
        const appPassword = document.getElementById('appPassword').value;

        axios.post('/admin/add_email_config', { key, email, app_password: appPassword })
            .then(response => {
                loadConfig();
                hideAddEmailModal();
                // New configuration diffusion effect
                anime({
                    targets: '#emailConfigTable tbody tr:last-child',
                    translateY: [50, 0],
                    opacity: [0, 1],
                    duration: 500,
                    easing: 'easeOutExpo'
                });
            })
            .catch(error => {
                console.error('Error adding email config:', error);
                alert('Error adding email configuration');
            });
    }

    function addSteamCredential() {
        const key = document.getElementById('steamKey').value;
        const password = document.getElementById('steamPassword').value;

        axios.post('/admin/add_steam_credential', { key, password })
            .then(response => {
                loadConfig();
                hideAddSteamModal();
                // New credential diffusion effect
                anime({
                    targets: '#steamCredentialTable tbody tr:last-child',
                    translateY: [50, 0],
                    opacity: [0, 1],
                    duration: 500,
                    easing: 'easeOutExpo'
                });
            })
            .catch(error => {
                console.error('Error adding steam credential:', error);
                alert('Error adding steam credential');
            });
    }

    function editEmailConfig(key) {
        const emailConfig = config.EMAIL_CONFIGS[key];
        document.getElementById('emailKey').value = key;
        document.getElementById('email').value = emailConfig.email;
        document.getElementById('appPassword').value = emailConfig.app_password;
        document.getElementById('emailModalTitle').textContent = 'Edit Email Configuration';
        document.getElementById('emailModalAction').textContent = 'Update';
        document.getElementById('emailModalAction').onclick = () => updateEmailConfig(key);

        // Reset opacity and transform of form fields
        anime.set('#addEmailModal .bg-white > div', {
            opacity: 0,
            translateY: 20
        });

        // Show modal and animate form fields
        modalShow('addEmailModal');
        anime({
            targets: '#addEmailModal .bg-white > div',
            opacity: [0, 1],
            translateY: [20, 0],
            duration: 300,
            easing: 'easeOutQuad'
        });
    }

    function editSteamCredential(key) {
        const steamCredential = config.STEAM_CREDENTIALS[key];
        document.getElementById('steamKey').value = key;
        document.getElementById('steamPassword').value = steamCredential.password;
        document.getElementById('steamModalTitle').textContent = 'Edit Steam Credential';
        document.getElementById('steamModalAction').textContent = 'Update';
        document.getElementById('steamModalAction').onclick = () => updateSteamCredential(key);

        // Reset opacity and transform of form fields
        anime.set('#addSteamModal .bg-white > div', {
            opacity: 0,
            translateY: 20
        });

        // Show modal and animate form fields
        modalShow('addSteamModal');
        anime({
            targets: '#addSteamModal .bg-white > div',
            opacity: [0, 1],
            translateY: [20, 0],
            duration: 300,
            easing: 'easeOutQuad'
        });
    }

    function updateEmailConfig(key) {
        const email = document.getElementById('email').value;
        const appPassword = document.getElementById('appPassword').value;

        axios.post('/admin/update_email_config', { key, email, app_password: appPassword })
            .then(response => {
                loadConfig();
                hideAddEmailModal();
            })
            .catch(error => {
                console.error('Error updating email config:', error);
                alert('Error updating email configuration');
            });
    }

    function updateSteamCredential(key) {
        const password = document.getElementById('steamPassword').value;

        axios.post('/admin/update_steam_credential', { key, password })
            .then(response => {
                loadConfig();
                hideAddSteamModal();
            })
            .catch(error => {
                console.error('Error updating steam credential:', error);
                alert('Error updating steam credential');
            });
    }

    function deleteEmailConfig(key) {
        if (confirm(`Are you sure you want to delete the email configuration for ${key}?`)) {
            axios.post('/admin/delete_email_config', { key })
                .then(response => {
                    loadConfig();
                })
                .catch(error => {
                    console.error('Error deleting email config:', error);
                    alert('Error deleting email configuration');
                });
        }
    }

    function deleteSteamCredential(key) {
        if (confirm(`Are you sure you want to delete the steam credential for ${key}?`)) {
            axios.post('/admin/delete_steam_credential', { key })
                .then(response => {
                    loadConfig();
                })
                .catch(error => {
                    console.error('Error deleting steam credential:', error);
                    alert('Error deleting steam credential');
                });
        }
    }

    function loadConfig() {
        axios.get('/admin/get_config')
            .then(response => {
                config = response.data;
                renderEmailConfigs(config.EMAIL_CONFIGS);
                renderSteamCredentials(config.STEAM_CREDENTIALS);
                animateTableRows('emailConfigTable');
                animateTableRows('steamCredentialTable');
            })
            .catch(error => {
                console.error('Error loading config:', error);
                alert('Error loading configuration');
            });
    }

    loadConfig();
</script>
{% endblock %}