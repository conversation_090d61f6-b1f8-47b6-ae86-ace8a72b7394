#!/usr/bin/env python3
"""
Test the fix for syahmialfabet username issue.
"""

import requests
import json


def test_send_message_fix():
    """Test the send_message fix for syahmialfabet."""
    print("=== Testing Send Message Fix ===")
    
    # Test syahmialfabet
    payload = {
        "text": "Test message for syahmialfabet after fix",
        "username": "syahmialfabet",
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    
    print(f"Testing syahmialfabet...")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:456/chat/send_message", 
            json=payload, 
            timeout=30
        )
        print(f"Response status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: syahmialfabet message sent successfully!")
            return True
        else:
            print("❌ FAILED: syahmialfabet message failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False


def main():
    """Main test function."""
    print("=== Testing ShopeeAPI Send Message Fix ===")
    
    # Test the fix
    success = test_send_message_fix()
    
    if success:
        print("\n🎉 FIX SUCCESSFUL! The syahmialfabet username issue has been resolved.")
    else:
        print("\n❌ FIX FAILED! The issue still exists.")


if __name__ == "__main__":
    main()
