<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redeeming Canva Invitation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .neumorphic {
            background: #f0f4f8;
            box-shadow: 
                8px 8px 16px rgba(174, 174, 192, 0.4),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
            border-radius: 1rem;
        }
        .loading-wave {
            width: 3px;
            height: 40px;
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
            margin: 5px;
            animation: wave 1s linear infinite;
            border-radius: 20px;
            display: inline-block;
        }
        .loading-wave:nth-child(2) { animation-delay: 0.1s; }
        .loading-wave:nth-child(3) { animation-delay: 0.2s; }
        .loading-wave:nth-child(4) { animation-delay: 0.3s; }
        .loading-wave:nth-child(5) { animation-delay: 0.4s; }
        @keyframes wave {
            0% { transform: scale(0); }
            50% { transform: scale(1); }
            100% { transform: scale(0); }
        }
        .loading-container {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-indigo-50 to-purple-50 min-h-screen flex items-center justify-center p-4">
    <div class="neumorphic p-8 max-w-md w-full">
        <div class="text-center">
            <h1 class="text-2xl font-bold text-gray-800 mb-4">Preparing Your Canva Access</h1>
            <div class="flex justify-center items-center my-8">
                <div class="loading-container">
                    <div class="loading-wave"></div>
                    <div class="loading-wave"></div>
                    <div class="loading-wave"></div>
                    <div class="loading-wave"></div>
                    <div class="loading-wave"></div>
                </div>
            </div>
            <p class="text-gray-600 mb-6">Please wait while we set up your Canva account...</p>
            <div class="space-y-4">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <p class="text-sm text-gray-500">For security reasons, this link can only be used once.</p>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <p class="text-sm text-gray-500">Do not share or refresh this page.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 防止返回
        history.pushState(null, null, document.URL);
        window.addEventListener('popstate', function () {
            history.pushState(null, null, document.URL);
        });

        // 防止右键和复制
        document.addEventListener('contextmenu', e => e.preventDefault());
        document.addEventListener('copy', e => e.preventDefault());
        
        // 使用临时令牌获取重定向URL
        setTimeout(async () => {
            try {
                const response = await fetch('/api/canva/get_redirect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: '{{ temp_token }}'
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to get redirect URL');
                }

                const data = await response.json();
                if (data.url) {
                    window.location.href = data.url;
                } else {
                    throw new Error('No redirect URL received');
                }
            } catch (error) {
                console.error('Error:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4';
                errorDiv.innerHTML = `
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"> Failed to process your request. Please try again later.</span>
                `;
                document.querySelector('.neumorphic').appendChild(errorDiv);
            }
        }, 2000);

        function isShopeeWebView() {
            const userAgent = navigator.userAgent;
            console.log('Current User Agent:', userAgent);  // 输出完整的 User-Agent
            
            const userAgentLower = userAgent.toLowerCase();
            console.log('Lowercase User Agent:', userAgentLower);  // 输出小写的 User-Agent
            
            const isShopee = userAgentLower.includes('shopee');
            console.log('Is Shopee WebView:', isShopee);  // 输出检测结果
            
            return isShopee;
        }
    </script>
</body>
</html>
