"""
Test script for ShopeeAPI hot reload functionality.

NOTE: This functionality has been integrated into the proper test suite in ShopeeAPI/tests/test_auth.py.
This standalone script is kept for backward compatibility and quick testing.
"""
import os
import json
import time
import requests
from pathlib import Path

# Define the API URL
API_URL = "http://localhost:8000"

def test_auth_endpoint():
    """Test the /auth endpoint and verify config.json is updated."""
    # Test credentials
    test_credentials = {
        "authorization_code": "test_auth_code_" + str(int(time.time())),
        "cookie": "test_cookie_" + str(int(time.time()))
    }

    # Call the /auth endpoint
    print(f"Sending credentials to /auth endpoint: {test_credentials}")
    response = requests.post(f"{API_URL}/auth", json=test_credentials)

    if response.status_code == 200:
        print(f"Auth endpoint response: {response.json()}")

        # Verify config.json was updated
        config_path = Path("ShopeeAPI/config.json")
        if config_path.exists():
            with open(config_path, "r") as f:
                config_data = json.load(f)

            if (config_data.get("AUTHORIZATION_CODE") == test_credentials["authorization_code"] and
                config_data.get("COOKIE") == test_credentials["cookie"]):
                print("✅ Config.json was updated successfully!")
            else:
                print("❌ Config.json was not updated correctly!")
                print(f"Expected: {test_credentials}")
                print(f"Actual: {config_data}")
        else:
            print(f"❌ Config file not found at {config_path}")
    else:
        print(f"❌ Auth endpoint failed with status {response.status_code}: {response.text}")

def test_config_reload():
    """Test that changes to config.json are detected and applied."""
    # Get current time for unique values
    timestamp = int(time.time())

    # Update config.json directly
    config_path = Path("ShopeeAPI/config.json")
    if config_path.exists():
        with open(config_path, "r") as f:
            config_data = json.load(f)

        # Save original values to restore later
        original_auth = config_data.get("AUTHORIZATION_CODE", "")
        original_cookie = config_data.get("COOKIE", "")

        try:
            # Update values
            config_data["AUTHORIZATION_CODE"] = f"direct_update_auth_{timestamp}"
            config_data["COOKIE"] = f"direct_update_cookie_{timestamp}"

            with open(config_path, "w") as f:
                json.dump(config_data, f, indent=2)

            print(f"Updated config.json directly: {config_data}")

            # Wait for the file watcher to detect changes
            print("Waiting for file watcher to detect changes...")
            time.sleep(2)

            # Make a request to verify the changes were applied
            response = requests.get(f"{API_URL}/orders/to_ship")
            print(f"Made request to /orders/to_ship, status: {response.status_code}")

            # There's no direct way to verify the API instance was updated from the response,
            # but we can check if the request was successful
            if response.status_code == 200:
                print("✅ Request successful after config update")
            else:
                print(f"❌ Request failed after config update: {response.status_code} - {response.text}")
        finally:
            # Restore original values
            config_data["AUTHORIZATION_CODE"] = original_auth
            config_data["COOKIE"] = original_cookie

            with open(config_path, "w") as f:
                json.dump(config_data, f, indent=2)
    else:
        print(f"❌ Config file not found at {config_path}")

if __name__ == "__main__":
    print("Testing ShopeeAPI hot reload functionality...")
    print("NOTE: For more comprehensive tests, run the full test suite with: python -m ShopeeAPI.run_tests")

    # Test the auth endpoint
    test_auth_endpoint()

    # Test direct config.json updates
    test_config_reload()

    print("Tests completed!")
