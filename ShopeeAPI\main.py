"""
Run the FastAPI application.
"""
import uvicorn
import os
import sys
import pathlib

# Add parent directory to Python path to make ShopeeAPI package importable
parent_dir = str(pathlib.Path(__file__).parent.parent.absolute())
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

if __name__ == "__main__":
    # Define port
    port = int(os.environ.get("PORT", 8000))
    
    # Run application - use a direct import path that works with the project structure
    uvicorn.run("ShopeeAPI.api:app", host="0.0.0.0", port=port, reload=True) 