# SteamCodeTool - Folder Structure

This document describes the folder structure and organization of the SteamCodeTool project.

## Root Directory Structure

```
SteamCodeTool/
├── api/                          # API related modules
├── data/                         # Data storage directory
├── extension/                    # Browser extension files
│   └── icons/                    # Extension icons
├── extension-dev/                # Development version of extension
│   └── icons/                    # Development extension icons
├── extension-encrypted/          # Encrypted extension files
│   └── icons/                    # Encrypted extension icons
├── scheduler/                    # Task scheduling modules
├── services/                     # Core service modules
├── ShopeeAPI/                    # Shopee API integration (Main Module)
│   ├── cache/                    # Cache storage
│   ├── core/                     # Core functionality
│   ├── middleware/               # Middleware components
│   ├── models/                   # Data models
│   ├── services/                 # Service layer
│   ├── static/                   # Static assets
│   │   ├── css/                  # Stylesheets
│   │   └── js/                   # JavaScript files
│   ├── templates/                # HTML templates
│   ├── tests/                    # Test files
│   └── utils/                    # Utility functions
├── static/                       # Global static files
├── templates/                    # Global templates
├── utils/                        # Global utility functions
├── venv/                         # Python virtual environment
├── config.json                  # Main configuration file
├── main.py                       # Main application entry point
├── main-full.py                  # Full featured main application
├── requirements.txt              # Python dependencies
└── folder_structure.md           # This documentation file
```

## Key Modules and Components

### ShopeeAPI Module (Core)
The main module for Shopee API integration with the following structure:

#### Core Components (`ShopeeAPI/core/`)
- **config.py** - Configuration management with support for websockets 11.x+
- **session.py** - Session management and authentication
- **cache.py** - Caching functionality

#### Services (`ShopeeAPI/services/`)
- **websocket.py** - WebSocket service with fixed connection status checking
  - ✅ **Fixed**: Compatibility with websockets 11.x+ library
  - ✅ **Added**: `_is_connection_open()` method for safe connection status checking
  - ✅ **Resolved**: AttributeError: 'ClientConnection' object has no attribute 'open'
- **chat.py** - Chat service functionality

#### Models (`ShopeeAPI/models/`)
- Data models for API responses and entities

#### Utils (`ShopeeAPI/utils/`)
- **webhook.py** - Webhook management utilities

### Configuration Files
- **config.json** - Main application configuration
- **canva_config.json** - Canva integration settings
- **ShopeeAPI/requirements.txt** - ShopeeAPI specific dependencies
- **requirements.txt** - Global project dependencies

### Extension Files
- **extension/** - Production browser extension
- **extension-dev/** - Development browser extension
- **extension-encrypted/** - Encrypted browser extension

## Recent Changes

### WebSocket Service Fix (Latest)
**Issue Resolved**: `ERROR: 'ClientConnection' object has no attribute 'open'`

**Changes Made**:
1. **Added** `_is_connection_open()` method in `ShopeeAPI/services/websocket.py`
   - Provides backward compatibility with websockets 10.x and 11.x+
   - Safely checks connection status using `closed` attribute (11.x+) or `open` attribute (10.x)
   - Handles exceptions gracefully

2. **Updated** all connection status checks throughout the WebSocket service:
   - Replaced `self.ws_connection.open` with `self._is_connection_open()`
   - Applied to 9 locations in the codebase
   - Maintains existing functionality while fixing compatibility issues

**Technical Details**:
- **Root Cause**: websockets library 11.x+ removed the `open` attribute and replaced it with `closed`
- **Solution**: Implemented compatibility layer that checks for both attributes
- **Testing**: Verified with comprehensive test suite covering all scenarios

## Architecture Principles

This project follows these architectural principles:

1. **Object-Oriented Programming (OOP)**: Classes and objects for better code organization
2. **Model-View-Controller (MVC)**: Separation of concerns across different layers
3. **Modular Design**: Well-organized folder structure with clear separation of responsibilities
4. **Maintainability**: Code is structured for easy maintenance and updates
5. **Flexibility**: Designed to accommodate future changes and extensions

## Dependencies

### Core Dependencies
- **FastAPI** - Web framework for API development
- **websockets>=11.0.3** - WebSocket client/server implementation
- **requests** - HTTP library for API calls
- **uvicorn** - ASGI server for FastAPI

### Development Dependencies
- **pytest** - Testing framework
- **black** - Code formatting
- **isort** - Import sorting
- **flake8** - Code linting

## Notes

- The `venv/` directory contains the Python virtual environment and should not be modified directly
- Cache files are stored in `ShopeeAPI/cache/` and are automatically managed
- Test files are temporarily created during development but are cleaned up automatically
- Configuration files use JSON format for easy editing and validation 