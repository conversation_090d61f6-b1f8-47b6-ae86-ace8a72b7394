from flask import Blueprint, render_template, request, jsonify, current_app, redirect, url_for, session
from config import load_config, save_config, update_config, save_sent_orders, sent_orders, ADMIN_CREDENTIALS
from services.session_service import session_manager
import json
from datetime import datetime, timedelta
from collections import defaultdict
import os
from flask_cors import cross_origin
from functools import wraps
from services.stock_service import get_all_stock
from services.manual_order_service import get_all_manual_orders
from services.netflix_service import load_netflix_sessions, save_netflix_sessions
from services.netflix_service import load_netflix_sessions, save_netflix_sessions
from services.netflix_session_service import netflix_session_manager
import time
from services.deepseek_service import DEFAULT_SYSTEM_PROMPT
from utils.credential_manager import credential_manager

MANUAL_ORDERS_FILE = 'manual_orders.json'

def load_dashboard_data():
    file_path = 'dashboard_data.json'
    if os.path.exists(file_path):
        with open(file_path, 'r') as f:
            return json.load(f)
    return {
        'auth_code_records': [],
        'daily_stats': {},
        'last_update': None
    }

def save_dashboard_data(data):
    with open('dashboard_data.json', 'w') as f:
        json.dump(data, f)

admin_bp = Blueprint('admin', __name__)

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/admin/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.json
        if data['username'] == ADMIN_CREDENTIALS['username'] and data['password'] == ADMIN_CREDENTIALS['password']:
            session['admin_logged_in'] = True
            return jsonify({"message": "Login successful"}), 200
        else:
            return jsonify({"error": "Invalid credentials"}), 401
    return render_template('admin_login.html')


@admin_bp.route('/admin/logout')
def logout():
    session.pop('admin_logged_in', None)
    return redirect(url_for('admin.login'))

@admin_bp.route('/admin', methods=['GET'])
@admin_bp.route('/admin/dashboard', methods=['GET'])
@login_required
def dashboard():
    config = load_config()
    email_config_count = len(config.get('EMAIL_CONFIGS', {}))
    steam_credential_count = len(config.get('STEAM_CREDENTIALS', {}))

    # Load dashboard data from JSON
    dashboard_data = load_dashboard_data()
    auth_code_records = dashboard_data['auth_code_records']

    # Update auth_code_records with new records from session_manager
    new_records = session_manager.get_auth_code_records()
    if new_records:
        auth_code_records.extend(new_records)
        dashboard_data['auth_code_records'] = auth_code_records
        dashboard_data['last_update'] = datetime.now().isoformat()
        save_dashboard_data(dashboard_data)

    # Calculate statistics
    today = datetime.now().date()
    successful_redeems_today = 0
    account_redeems = defaultdict(int)
    order_redeems = defaultdict(int)

    for record in auth_code_records:
        record_date = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S').date()
        if record_date == today and record['status'] == 'Success':
            successful_redeems_today += 1

        account_redeems[record['username']] += 1
        order_redeems[record['order_id']] += 1

    # Enhance summary
    summary = {
        'total_requests': len(auth_code_records),
        'successful_requests': sum(1 for r in auth_code_records if r['status'] == 'Success'),
        'failed_requests': sum(1 for r in auth_code_records if r['status'] == 'Failure'),
        'unique_accounts': len(account_redeems),
        'unique_orders': len(order_redeems),
        'success_rate': round(sum(1 for r in auth_code_records if r['status'] == 'Success') / len(auth_code_records) * 100, 2) if auth_code_records else 0,
    }

    # Calculate daily statistics for the last 7 days
    daily_stats = defaultdict(lambda: {'total': 0, 'success': 0, 'failure': 0})
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=6)
    date_range = [start_date + timedelta(days=i) for i in range(7)]

    for record in auth_code_records:
        record_date = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S').date()
        if start_date <= record_date <= end_date:
            daily_stats[record_date.isoformat()]['total'] += 1
            if record['status'] == 'Success':
                daily_stats[record_date.isoformat()]['success'] += 1
            elif record['status'] == 'Failure':
                daily_stats[record_date.isoformat()]['failure'] += 1

    # Update daily_stats in dashboard_data
    dashboard_data['daily_stats'] = daily_stats
    save_dashboard_data(dashboard_data)

    # Prepare data for charts
    chart_data = {
        'dates': [date.strftime('%Y-%m-%d') for date in date_range],
        'total_requests': [daily_stats[date.isoformat()]['total'] for date in date_range],
        'successful_requests': [daily_stats[date.isoformat()]['success'] for date in date_range],
        'failed_requests': [daily_stats[date.isoformat()]['failure'] for date in date_range],
    }

    return render_template('dashboard.html',
                           email_config_count=email_config_count,
                           steam_credential_count=steam_credential_count,
                           auth_code_records=auth_code_records,
                           successful_redeems_today=successful_redeems_today,
                           account_redeems=dict(account_redeems),
                           order_redeems=dict(order_redeems),
                           summary=summary,
                           chart_data=json.dumps(chart_data))


@admin_bp.route('/admin/system_config', methods=['GET'])
@login_required
def system_config():
    return render_template('system_config.html')

@admin_bp.route('/steam/settings')
@login_required
def steam_settings():
    return render_template('steam_settings.html')

@admin_bp.route('/auto-chat')
@login_required
def auto_chat():
    return render_template('auto_chat.html')

@admin_bp.route('/stock')
@login_required
def stock():
    return render_template('stock.html')

@admin_bp.route('/netflix/accounts')
@login_required
def netflix_accounts():
    return render_template('netflix_accounts.html')

@admin_bp.route('/netflix/settings')
@login_required
def netflix_settings():
    return render_template('netflix_settings.html')

@admin_bp.route('/admin/credentials', methods=['GET'])
@login_required
def credentials():
    return render_template('credentials.html')

@admin_bp.route('/admin/get_config', methods=['GET'])
@login_required
def get_config():
    try:
        config = load_config()

        # Get current credentials from credential manager
        credentials = credential_manager.get_credentials()

        # Update config with current credentials before sending
        config['AUTHORIZATION_CODE'] = credentials['authorization_code']
        config['COOKIE'] = credentials['cookie']

        return jsonify(config)
    except Exception as e:
        print(f"Error in get_config: {str(e)}")
        return jsonify({"error": str(e)}), 500

@admin_bp.route('/admin/update_config', methods=['POST'])
@login_required
def update_config_route():
    try:
        new_config = request.json

        # Update credentials if they are included in the new config
        if 'AUTHORIZATION_CODE' in new_config or 'COOKIE' in new_config:
            credential_manager.update_credentials(
                authorization_code=new_config.get('AUTHORIZATION_CODE'),
                cookie=new_config.get('COOKIE')
            )

        save_config(new_config)
        update_config()
        return jsonify({"message": "Configuration updated successfully"}), 200
    except Exception as e:
        return jsonify({"error": f"Failed to update configuration: {str(e)}"}), 500

@admin_bp.route('/admin/add_email_config', methods=['POST'])
@login_required
def add_email_config():
    data = request.json
    config = load_config()
    config['EMAIL_CONFIGS'][data['key']] = {
        "email": data['email'],
        "app_password": data['app_password']
    }
    save_config(config)
    update_config()
    return jsonify({"message": "Email configuration added successfully"}), 200

@admin_bp.route('/admin/add_steam_credential', methods=['POST'])
@login_required
def add_steam_credential():
    data = request.json
    config = load_config()
    config['STEAM_CREDENTIALS'][data['key']] = {
        "password": data['password']
    }
    save_config(config)
    update_config()
    return jsonify({"message": "Steam credential added successfully"}), 200

@admin_bp.route('/admin/delete_email_config', methods=['POST'])
@login_required
def delete_email_config():
    data = request.json
    config = load_config()
    if data['key'] in config['EMAIL_CONFIGS']:
        del config['EMAIL_CONFIGS'][data['key']]
        save_config(config)
        update_config()
        return jsonify({"message": "Email configuration deleted successfully"}), 200
    else:
        return jsonify({"message": "Email configuration not found"}), 404

@admin_bp.route('/admin/delete_steam_credential', methods=['POST'])
@login_required
def delete_steam_credential():
    data = request.json
    config = load_config()
    if data['key'] in config['STEAM_CREDENTIALS']:
        del config['STEAM_CREDENTIALS'][data['key']]
        save_config(config)
        update_config()
        return jsonify({"message": "Steam credential deleted successfully"}), 200
    else:
        return jsonify({"message": "Steam credential not found"}), 404

@admin_bp.route('/admin/update_email_config', methods=['POST'])
@login_required
def update_email_config():
    data = request.json
    config = load_config()
    if data['key'] in config['EMAIL_CONFIGS']:
        config['EMAIL_CONFIGS'][data['key']] = {
            "email": data['email'],
            "app_password": data['app_password']
        }
        save_config(config)
        update_config()
        return jsonify({"message": "Email configuration updated successfully"}), 200
    else:
        return jsonify({"message": "Email configuration not found"}), 404

@admin_bp.route('/admin/update_steam_credential', methods=['POST'])
@login_required
def update_steam_credential():
    data = request.json
    config = load_config()
    if data['key'] in config['STEAM_CREDENTIALS']:
        config['STEAM_CREDENTIALS'][data['key']] = {
            "password": data['password']
        }
        save_config(config)
        update_config()
        return jsonify({"message": "Steam credential updated successfully"}), 200
    else:
        return jsonify({"message": "Steam credential not found"}), 404

@admin_bp.route('/admin/reset_auth_code_records', methods=['POST'])
@login_required
def reset_auth_code_records():
    session_manager.reset_auth_code_records()
    # Reset the stored dashboard data
    dashboard_data = {'auth_code_records': [], 'daily_stats': {}, 'last_update': datetime.now().isoformat()}
    save_dashboard_data(dashboard_data)
    return jsonify({"message": "Authentication code request records reset successfully"}), 200

@admin_bp.route('/admin/update_shopee_credentials', methods=['POST'])
@cross_origin()
def update_shopee_credentials():
    try:
        data = request.json
        config = load_config()

        # Get bearer token and cookies
        bearer_token = data.get('bearerToken', '')
        cookies = data.get('cookies', '')

        # Update config file
        config['AUTHORIZATION_CODE'] = bearer_token

        # Handle different cookie formats
        if isinstance(cookies, (dict, list)):
            # Store as JSON object
            config['COOKIE_JSON'] = cookies
            # Also store as string for backward compatibility
            if isinstance(cookies, list):
                # Convert array format to string
                cookie_str = '; '.join([f"{c.get('name', '')}={c.get('value', '')}"
                                      for c in cookies if 'name' in c and 'value' in c])
            else:
                # Convert object format to string
                cookie_str = '; '.join([f"{name}={value}" for name, value in cookies.items()])
            config['COOKIE'] = cookie_str
        else:
            # Store as string
            config['COOKIE'] = cookies

        save_config(config)
        update_config()

        # Update credential manager directly
        credential_manager.update_credentials(
            authorization_code=bearer_token,
            cookie=cookies
        )

        # Log the update
        current_app.logger.info("Credentials updated successfully")

        return jsonify({"message": "Credentials updated successfully"}), 200
    except Exception as e:
        current_app.logger.error(f"Error updating credentials: {str(e)}")
        return jsonify({"error": f"Failed to update credentials: {str(e)}"}), 500


@admin_bp.route('/admin/get_cooldowns', methods=['GET'])
@login_required
def get_cooldowns():
    """
    Retrieves all active cooldowns.
    """
    try:
        cooldowns = session_manager.get_all_cooldowns()
        return jsonify({"cooldowns": cooldowns}), 200
    except Exception as e:
        return jsonify({"error": "Failed to retrieve cooldowns"}), 500

@admin_bp.route('/admin/reset_cooldown', methods=['POST'])
@login_required
def reset_cooldown():
    """
    Resets the cooldown for a specific user and order_id.
    Expects JSON payload with 'username' and 'order_id'.
    """
    data = request.json
    username = data.get('username')
    order_id = data.get('order_id')

    if not username or not order_id:
        return jsonify({"error": "Username and order_id are required"}), 400

    success = session_manager.reset_cooldown(username, order_id)
    if success:
        return jsonify({"message": f"Cooldown reset for {username}:{order_id}"}), 200
    else:
        return jsonify({"error": f"Session not found for {username}:{order_id}"}), 404

@admin_bp.route('/admin/update_auto_redeem', methods=['POST'])
@login_required
def update_auto_redeem():
    new_config = request.json
    save_config(new_config)
    update_config()
    return jsonify({"message": "Auto Redeem configuration updated successfully"}), 200

@admin_bp.route('/admin/get_sent_orders', methods=['GET'])
@login_required
def get_sent_orders_route():
    return jsonify({"sent_orders": list(sent_orders)}), 200

@admin_bp.route('/admin/reset_sent_orders', methods=['POST'])
@login_required
def reset_sent_orders():
    sent_orders.clear()
    save_sent_orders(sent_orders)
    return jsonify({"message": "Sent orders reset successfully"}), 200

@admin_bp.route('/admin/update_manual_orders', methods=['POST'])
@login_required
def update_manual_orders():
    manual_orders = request.json
    with open(MANUAL_ORDERS_FILE, 'w') as f:
        json.dump(manual_orders, f, indent=2)
    return jsonify({"message": "Manual orders updated successfully"})

@admin_bp.route('/admin/get_manual_orders', methods=['GET'])
@login_required
def get_manual_orders():
    try:
        with open(MANUAL_ORDERS_FILE, 'r') as f:
            manual_orders = json.load(f)
    except FileNotFoundError:
        manual_orders = []
    return jsonify(manual_orders)

@admin_bp.route('/admin/inventory', methods=['GET'])
@login_required
def inventory():
    return render_template('inventory.html')

@admin_bp.route('/admin/get_inventory_data', methods=['GET'])
@login_required
def get_inventory_data():
    # Get redeemed items
    with open('redeemed_stock.json', 'r') as f:
        redeemed_items = json.load(f)
    redeemed_items_list = [
        {
            'order_sn': order_sn,
            'var_sku': data['var_sku'],
            'item': data['item'],
            'replace_count': data.get('replace_count', 0)
        }
        for order_sn, data in redeemed_items.items()
    ]

    # Get current stock
    current_stock = get_all_stock()

    # Get manual orders
    manual_orders = get_all_manual_orders()

    return jsonify({
        'redeemed_items': redeemed_items_list,
        'current_stock': current_stock,
        'manual_orders': manual_orders
    })

@admin_bp.route('/admin/get_netflix_cooldowns', methods=['GET'])
@login_required
def get_netflix_cooldowns_route():
    try:
        sessions = load_netflix_sessions()
        cooldowns = []
        current_time = time.time()

        for order_sn, order_data in sessions.get('orders', {}).items():
            cooldown_until = order_data.get('cooldown_until')

            # 只显示当前正在冷却中的订单
            if cooldown_until and current_time < cooldown_until:
                remaining = int(cooldown_until - current_time)
                hours = remaining // 3600
                minutes = (remaining % 3600) // 60

                cooldowns.append({
                    'order_sn': order_sn,
                    'account': order_data.get('account'),
                    'last_redeem': order_data.get('last_redeem'),
                    'cooldown_until': cooldown_until,
                    'remaining_time': f"{hours}h {minutes}m",
                    'remaining_seconds': remaining
                })

        # 按剩余冷却时间排序
        cooldowns.sort(key=lambda x: x['remaining_seconds'])
        return jsonify({"cooldowns": cooldowns}), 200
    except Exception as e:
        current_app.logger.error(f"Error getting Netflix cooldowns: {str(e)}")
        return jsonify({"error": "Failed to retrieve cooldowns"}), 500

@admin_bp.route('/admin/reset_netflix_cooldown', methods=['POST'])
@login_required
def reset_netflix_cooldown_route():
    try:
        data = request.json
        order_sn = data.get('order_sn')

        if not order_sn:
            return jsonify({"error": "order_sn is required"}), 400

        sessions = load_netflix_sessions()
        if 'orders' in sessions and order_sn in sessions['orders']:
            # Reset cooldown in the file
            sessions['orders'][order_sn]['cooldown_until'] = None
            sessions['orders'][order_sn]['last_redeem'] = None
            save_netflix_sessions(sessions)

            # Also reset in the session manager for consistency
            netflix_session_manager.sessions[order_sn] = {'cooldown_until': None}
            netflix_session_manager.sessions[order_sn] = {'last_redeem': None}

            current_app.logger.info(f"Netflix cooldown reset for order {order_sn}")
            return jsonify({"message": f"Netflix cooldown reset for order {order_sn}"}), 200
        else:
            current_app.logger.warning(f"Netflix session not found for order {order_sn}")
            return jsonify({"error": f"Netflix session not found for order {order_sn}"}), 404

    except Exception as e:
        current_app.logger.error(f"Error resetting Netflix cooldown: {str(e)}")
        return jsonify({"error": "Failed to reset cooldown"}), 500

@admin_bp.route('/admin/update_netflix_emails', methods=['POST'])
@login_required
def update_netflix_emails():
    email_updates = request.json

    if not email_updates:
        current_app.logger.warning("No email updates provided")
        return jsonify({'success': False, 'message': 'No email updates provided'}), 400

    try:
        current_app.logger.info(f"Received email updates: {email_updates}")

        # Load Netflix sessions
        sessions = load_netflix_sessions()
        current_app.logger.info(f"Loaded sessions: {sessions}")

        # Update emails
        updates_made = False
        for old_email, new_email in email_updates.items():
            for key, value in sessions.items():
                if value.get('account') == old_email:
                    value['account'] = new_email
                    updates_made = True
                    current_app.logger.info(f"Updated email for session {key}: {old_email} -> {new_email}")

        if updates_made:
            # Save updated sessions
            save_netflix_sessions(sessions)
            current_app.logger.info("Netflix sessions updated and saved successfully")
            return jsonify({'success': True, 'message': 'Netflix emails updated successfully'})
        else:
            current_app.logger.warning("No matching emails found to update")
            return jsonify({'success': False, 'message': 'No matching emails found to update'}), 404

    except Exception as e:
        current_app.logger.error(f"Error updating Netflix emails: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error updating Netflix emails: {str(e)}'}), 500

@admin_bp.route('/admin/auto_reply')
@login_required
def auto_reply():
    return render_template('auto_reply.html')

@admin_bp.route('/admin/replace_content', methods=['POST'])
@login_required
def replace_content():
    try:
        # Get data from request
        data = request.json
        replace_type = data['type']
        order_sn = data['order_sn']
        var_sku = data['var_sku']
        item = data['item']
        new_content = data['new_content']
        selected_orders = data.get('selectedOrders', [])  # New: Get list of selected order SNs

        # Load current redeemed stock
        with open('redeemed_stock.json', 'r') as f:
            redeemed_stock = json.load(f)

        def process_order_replacement(order_sn, content):
            """Process replacements for a single order"""
            from services.order_service import get_order_details
            order_details, status = get_order_details(order_sn)

            if status == 200 and isinstance(order_details, dict):
                order_data = order_details['data']
                buyer_user = order_data['buyer_user']
                order_item = order_data['order_items'][0]

                # Variables that can be replaced in the content
                variables = {
                    "{order_sn}": order_sn,
                    "{buyer_username}": buyer_user['user_name'],
                    "{item_name}": order_item['product']['name'],
                    "{item_price}": str(order_data['total_price']),
                    "{buyer_name}": order_data['buyer_address_name'],
                    "{buyer_phone}": order_data['buyer_address_phone'],
                    "{create_time}": order_data['create_time'],
                    "{shipping_address}": order_data['shipping_address'],
                    "{item_sku}": order_item['item_model']['sku'],
                    "{item_quantity}": str(order_item['amount']),
                    "{payment_method}": str(order_data['payment_method']),
                    "{shop_name}": order_data['seller_address']['name'],
                    "{escrow_release_time}": order_data['escrow_release_time'],
                    "{buyer_rating}": str(buyer_user['rating_star']),
                    "{order_status}": str(order_data['status']),
                }

                # Process each section of the content
                processed_content = {
                    'header': content['header'],
                    'content': content['content'],
                    'footer': content['footer']
                }

                # Replace variables in each section
                for section in processed_content:
                    if processed_content[section]:
                        text = processed_content[section]
                        for key, value in variables.items():
                            text = text.replace(key, str(value))
                        processed_content[section] = text

                return processed_content
            return content

        # Handle bulk replacement if selected_orders is provided
        if selected_orders:
            for selected_order_sn in selected_orders:
                if selected_order_sn in redeemed_stock:
                    # Increment or initialize replace_count
                    redeemed_stock[selected_order_sn]['replace_count'] = redeemed_stock[selected_order_sn].get('replace_count', 0) + 1
                    processed_content = process_order_replacement(selected_order_sn, new_content)
                    redeemed_stock[selected_order_sn]['item'] = processed_content['content'].strip()
                    send_formatted_message(selected_order_sn, processed_content)
        else:
            if replace_type == 'single':
                if order_sn in redeemed_stock:
                    redeemed_stock[order_sn]['replace_count'] = redeemed_stock[order_sn].get('replace_count', 0) + 1
                    processed_content = process_order_replacement(order_sn, new_content)
                    redeemed_stock[order_sn]['item'] = processed_content['content'].strip()
                    send_formatted_message(order_sn, processed_content)

            elif replace_type == 'var_sku':
                for key, value in redeemed_stock.items():
                    if value['var_sku'] == var_sku:
                        value['replace_count'] = value.get('replace_count', 0) + 1
                        processed_content = process_order_replacement(key, new_content)
                        value['item'] = processed_content['content'].strip()
                        send_formatted_message(key, processed_content)

            elif replace_type == 'content':
                for key, value in redeemed_stock.items():
                    if value['item'] == item:
                        value['replace_count'] = value.get('replace_count', 0) + 1
                        processed_content = process_order_replacement(key, new_content)
                        value['item'] = processed_content['content'].strip()
                        send_formatted_message(key, processed_content)

        # Save the updated content back to file
        with open('redeemed_stock.json', 'w') as f:
            json.dump(redeemed_stock, f, indent=2)

        return jsonify({'success': True, 'message': 'Content replaced successfully'})

    except Exception as e:
        print(f"Error in replace_content: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

def send_formatted_message(order_sn, content_data):
    """Helper function to send formatted chat message"""
    try:
        # Format the complete message with header, content, and footer
        formatted_parts = []

        if content_data['header']:
            header_lines = content_data['header'].strip().split('\n')
            formatted_parts.append('\n'.join(header_lines))

        if content_data['content']:
            content_lines = content_data['content'].strip().split('\n')
            formatted_parts.append('\n'.join(content_lines))

        if content_data['footer']:
            footer_lines = content_data['footer'].strip().split('\n')
            formatted_parts.append('\n'.join(footer_lines))

        # Join all parts with double line breaks
        complete_message = '\n\n'.join(formatted_parts)

        # Send the chat message
        from services.chat_service import send_chat_message
        chat_payload = {
            'order_sn': order_sn,
            'text': complete_message,
            'force_send_cancel_order_warning': False,
            'comply_cancel_order_warning': False
        }
        send_chat_message(chat_payload)

    except Exception as e:
        print(f"Error sending formatted message for order {order_sn}: {str(e)}")

@admin_bp.route('/admin/manual_invoice', methods=['GET'])
@login_required
def manual_invoice():
    return render_template('manual_invoice.html')

@admin_bp.route('/canva/manage')
@login_required
def canva_manage():
    try:
        with open('canva_config.json', 'r') as f:
            canva_config = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        canva_config = {}  # Initialize to an empty dictionary if the file is not found or invalid

    return render_template('canva_manage.html', canva_config=canva_config)

@admin_bp.route('/admin/get_canva_config', methods=['GET'])
@login_required
def get_canva_config():
    try:
        with open('canva_config.json', 'r') as f:
            canva_config = json.load(f)
        return jsonify(canva_config)
    except (FileNotFoundError, json.JSONDecodeError):
        return jsonify({"error": "Failed to load Canva config"}), 500

@admin_bp.route('/admin/update_canva_config', methods=['POST'])
@login_required
def update_canva_config():
    try:
        new_config = request.json
        # 保持 API_KEY 不变
        with open('canva_config.json', 'r') as f:
            current_config = json.load(f)
            api_key = current_config.get('API_KEY', '')

        new_config['API_KEY'] = api_key
        with open('canva_config.json', 'w') as f:
            json.dump(new_config, f, indent=4)
        return jsonify({"message": "Canva config updated successfully"}), 200
    except Exception as e:
        return jsonify({"error": f"Failed to update Canva config: {str(e)}"}), 500

@admin_bp.route('/admin/ai_chat')
@login_required
def ai_chat():
    return render_template('ai_chat.html')

@admin_bp.route('/admin/get_ai_config', methods=['GET'])
@login_required
def get_ai_config():
    try:
        config = load_config()
        ai_settings = {
            'AI_REPLY_ENABLED': config.get('AI_REPLY_ENABLED', False),
            'AI_REPLY_COOLDOWN_MINUTES': config.get('AI_REPLY_COOLDOWN_MINUTES', 60),
            'AI_SYSTEM_PROMPT': config.get('AI_SYSTEM_PROMPT', DEFAULT_SYSTEM_PROMPT),
            'AI_TEMPERATURE': config.get('AI_TEMPERATURE', 1.0),
            'DEEPSEEK_API_KEY': config.get('DEEPSEEK_API_KEY', '')
        }
        return jsonify(ai_settings)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@admin_bp.route('/admin/update_ai_config', methods=['POST'])
@login_required
def update_ai_config():
    try:
        new_config = request.json
        config = load_config()

        # Update AI settings
        config['AI_REPLY_ENABLED'] = new_config.get('AI_REPLY_ENABLED', False)
        config['AI_REPLY_COOLDOWN_MINUTES'] = new_config.get('AI_REPLY_COOLDOWN_MINUTES', 60)
        config['AI_SYSTEM_PROMPT'] = new_config.get('AI_SYSTEM_PROMPT', DEFAULT_SYSTEM_PROMPT)
        config['AI_TEMPERATURE'] = new_config.get('AI_TEMPERATURE', 1.0)
        config['DEEPSEEK_API_KEY'] = new_config.get('DEEPSEEK_API_KEY', config.get('DEEPSEEK_API_KEY', ''))

        save_config(config)
        update_config()
        return jsonify({"message": "AI configuration updated successfully"}), 200
    except Exception as e:
        return jsonify({"error": f"Failed to update configuration: {str(e)}"}), 500