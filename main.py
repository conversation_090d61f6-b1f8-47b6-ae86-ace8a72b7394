from flask import Flask, render_template, jsonify, redirect, request, session
from api.order_routes import order_bp
from api.conversation_routes import conversation_bp
from api.chat_routes import chat_bp
from api.email_routes import email_bp
from api.admin_routes import admin_bp
from api.canva_routes import canva_bp
from api.vpn_routes import vpn_bp
from api.curlec_routes import curlec_blueprint
from flask_cors import CORS
from scheduler.scheduler import start_scheduler
from utils.credential_manager import credential_manager
from config import load_config
from services.canva_service import CanvaService
import os
import secrets
import hmac
import time
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
import json
import datetime
from urllib.parse import urlparse, parse_qs

app = Flask(__name__)
CORS(app)

app.secret_key = os.urandom(24)
canva_service = CanvaService()

# Register blueprints
app.register_blueprint(order_bp, url_prefix='/api')
app.register_blueprint(conversation_bp, url_prefix='/api')
app.register_blueprint(chat_bp, url_prefix='/api')
app.register_blueprint(email_bp, url_prefix='/api')
app.register_blueprint(canva_bp, url_prefix='/api')
app.register_blueprint(admin_bp)
app.register_blueprint(vpn_bp)
app.register_blueprint(curlec_blueprint)

def init_credential_manager():
    config = load_config()
    credential_manager.update_credentials(
        authorization_code=config.get('AUTHORIZATION_CODE', ''),
        cookie=config.get('COOKIE', '')
    )

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/canva_order')
def enter_canva_order():
    return render_template('canva_order.html')

# Canva Intation
@app.route('/canva_redeem_invitation')
def canva_redeem_invitation():
    """处理Canva邀请链接的兑换"""
    token = request.args.get('token')
    order_sn = request.args.get('order')
    
    if not token or not order_sn:
        return render_template('canva_redeem_error.html', 
                             error="Invalid invitation link"), 400
    
    # 首先检查订单是否已经被兑换
    canva_service.load_orders()  # 确保使用最新数据
    if order_sn in canva_service.orders:
        if canva_service.orders[order_sn].get('redeemed', False):
            return render_template('canva_redeem_error.html', 
                                 error="This invitation has already been redeemed"), 400
    
    canva_url, error = canva_service.redeem_invitation(token, order_sn)
    if error:
        return render_template('canva_redeem_error.html', 
                             error=error), 400
    
    # 生成临时令牌
    temp_token = secrets.token_urlsafe(32)
    session[temp_token] = canva_url
    
    # 传递临时令牌而不是实际URL
    return render_template('canva_redeem_success.html', 
                         temp_token=temp_token)

@app.route('/api/canva/get_redirect', methods=['POST'])
def canva_get_redirect():
    """获取实际的重定向URL"""
    try:
        data = request.json
        temp_token = data.get('token')

        if not temp_token or temp_token not in session:
            return jsonify({'error': 'Invalid token'}), 400
        
        # 获取并立即删除会话中的URL
        redirect_url = session.pop(temp_token, None)
        if not redirect_url:
            return jsonify({'error': 'Token expired'}), 400
            
        # 从URL中提取order_sn参数
        parsed_url = urlparse(redirect_url)
        query_params = parse_qs(parsed_url.query)
        order_sn = query_params.get('order', [None])[0]
        
        if order_sn:
            # 重新加载订单数据以确保数据最新
            canva_service.load_orders()
            
            if order_sn in canva_service.orders:
                # 使用 canva_service 的方法更新订单状态
                canva_service.orders[order_sn].update({
                    'redeemed': True,
                    'redeemed_at': datetime.datetime.now().isoformat(),
                    'redemption_token': None
                })
                # 确保保存更改
                canva_service.save_orders()
                
                # 验证更改是否成功
                canva_service.load_orders()
                if not canva_service.orders[order_sn].get('redeemed', False):
                    print(f"Warning: Failed to update order {order_sn} status to redeemed")
        
        return jsonify({
            'url': redirect_url
        })
    except Exception as e:
        print(f"Error in get_redirect: {str(e)}")  # 添加错误日志
        return jsonify({'error': str(e)}), 400

@app.route('/order')
def enter_order():
    return render_template('order.html')

@app.route('/netflix_signin')
def netflix_signin():
    return render_template('netflix_signin.html')

@app.errorhandler(401)
def unauthorized(error):
    return jsonify({"error": "Unauthorized access"}), 401

@app.errorhandler(500)
def internal_server_error(error):
    return jsonify({"error": "Internal server error. Please try again later."}), 500

if __name__ == '__main__':
    init_credential_manager()  # Initialize credentials
    start_scheduler()
    app.run(debug=True, use_reloader=True, port=5000)

