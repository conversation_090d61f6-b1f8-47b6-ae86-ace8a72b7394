#!/usr/bin/env python3
"""
Script to check environment variables in the container.
"""
import os
import sys

def main():
    """Print all environment variables."""
    print("Environment Variables:")
    print("-" * 50)
    
    # Get PORT environment variable
    port = os.environ.get("PORT", "8000 (default)")
    print(f"PORT: {port}")
    
    # Get PYTHONPATH environment variable
    pythonpath = os.environ.get("PYTHONPATH", "Not set")
    print(f"PYTHONPATH: {pythonpath}")
    
    # Get Python version
    print(f"Python version: {sys.version}")
    
    # Get current working directory
    print(f"Current working directory: {os.getcwd()}")
    
    # Check if ShopeeAPI module is importable
    try:
        import ShopeeAPI
        print(f"ShopeeAPI module found at: {ShopeeAPI.__file__}")
    except ImportError as e:
        print(f"ShopeeAPI module not found: {e}")
    
    # Print all environment variables
    print("\nAll Environment Variables:")
    print("-" * 50)
    for key, value in sorted(os.environ.items()):
        # Truncate long values
        if len(value) > 50:
            value = value[:47] + "..."
        print(f"{key}: {value}")

if __name__ == "__main__":
    main()
