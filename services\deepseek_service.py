from openai import OpenAI
from typing import List, Dict, Any
import json
import config

def get_client():
    """Get OpenAI client with current API key"""
    return OpenAI(
        api_key=config.DEEPSEEK_API_KEY,
        base_url="https://api.deepseek.com"
    )

DEFAULT_SYSTEM_PROMPT = """You are a Customer Service Representative. Analyze the conversation history (up to 20 messages) and respond naturally as a human customer service agent would.

Product Knowledge Base:
{
    "product_rules": [
        {
            "product_id": "28022545701",
            "product_type": "VPN Service",
            "specific_guidelines": [
                "Don't promise specific connection speeds",
                "Available anytime, our system are automatically delivering the order 24/7 hours.",
                "If failed to get the VPN, please wait us to online, we will handle it manually."
            ],
            "common_responses": {
                "connection_issue": "Try to on off the airplane mode, if still not working, please send your config, we will check it once we online.",
                "what different between special and non-special": "The special is for the maxis telco, because the price is different.",
                "what different between GBNetwork, Shinjiru and Singapore Server": "🔥Shinjiru / GBNetwork ?
𝑺𝒉𝒊𝒏𝒋𝒊𝒓𝒖 (Full: 150Mbps)
✅ Murah, Seller2 guna server tu
✅ Dapat Area Full Speed 40-60%
❌ Banyak orang guna data akan jadi slow sikit

𝑮𝑩𝑵𝒆𝒕𝒘𝒐𝒓𝒌 (Full: 5000Mbps)
✅ Support Higher Speed Cap
✅ Dapat Area Full Speed 70-80%
✅ Byk org guna data pun x akan affect network speed

Singapore Server (Full: 1000Mbps)
✅ Menyokong Netflix, OTT, Hotstar, Disney, VIU, HBO
❌ Tidak stabil kadangkala
❌ YouTube atau banyak kandungan Malaysia auto tukar ke kawasan Singapura

⚠️ Kalau area kamu x lebih dari 50mbps, xbanyak beza.",
                "What is this product? How does this work": "You can send chat '#vpn' and the bot will send you the details.",
                "What telco bypass currently available?": "You can send chat '#bypass' and the bot will send you the details."
            }
        },
        {
            "product_id": "27672541012",
            "product_type": "Canva Pro",
            "specific_guidelines": [
                "The product always available, the order will be delivered automatically.",
                "If failed to get the canva pro, please leave your email, we will handle it manully once we online."
            ],
            "common_responses": {
                "why the canva become non pro or invalid": "Please leave your email, we will check it once we online later.",
                "Can this work on all device ?": "Yes, it can work on all device for the user buying Lifetime variation, if the 1 Month variation only can work on the PC."
            }
        }
    ]
}

Response Requirements:
1. Always respond in this JSON format (DO NOT USE MARKDOWN):
{
    "actions": [
        {
            "type": "chat",
            "reply": "your direct response here [MTYB]"
        },
        {
            "type": "send_order",
            "order_sn": "order_number_here"
        }
    ]
}

Available Actions:
{
    "actions": [
        {
            "type": "chat",
            "description": "Send a normal chat message to user"
        },
        {
            "type": "send_order",
            "description": "Send order details to user",
            "requires": "order_sn"
        }
    ]
}

Important Guidelines:
1. If source_content is not null:
- Check product_id and shop_id
- Use relevant product-specific guidelines and responses
- Maintain product compliance requirements

2. In the "reply" field:
- Start responding directly without greetings
- End every response with [MTYB]
- Ask questions when unclear
- Reference previous messages naturally
- Match customer's language style (Malay, Chinese, English)
- Short response with clear instruction.
- Do not talk about anything else that we didnt provide, just tell the customer you dont know, and what you can do as AI.
- Use "\n" to break lines.
- Do not use markdown.
- Do not use any other formatting.
- You are allow to use emoji to act like a human.

3. Never make promises about:
- Refund guarantees
- Seller actions

4. Keep all responses concise and solution-focused
"""

def format_messages_for_context(messages: List[Dict[str, Any]], max_messages: int = 5) -> str:
    """Format recent messages into a readable context for the AI"""
    formatted = []
    # Take last N messages (they're already in reverse chronological order)
    for msg in messages[:max_messages]:
        sender = "Seller" if msg['from_shop_id'] == config.SHOP_ID else "Customer"
        text = msg['content'].get('text', '')
        timestamp = msg['created_at']
        
        # Add source_content information if available
        source_info = ""
        if msg.get('source_content'):
            product_id = msg['source_content'].get('product_id')
            if product_id:
                source_info = f" [Product ID: {product_id}]"
        
        formatted.append(f"{sender} ({timestamp}){source_info}: {text}")
    print(formatted)
    return "\n".join(formatted)

def process_action(actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process multiple actions based on AI response"""
    results = []
    
    for action in actions:
        action_type = action.get('type')
        
        if action_type == 'chat':
            results.append({
                "type": "chat",
                "status": "sent",
                "reply": action.get('reply', '')
            })
            
        elif action_type == 'send_order':
            order_sn = action.get('order_sn')
            if order_sn:
                from services.order_service import get_order_details
                order_result, status_code = get_order_details(order_sn)
                results.append({
                    "type": "send_order",
                    "status": "success" if status_code == 200 else "failed",
                    "order_sn": order_sn,
                    "order_data": order_result if status_code == 200 else None,
                    "error": order_result.get('error') if status_code != 200 else None
                })
            else:
                results.append({
                    "type": "send_order",
                    "status": "failed",
                    "error": "Missing order_sn"
                })
    
    return results

def generate_reply(conversation_messages: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate an AI response based on conversation history"""
    try:
        context = format_messages_for_context(conversation_messages)
        client = get_client()
        
        system_prompt = config.AI_SYSTEM_PROMPT if hasattr(config, 'AI_SYSTEM_PROMPT') else DEFAULT_SYSTEM_PROMPT
        temperature = float(config.AI_TEMPERATURE if hasattr(config, 'AI_TEMPERATURE') else 1.0)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Here is the recent conversation history:\n\n{context}\n\nPlease analyze and generate an appropriate response:"}
        ]

        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            temperature=temperature,
            stream=False,
            response_format={"type": "json_object"}
        )

        try:
            response_content = response.choices[0].message.content
            print(response_content)
            parsed_response = json.loads(response_content)
            
            # Process all actions and get results
            action_results = process_action(parsed_response.get("actions", []))
            
            return {
                "success": True,
                "data": parsed_response,
                "action_results": action_results
            }
        except json.JSONDecodeError:
            return {
                "success": False,
                "error": "Failed to parse AI response as JSON",
                "raw_response": response_content
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"AI generation failed: {str(e)}"
        }

def get_temperature_for_context(message_type: str) -> float:
    """Return appropriate temperature based on context"""
    temperature_map = {
        "technical": 0.0,  # For technical/coding responses
        "data": 1.0,      # For data analysis
        "general": 1.3,   # For general conversation
        "translation": 1.3,# For translations
        "creative": 1.0   # For creative writing
    }
    return temperature_map.get(message_type, float(config.AI_TEMPERATURE if hasattr(config, 'AI_TEMPERATURE') else 1.0))
