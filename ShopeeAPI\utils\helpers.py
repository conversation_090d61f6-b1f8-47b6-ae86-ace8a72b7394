"""
Helper functions for the Shopee API.
"""
import os
import json
import time
import uuid
from typing import Dict, Any, Optional
from datetime import datetime


def generate_request_id() -> str:
    """
    Generate a unique request ID.
    
    Returns:
        Unique request ID string
    """
    return str(uuid.uuid4())


def timestamp_to_datetime(timestamp: int) -> datetime:
    """
    Convert a Unix timestamp to a datetime object.
    
    Args:
        timestamp: Unix timestamp in seconds
        
    Returns:
        Datetime object
    """
    return datetime.fromtimestamp(timestamp)


def datetime_to_timestamp(dt: datetime) -> int:
    """
    Convert a datetime object to a Unix timestamp.
    
    Args:
        dt: Datetime object
        
    Returns:
        Unix timestamp in seconds
    """
    return int(dt.timestamp())


def load_json_file(file_path: str) -> Dict[str, Any]:
    """
    Load a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        Dictionary with the JSON data
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    with open(file_path, 'r') as f:
        return json.load(f)


def save_json_file(file_path: str, data: Dict[str, Any]) -> None:
    """
    Save data to a JSON file.
    
    Args:
        file_path: Path to save the JSON file
        data: Dictionary to save
        
    Raises:
        IOError: If the file can't be written
    """
    with open(file_path, 'w') as f:
        json.dump(data, f, indent=2)


def get_file_modification_time(file_path: str) -> Optional[float]:
    """
    Get the last modification time of a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Modification time as a float, or None if the file doesn't exist
    """
    if os.path.exists(file_path):
        return os.path.getmtime(file_path)
    return None


def rate_limit(last_request_time: float, request_interval: float) -> float:
    """
    Apply rate limiting between requests.
    
    Args:
        last_request_time: Time of the last request
        request_interval: Minimum interval between requests
        
    Returns:
        Current time after rate limiting
    """
    if request_interval > 0:
        current_time = time.time()
        elapsed = current_time - last_request_time
        if elapsed < request_interval:
            time.sleep(request_interval - elapsed)
    return time.time()
