<!DOCTYPE html>
<html>
<head>
    <title>WebSocket API Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #444;
            margin-top: 30px;
        }
        h3 {
            color: #555;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: monospace;
        }
        .endpoint {
            background-color: #e9f7fe;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }
        .test-area {
            margin-top: 40px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.disconnect {
            background-color: #f44336;
        }
        button.disconnect:hover {
            background-color: #d32f2f;
        }
        #status {
            margin: 10px 0;
            font-weight: bold;
        }
        #messages {
            height: 300px;
            overflow-y: scroll;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
            background-color: white;
            font-family: monospace;
            font-size: 14px;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .endpoint-url {
            font-family: monospace;
            background-color: #f0f0f0;
            padding: 3px 6px;
            border-radius: 3px;
        }
        .url-input {
            width: 300px;
            padding: 8px;
            margin-right: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket API Documentation</h1>

        <p>This page documents the WebSocket endpoints available in the Shopee API and provides a simple interface to test them.</p>

        <div class="endpoint">
            <h2>General Chat WebSocket</h2>
            <p>Endpoint: <span class="endpoint-url">ws://localhost:8000/ws/chat</span></p>
            <p>This endpoint provides real-time chat messages from all conversations.</p>

            <h3>Response Format</h3>
            <p>Upon connection:</p>
            <pre>{
  "type": "connection_established",
  "client_id": "unique-id",
  "timestamp": "2025-05-13T18:49:28.837108"
}</pre>

            <h3>Webhook Integration</h3>
            <p>The WebSocket service can send webhook notifications when messages are sent or received. This can be configured in the config.json file:</p>
            <pre>{
  "WEBHOOK": {
    "ENABLED": true,
    "MESSAGE_RECEIVED": {
      "ENABLED": true,
      "URL": "http://your-webhook-url/message-received",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    },
    "MESSAGE_SENT": {
      "ENABLED": true,
      "URL": "http://your-webhook-url/message-sent",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    }
  }
}</pre>
            <p>When a message is received or sent, a webhook notification will be sent to the configured URL with the following payload:</p>
            <pre>// For received messages:
{
  "message": {
    // Full message content
    "id": "2347556398049968497",
    "shop_id": 345602862,
    "from_id": 345622437,
    "to_id": 1368568180,
    "from_user_name": "mtyb_official",
    "to_user_name": "me0tn_14qo",
    "type": "text",
    "content": {"text": "hi"},
    "conversation_id": "1484437065047388532",
    "send_by_yourself": false,
    // ... other message fields
  },
  "conversation_id": "1484437065047388532",
  "event": "message_received",
  "timestamp": 1747410578
}

// For sent messages:
{
  "message": {
    // Full message content
    "id": "2347556170106225009",
    "shop_id": 1367853447,
    "from_id": 1368568180,
    "to_id": 345622437,
    "from_user_name": "me0tn_14qo",
    "to_user_name": "mtyb_official",
    "type": "text",
    "content": {"text": "hi"},
    "conversation_id": "1484437065047388532",
    "send_by_yourself": true,
    // ... other message fields
  },
  "conversation_id": "1484437065047388532",
  "event": "message_sent",
  "timestamp": 1747410470
}</pre>

            <h3>Message Types</h3>
            <p>The WebSocket connection will receive different types of messages from Shopee. Here are the main message types:</p>

            <h4>1. Chat Messages</h4>
            <pre>{
  "type": "chat_message",
  "event": "message",
  "data": {
    "ack": false,
    "message_content": "...", // JSON string containing message details
    "message_id": "a5700dbf67be475196c03383a91f1acf",
    "message_type": "message",
    "request_id": "625da116-d9a6-491f-bf79-6f33784e1b04"
  },
  "timestamp": "2025-05-13T18:49:28.837108"
}</pre>
            <p>The <code>message_content</code> field is a JSON string that contains the actual message details, including:</p>
            <ul>
                <li><strong>id</strong>: Message ID</li>
                <li><strong>conversation_id</strong>: Conversation ID</li>
                <li><strong>from_id</strong>: Sender ID</li>
                <li><strong>to_id</strong>: Recipient ID</li>
                <li><strong>from_user_name</strong>: Sender username</li>
                <li><strong>to_user_name</strong>: Recipient username</li>
                <li><strong>type</strong>: Message type (e.g., "text")</li>
                <li><strong>content</strong>: Message content (e.g., {"text": "Hello"})</li>
                <li><strong>created_timestamp</strong>: Message creation timestamp</li>
                <li><strong>created_at</strong>: Message creation time in ISO format</li>
            </ul>

            <h4>2. Notification Messages</h4>
            <pre>{
  "type": "chat_message",
  "event": "message",
  "data": {
    "ack": false,
    "message_content": "...", // JSON string containing notification details
    "message_id": "8a26ccf844d64b4bb33c57d5c3c045ad",
    "message_type": "notification",
    "request_id": ""
  },
  "timestamp": "2025-05-13T18:49:28.844023"
}</pre>
            <p>Notification messages include system events like "mark_as_replied" and other status updates.</p>

            <h4>3. Heartbeat Messages</h4>
            <pre>{
  "type": "heartbeat",
  "event": "heartbeat",
  "data": "pmminichat|0|0",
  "timestamp": "2025-05-13T18:48:28.844023"
}</pre>
            <p>Heartbeat messages are sent periodically to maintain the connection.</p>

            <h4>4. Status Updates</h4>
            <pre>{
  "type": "status_update",
  "connected_to_shopee": true,
  "last_message_time": "2025-05-13T18:49:28.844023",
  "client_count": 1,
  "timestamp": "2025-05-13T18:49:28.844023"
}</pre>
            <p>Status updates provide information about the WebSocket connection status.</p>

            <h3>Parsing Message Content</h3>
            <p>The <code>message_content</code> field in chat messages and notifications is a JSON string that needs to be parsed. Here's an example of how to parse it in JavaScript:</p>
            <pre>
// Assuming 'data' is the received WebSocket message
const message = JSON.parse(data);
if (message.type === "chat_message" && message.data && message.data.message_content) {
  try {
    // Parse the message_content JSON string
    const messageContent = JSON.parse(message.data.message_content);

    // Now you can access the message details
    const conversationId = messageContent.conversation_id;
    const fromUsername = messageContent.from_user_name;
    const toUsername = messageContent.to_user_name;

    // For text messages
    if (messageContent.type === "text" && messageContent.content && messageContent.content.text) {
      const textMessage = messageContent.content.text;
      console.log(`Message from ${fromUsername} to ${toUsername}: ${textMessage}`);
    }
  } catch (e) {
    console.error("Error parsing message content:", e);
  }
}
</pre>
        </div>

        <div class="endpoint">
            <h2>Username-specific Chat WebSocket</h2>
            <p>Endpoint: <span class="endpoint-url">ws://localhost:8000/ws/chat/{username}</span></p>
            <p>This endpoint provides real-time chat messages filtered for a specific username.</p>

            <h3>Parameters</h3>
            <ul>
                <li><strong>username</strong>: The username to filter messages for</li>
            </ul>

            <h3>Response Format</h3>
            <p>Same as the general chat WebSocket, but messages are filtered to only include those where:</p>
            <ul>
                <li>The specified username is the sender (<code>from_user_name</code>)</li>
                <li>The specified username is the recipient (<code>to_user_name</code>)</li>
            </ul>
            <p>This allows you to monitor all messages for a specific customer or seller.</p>
        </div>

        <div class="test-area">
            <h2>Test WebSocket Connection</h2>

            <div>
                <label for="ws-url">WebSocket URL:</label>
                <input type="text" id="ws-url" class="url-input" value="ws://localhost:8000/ws/chat">
                <button onclick="connect()">Connect</button>
                <button onclick="disconnect()" class="disconnect">Disconnect</button>
            </div>

            <div id="status">Disconnected</div>

            <div style="margin-top: 10px;">
                <button onclick="sendCommand('status')" id="status-btn" disabled>Get Status</button>
                <button onclick="sendCommand('reconnect')" id="reconnect-btn" disabled>Force Reconnect</button>
                <button onclick="clearMessages()" id="clear-btn">Clear Messages</button>
            </div>

            <h3>Messages</h3>
            <div id="messages"></div>

            <div style="margin-top: 20px;">
                <h3>Debug Information</h3>
                <div>
                    <strong>Connection Status:</strong> <span id="connection-status">Not connected</span>
                </div>
                <div>
                    <strong>Last Message:</strong> <span id="last-message-time">None</span>
                </div>
                <div>
                    <strong>Connected Clients:</strong> <span id="client-count">0</span>
                </div>
                <div>
                    <strong>Messages Received:</strong> <span id="message-count">0</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>ShopeeAPI WebSocket Documentation | <a href="/docs">Back to API Docs</a></p>
        </div>
    </div>

    <script>
        let socket = null;
        const messagesDiv = document.getElementById('messages');
        const statusDiv = document.getElementById('status');
        const statusBtn = document.getElementById('status-btn');
        const reconnectBtn = document.getElementById('reconnect-btn');
        const connectionStatusSpan = document.getElementById('connection-status');
        const lastMessageTimeSpan = document.getElementById('last-message-time');
        const clientCountSpan = document.getElementById('client-count');
        const messageCountSpan = document.getElementById('message-count');

        let messageCount = 0;

        function connect() {
            const url = document.getElementById('ws-url').value;

            // Close existing connection if any
            if (socket) {
                socket.close();
            }

            // Update UI
            statusDiv.textContent = 'Connecting...';
            statusDiv.style.color = 'orange';
            connectionStatusSpan.textContent = 'Connecting...';
            connectionStatusSpan.style.color = 'orange';

            // Connect to WebSocket
            socket = new WebSocket(url);

            socket.onopen = function(e) {
                statusDiv.textContent = 'Connected';
                statusDiv.style.color = 'green';
                connectionStatusSpan.textContent = 'Connected';
                connectionStatusSpan.style.color = 'green';
                addMessage('Connection established');

                // Enable buttons
                statusBtn.disabled = false;
                reconnectBtn.disabled = false;
            };

            socket.onmessage = function(event) {
                messageCount++;
                messageCountSpan.textContent = messageCount;

                try {
                    const data = JSON.parse(event.data);
                    addMessage('Received: ' + JSON.stringify(data, null, 2));

                    // Update debug information
                    if (data.type === 'connection_established' || data.type === 'status_update' || data.type === 'status_response') {
                        if (data.connected_to_shopee !== undefined) {
                            connectionStatusSpan.textContent = data.connected_to_shopee ? 'Connected to Shopee' : 'Not connected to Shopee';
                            connectionStatusSpan.style.color = data.connected_to_shopee ? 'green' : 'red';
                        } else if (data.websocket_status && data.websocket_status.connected_to_shopee !== undefined) {
                            connectionStatusSpan.textContent = data.websocket_status.connected_to_shopee ? 'Connected to Shopee' : 'Not connected to Shopee';
                            connectionStatusSpan.style.color = data.websocket_status.connected_to_shopee ? 'green' : 'red';
                        }

                        if (data.last_message_time) {
                            lastMessageTimeSpan.textContent = new Date(data.last_message_time).toLocaleString();
                        }

                        if (data.client_count !== undefined) {
                            clientCountSpan.textContent = data.client_count;
                        } else if (data.websocket_status && data.websocket_status.client_count !== undefined) {
                            clientCountSpan.textContent = data.websocket_status.client_count;
                        }
                    }
                } catch (e) {
                    addMessage('Received (non-JSON): ' + event.data);
                }
            };

            socket.onclose = function(event) {
                if (event.wasClean) {
                    addMessage(`Connection closed cleanly, code=${event.code} reason=${event.reason}`);
                } else {
                    addMessage('Connection died');
                }
                statusDiv.textContent = 'Disconnected';
                statusDiv.style.color = 'red';
                connectionStatusSpan.textContent = 'Not connected';
                connectionStatusSpan.style.color = 'red';

                // Disable buttons
                statusBtn.disabled = true;
                reconnectBtn.disabled = true;
            };

            socket.onerror = function(error) {
                addMessage(`Error: ${error.message || 'Unknown error'}`);
                statusDiv.textContent = 'Error';
                statusDiv.style.color = 'red';
                connectionStatusSpan.textContent = 'Error';
                connectionStatusSpan.style.color = 'red';
            };
        }

        function disconnect() {
            if (socket) {
                socket.close();
                socket = null;
            }
        }

        function sendCommand(command) {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage(`Cannot send command: WebSocket not connected`);
                return;
            }

            const message = {
                command: command,
                timestamp: Date.now()
            };

            try {
                socket.send(JSON.stringify(message));
                addMessage(`Sent command: ${command}`);
            } catch (e) {
                addMessage(`Error sending command: ${e.message}`);
            }
        }

        function clearMessages() {
            messagesDiv.innerHTML = '';
            addMessage('Messages cleared');
        }

        function addMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message';
            messageElement.textContent = new Date().toLocaleTimeString() + ': ' + message;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 添加自动重连功能
        window.addEventListener('online', function() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage('Network connection restored. Attempting to reconnect...');
                setTimeout(connect, 1000);
            }
        });
    </script>
</body>
</html>
