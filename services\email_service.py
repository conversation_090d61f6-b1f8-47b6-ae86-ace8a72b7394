import imaplib
import email
from email.header import decode_header
import re
import time
from config import load_config 
import logging

logger = logging.getLogger(__name__)

def get_steam_auth_code(email_address, app_password, username, request_timeout):
    # Connect to Gmail's IMAP server
    imap_server = "imap.gmail.com"
    imap_port = 993

    # Create an IMAP4 client encrypted with SSL
    mail = imaplib.IMAP4_SSL(imap_server, imap_port)
    logger.info(f"Connected to IMAP server: {imap_server}:{imap_port}")

    try:
        # Login to the email account
        mail.login(email_address, app_password)
        logger.info(f"Logged in to email account: {email_address}")

        start_time = time.time()

        while time.time() - start_time < request_timeout:
            # Select the inbox
            mail.select("inbox")
            logger.info("Selected inbox")

            # Search for unread <NAME_EMAIL>
            _, message_numbers = mail.search(None, '(UNSEEN FROM "<EMAIL>")')
            message_numbers = message_numbers[0].split()
            logger.info(f"Found {len(message_numbers)} unread <NAME_EMAIL>")

            for num in reversed(message_numbers):
                _, msg_data = mail.fetch(num, "(RFC822)")
                email_body = msg_data[0][1]
                email_message = email.message_from_bytes(email_body)
                
                # Check if the email contains the Steam username in the HTML content
                if contains_username_in_html(email_message, username):
                    logger.info(f"Email found for user {username} with message number {num}")
                    # If we found a matching email, extract the code
                    auth_code = extract_auth_code(email_message)
                    if auth_code:
                        # Mark the email as read
                        mail.store(num, '+FLAGS', '\\Seen')
                        logger.info(f"Auth code {auth_code} extracted and email marked as read")
                        return auth_code  # Return the code from the newest matching email

            # If no matching email found, wait for a short time before checking again
            logger.info("No matching email found, waiting for 3 seconds before retrying")
            time.sleep(3)

        logger.warning(f"No matching emails found for user {username} within timeout period")
        return None  # No matching emails found within REQUEST_TIMEOUT

    except imaplib.IMAP4.error as e:
        logger.error(f"IMAP4 error: {str(e)}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return None
    finally:
        # Close the connection and logout
        try:
            mail.close()
            logger.info("Mailbox closed successfully")
        except Exception as e:
            logger.warning(f"Error closing mailbox: {str(e)}")
        mail.logout()
        logger.info("Logged out from email server")

def contains_username_in_html(email_message, username):
    for part in email_message.walk():
        if part.get_content_type() == "text/html":
            html_body = part.get_payload(decode=True).decode()
            pattern = f'<span style="color: ?#77b9ee;">{re.escape(username)},?</span>'
            if re.search(pattern, html_body, re.IGNORECASE):
                logger.info(f"Username {username} found in email HTML content")
                return True
    return False

def extract_auth_code(email_message):
    for part in email_message.walk():
        if part.get_content_type() == "text/html":
            html_body = part.get_payload(decode=True).decode()
            match = re.search(r'<td[^>]*class="[^"]*title-48[^"]*"[^>]*>\s*([A-Z0-9]+)\s*</td>', html_body, re.IGNORECASE)
            if match:
                auth_code = match.group(1)
                logger.info(f"Auth code {auth_code} extracted from email")
                return auth_code
    logger.warning("Auth code not found in email")
    return None

def get_steam_auth_code_for_user(username):
    logger.info(f"Attempting to get Steam auth code for user: {username}")
    config = load_config()
    email_configs = config.get('EMAIL_CONFIGS', {})
    request_timeout = config.get('REQUEST_TIMEOUT', 30)

    logger.info(f"Email configs: {email_configs}")
    logger.info(f"Request timeout: {request_timeout}")

    if username in email_configs:
        user_config = email_configs[username]
        logger.info(f"User config for {username}: {user_config}")
        auth_code = get_steam_auth_code(user_config['email'], user_config['app_password'], username, request_timeout)
        logger.info(f"Auth code retrieval result for {username}: {'Success' if auth_code else 'Failure'}")
        return auth_code
    else:
        logger.warning(f"No email configuration found for user: {username}")
    return None