{% extends "base.html" %}

{% block title %}VPN Clients Management{% endblock %}
{% block header %}VPN Clients Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="vpnClientsData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Main content -->
        <div class="flex-1">
            <!-- Server and Inbound Selection -->
            <div class="mb-8 bg-white shadow rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Server</label>
                        <select x-model="selectedServer" @change="loadInbounds()"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Choose a server...</option>
                            <template x-for="server in servers" :key="server.domain + server.port">
                                <option :value="JSON.stringify({domain: server.domain, port: server.port})"
                                    x-text="`${server.domain}:${server.port}`"></option>
                            </template>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Inbound</label>
                        <select x-model="selectedInbound" @change="loadClients()"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Choose an inbound...</option>
                            <template x-for="inbound in inbounds" :key="inbound.id">
                                <option :value="inbound.id" x-text="`${inbound.remark} (Port: ${inbound.port})`">
                                </option>
                            </template>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Clients Table -->
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-bold">VPN Clients</h2>
                    <button @click="showAddClientModal = true"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                        Add New Client
                    </button>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Email</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total Traffic</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Expiry Date</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(client, index) in clients" :key="client.id">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="client.email"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="{
                                            'px-2 inline-flex text-xs leading-5 font-semibold rounded-full': true,
                                            'bg-green-100 text-green-800': client.enable,
                                            'bg-red-100 text-red-800': !client.enable
                                        }" x-text="client.enable ? 'Active' : 'Inactive'"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="formatTraffic(client.totalGB)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="formatDate(client.expiryTime)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click="editClient(index)"
                                            class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                                        <button @click="deleteClient(index)"
                                            class="text-red-600 hover:text-red-900 mr-2">Delete</button>
                                        <button @click="showConfigGenerator(client)"
                                            class="text-green-600 hover:text-green-900">Generate Config</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading...</p>
    </div>

    <!-- Add Config Generator Modal -->
    <div x-show="showConfigModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full">
            <h3 class="text-lg font-medium mb-4">Generate Config</h3>

            <!-- Template Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Template</label>
                <div class="flex space-x-2">
                    <select x-model="selectedTemplate"
                        class="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3">
                        <template x-for="template in configTemplates" :key="template.id">
                            <option :value="template.id" x-text="template.name"></option>
                        </template>
                    </select>
                    <button @click="showTemplateManager = true"
                        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Manage Templates
                    </button>
                </div>
            </div>

            <!-- Generated Config -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Generated Config</label>
                <div class="bg-gray-50 p-4 rounded-md">
                    <pre class="whitespace-pre-wrap break-all" x-text="generateConfig()"></pre>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button @click="copyGeneratedConfig()"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    Copy Config
                </button>
                <button @click="closeConfigModal()"
                    class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Template Manager Modal -->
    <div x-show="showTemplateManager" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-3xl w-full">
            <h3 class="text-lg font-medium mb-4">Manage Config Templates</h3>

            <!-- Template List -->
            <div class="mb-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2">Name</th>
                                <th class="px-4 py-2">Template</th>
                                <th class="px-4 py-2">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <template x-for="template in configTemplates" :key="template.id">
                                <tr>
                                    <td class="px-4 py-2" x-text="template.name"></td>
                                    <td class="px-4 py-2" x-text="template.template"></td>
                                    <td class="px-4 py-2">
                                        <button @click="editTemplate(template)"
                                            class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                                        <button @click="deleteTemplate(template.id)"
                                            class="text-red-600 hover:text-red-900">Delete</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Add/Edit Template Form -->
            <div class="mb-4">
                <h4 class="text-md font-medium mb-2" x-text="editingTemplate ? 'Edit Template' : 'Add New Template'">
                </h4>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Template Name</label>
                        <input type="text" x-model="templateForm.name"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Template String</label>
                        <textarea x-model="templateForm.template" rows="3"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                            placeholder="Example: vless://{uuid}@{server}:{port}?encryption=none&type=ws&path={path}#{identity}"></textarea>
                    </div>
                    <button @click="saveTemplate()"
                        class="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Save Template
                    </button>
                </div>
            </div>

            <div class="flex justify-end">
                <button @click="closeTemplateManager()"
                    class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Add/Edit Client Modal -->
    <div x-show="showAddClientModal || editingClient !== null"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 class="text-lg font-medium mb-4" x-text="editingClient !== null ? 'Edit Client' : 'Add New Client'">
            </h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" x-model="clientForm.email"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Total Traffic (GB)</label>
                    <input type="number" x-model="clientForm.totalGB"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3">
                </div>
                <!-- Updated Expiry Date section -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Expiry Date</label>
                    <div class="flex space-x-2">
                        <div class="flex-1">
                            <input type="datetime-local" x-model="clientForm.expiryTime"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3">
                        </div>
                        <div class="flex-1">
                            <div class="flex space-x-2">
                                <input type="number" x-model="daysToAdd" @input="calculateExpiryFromDays()"
                                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                                    placeholder="Days">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" x-model="clientForm.enable" class="h-4 w-4 text-indigo-600">
                    <label class="ml-2 block text-sm text-gray-900">Active</label>
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button @click="closeClientModal"
                    class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </button>
                <button @click="saveClient"
                    class="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function vpnClientsData() {
        return {
            servers: [],
            inbounds: [],
            clients: [],
            selectedServer: '',
            selectedInbound: '',
            isLoaded: false,
            showAddClientModal: false,
            editingClient: null,
            clientForm: {
                email: '',
                totalGB: 0,
                expiryTime: '',
                enable: true
            },
            showConfigModal: false,
            showTemplateManager: false,
            configTemplates: [],
            selectedTemplate: '',
            editingTemplate: null,
            selectedClient: null,
            templateForm: {
                name: '',
                template: ''
            },
            daysToAdd: '',

            init() {
                this.loadServers();
                this.loadConfigTemplates();
            },
            // Add these new methods
            loadConfigTemplates() {
                fetch('/admin/vpn/config-templates')
                    .then(response => response.json())
                    .then(data => {
                        this.configTemplates = data.templates;
                    });
            },
            calculateExpiryFromDays() {
                if (this.daysToAdd) {
                    const days = parseInt(this.daysToAdd);
                    if (!isNaN(days)) {
                        const date = new Date();
                        date.setDate(date.getDate() + days);
                        // Format the date to match datetime-local input format
                        this.clientForm.expiryTime = date.toISOString().slice(0, 16);
                    }
                }
            },

            showConfigGenerator(client) {
                this.selectedClient = client;
                this.showConfigModal = true;
            },

            closeConfigModal() {
                this.showConfigModal = false;
                this.selectedClient = null;
            },

            generateConfig() {
                if (!this.selectedTemplate || !this.selectedClient) return '';

                const template = this.configTemplates.find(t => t.id === this.selectedTemplate);
                if (!template) return '';

                const server = JSON.parse(this.selectedServer);
                const variables = {
                    uuid: this.selectedClient.id,
                    server: server.domain,
                    port: this.selectedInbound.port,
                    path: this.selectedInbound.streamSettings?.wsSettings?.path || '',
                    identity: this.selectedClient.email
                };

                return template.template.replace(/\{(\w+)\}/g, (match, key) => variables[key] || match);
            },

            copyGeneratedConfig() {
                const config = this.generateConfig();
                navigator.clipboard.writeText(config)
                    .then(() => alert('Config copied to clipboard!'))
                    .catch(err => console.error('Failed to copy:', err));
            },

            editTemplate(template) {
                this.editingTemplate = template.id;
                this.templateForm = { ...template };
            },

            saveTemplate() {
                const template = {
                    ...this.templateForm,
                    id: this.editingTemplate || this.generateRandomId()
                };

                fetch('/admin/vpn/config-templates', {
                    method: this.editingTemplate ? 'PUT' : 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(template)
                })
                    .then(response => response.json())
                    .then(() => {
                        this.loadConfigTemplates();
                        this.templateForm = { name: '', template: '' };
                        this.editingTemplate = null;
                    });
            },

            deleteTemplate(templateId) {
                if (confirm('Are you sure you want to delete this template?')) {
                    fetch(`/admin/vpn/config-templates/${templateId}`, {
                        method: 'DELETE'
                    })
                        .then(() => this.loadConfigTemplates());
                }
            },

            closeTemplateManager() {
                this.showTemplateManager = false;
                this.templateForm = { name: '', template: '' };
                this.editingTemplate = null;
            },

            loadServers() {
                fetch('/admin/vpn/get_servers')
                    .then(response => response.json())
                    .then(data => {
                        this.servers = data.servers;
                        this.isLoaded = true;
                    });
            },

            loadInbounds() {
                if (!this.selectedServer) return;

                const server = JSON.parse(this.selectedServer);
                fetch('/admin/vpn/inbounds/list')
                    .then(response => response.json())
                    .then(data => {
                        this.inbounds = data.obj.filter(inbound =>
                            inbound.server === server.domain &&
                            inbound.serverPort === server.port
                        );
                    });
            },

            loadClients() {
                if (!this.selectedInbound) return;

                const server = JSON.parse(this.selectedServer);
                fetch(`/admin/vpn/inbounds/${this.selectedInbound}?server=${server.domain}&serverPort=${server.port}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const settings = JSON.parse(data.obj.settings);
                            this.clients = settings.clients || [];
                        }
                    });
            },

            formatTraffic(gb) {
                return gb ? `${gb} GB` : 'Unlimited';
            },

            formatDate(timestamp) {
                if (!timestamp) return 'Never';
                return new Date(timestamp).toLocaleDateString();
            },

            closeClientModal() {
                this.showAddClientModal = false;
                this.editingClient = null;
                this.clientForm = {
                    email: '',
                    totalGB: 0,
                    expiryTime: '',
                    enable: true
                };
                this.daysToAdd = ''; // Reset days input when closing
            },
            formatTraffic(bytes) {
                if (!bytes) return 'Unlimited';
                // Convert bytes to GB
                const gb = bytes / (1024 ** 3);
                return `${parseFloat(gb.toFixed(2))} GB`;
            },

            // When loading client data for editing
            editClient(index) {
                this.editingClient = index;
                const client = this.clients[index];
                this.clientForm = {
                    ...client,
                    totalGB: client.totalGB ? (client.totalGB / (1024 ** 3)).toFixed(2) : 0,
                    expiryTime: client.expiryTime ?
                        new Date(client.expiryTime).toISOString().slice(0, 16) : ''
                };
                this.daysToAdd = ''; // Reset days input when editing
            },

            saveClient() {
                const server = JSON.parse(this.selectedServer);
                const clientData = {
                    ...this.clientForm,
                    expiryTime: this.clientForm.expiryTime ?
                        new Date(this.clientForm.expiryTime).toISOString() : '',
                    server: server.domain,
                    serverPort: server.port,
                    inboundId: this.selectedInbound
                };

                if (this.editingClient !== null) {
                    // Update existing client (previous code)
                    const client = this.clients[this.editingClient];
                    fetch(`/admin/vpn/inbounds/updateClient/${client.id}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(clientData)
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                this.loadClients();
                                this.closeClientModal();
                            } else {
                                alert('Failed to update client: ' + data.msg);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Failed to update client');
                        });
                } else {
                    // Add new client
                    fetch('/admin/vpn/inbounds/addClient', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(clientData)
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                this.loadClients(); // Reload the clients list
                                this.closeClientModal();
                            } else {
                                alert('Failed to add client: ' + data.msg);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Failed to add client: ' + error.message);
                        });
                }
            }, generateRandomId() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                    var r = Math.random() * 16 | 0,
                        v = c == 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            },

            deleteClient(index) {
                if (!confirm('Are you sure you want to delete this client?')) {
                    return;
                }

                const server = JSON.parse(this.selectedServer);
                const client = this.clients[index];

                fetch(`/admin/vpn/inbounds/${this.selectedInbound}/delClient/${client.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        server: server.domain,
                        serverPort: server.port
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.loadClients(); // Reload the clients list
                        } else {
                            alert('Failed to delete client: ' + data.msg);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to delete client');
                    });
            },

            updateInbound() {
                const server = JSON.parse(this.selectedServer);
                const inbound = this.inbounds.find(i => i.id === this.selectedInbound);

                if (!inbound) return;

                const settings = JSON.parse(inbound.settings);
                settings.clients = this.clients;
                inbound.settings = JSON.stringify(settings);

                fetch(`/admin/vpn/inbounds/update/${this.selectedInbound}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        ...inbound,
                        server: server.domain,
                        serverPort: server.port
                    })
                });
            },

            copyConfig(client) {
                // Implement config generation and copying logic here
                alert('Config copied to clipboard!');
            }
        }
    }
</script>
{% endblock %}