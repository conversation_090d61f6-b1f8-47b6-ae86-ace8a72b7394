{% extends "base.html" %}

{% block title %}Netflix Accounts Management{% endblock %}
{% block header %}Netflix Accounts Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="netflixAccountsData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Main content -->
        <div class="flex-1">
            <div class="section-content">
                <!-- Account Limit Setting -->
                <div class="mb-8 config-item bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold mb-4">Account Settings</h2>
                    <div class="mb-4">
                        <label for="netflix_account_limit" class="block text-sm font-medium text-gray-700">
                            Netflix Account Limit
                        </label>
                        <div class="mt-1 flex items-center">
                            <input type="number" 
                                   id="netflix_account_limit" 
                                   x-model="config.NETFLIX_ACCOUNT_LIMIT"
                                   class="block w-32 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <span class="ml-2 text-sm text-gray-500">Maximum users allowed per account</span>
                        </div>
                    </div>
                </div>

                <!-- Netflix Accounts Management -->
                <div class="mb-8 config-item bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold mb-4">Netflix Accounts</h2>
                    
                    <!-- Add New Account -->
                    <div class="mb-6">
                        <button @click="addNetflixAccount"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add New Account
                        </button>
                    </div>

                    <!-- Accounts List -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Email
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(email, index) in config.NETFLIX_ACCOUNTS" :key="index">
                                    <tr>
                                        <td class="px-6 py-4">
                                            <input type="email" 
                                                   :value="email"
                                                   @input="updateNetflixEmail(index, $event.target.value)"
                                                   class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 text-center">
                                            <button @click.prevent="removeNetflixAccount(index)"
                                                class="text-red-600 hover:text-red-900">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Sign-in Email Configuration -->
                <div class="mb-8 config-item bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold mb-4">Sign-in Code Email Configuration</h2>
                    <div class="space-y-4">
                        <div>
                            <label for="signin_email" class="block text-sm font-medium text-gray-700">
                                Email Address
                            </label>
                            <input type="email" 
                                   id="signin_email"
                                   x-model="config.NETFLIX_SIGNIN_EMAIL.email"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                   placeholder="Email for receiving Netflix sign-in codes">
                        </div>
                        <div>
                            <label for="signin_password" class="block text-sm font-medium text-gray-700">
                                App Password
                            </label>
                            <input type="password" 
                                   id="signin_password"
                                   x-model="config.NETFLIX_SIGNIN_EMAIL.app_password"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                   placeholder="App password for the sign-in code email">
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="mt-6">
                    <button @click="saveConfig"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                        Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function netflixAccountsData() {
        return {
            config: {},
            isLoaded: false,
            emailChanges: {},
            initialNetflixAccounts: [],
            init() {
                this.loadConfig();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        if (!this.config.NETFLIX_ACCOUNTS) {
                            this.config.NETFLIX_ACCOUNTS = [];
                        }
                        if (!this.config.NETFLIX_SIGNIN_EMAIL) {
                            this.config.NETFLIX_SIGNIN_EMAIL = {
                                email: '',
                                app_password: ''
                            };
                        }
                        this.initialNetflixAccounts = [...this.config.NETFLIX_ACCOUNTS];
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            addNetflixAccount() {
                this.config.NETFLIX_ACCOUNTS.push('');
            },
            removeNetflixAccount(index) {
                if (confirm('Are you sure you want to delete this Netflix account?')) {
                    const removedEmail = this.config.NETFLIX_ACCOUNTS[index];
                    if (this.emailChanges[removedEmail]) {
                        delete this.emailChanges[removedEmail];
                    }
                    this.config.NETFLIX_ACCOUNTS.splice(index, 1);
                }
            },
            updateNetflixEmail(index, newEmail) {
                const initialEmail = this.initialNetflixAccounts[index];
                if (initialEmail !== newEmail) {
                    this.emailChanges[initialEmail] = newEmail;
                } else {
                    delete this.emailChanges[initialEmail];
                }
                this.config.NETFLIX_ACCOUNTS[index] = newEmail;
            },
            updateNetflixSessions() {
                if (Object.keys(this.emailChanges).length === 0) return;

                fetch('/admin/update_netflix_emails', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.emailChanges),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Netflix emails updated successfully');
                        this.initialNetflixAccounts = [...this.config.NETFLIX_ACCOUNTS];
                        this.emailChanges = {};
                    } else {
                        console.error('Failed to update Netflix emails');
                    }
                })
                .catch(error => {
                    console.error('Error updating Netflix emails:', error);
                });
            },
            saveConfig() {
                // Convert numeric fields to numbers
                if (this.config.NETFLIX_ACCOUNT_LIMIT) {
                    this.config.NETFLIX_ACCOUNT_LIMIT = Number(this.config.NETFLIX_ACCOUNT_LIMIT);
                }

                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                .then(response => response.json())
                .then(data => {
                    if (Object.keys(this.emailChanges).length > 0) {
                        this.updateNetflixSessions();
                    }
                    alert(data.message);
                    this.animateSaveButton();
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the configuration.');
                });
            },
            animateInitialLoad() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}