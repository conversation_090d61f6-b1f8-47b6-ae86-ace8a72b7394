<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>MTYB Steam Auth Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            width: 300px;
            padding: 20px;
            background-color: #1E1E2E;
            color: white;
        }

        .container {
            text-align: center;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }

        .feature {
            text-align: left;
            margin-bottom: 10px;
        }

        .feature:before {
            content: '✓';
            color: #FF4081;
            margin-right: 10px;
        }

        .features-list {
            margin-bottom: 15px; /* Spacing for the features group */
        }

        #sendData {
            padding: 12px 20px;
            background-color: #6200EA;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
        }

        #sendData:disabled {
            background-color: #9D46FF;
            cursor: not-allowed;
        }

        .section {
            margin-bottom: 15px;
            background-color: #2E2E3E;
            padding: 10px;
            border-radius: 5px;
        }

        h2 {
            font-size: 16px;
            margin-bottom: 5px;
            color: #FF4081;
        }

        pre {
            background-color: #3E3E4E;
            padding: 10px;
            overflow-x: auto;
            max-height: 100px;
            border-radius: 5px;
            font-size: 12px;
            position: relative;
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;
            /* Internet Explorer 10+ */
        }

        pre::-webkit-scrollbar {
            display: none;
            /* WebKit */
        }

        .copy-icon {
            cursor: pointer;
            border-radius: 3px;
            padding: 2px;
        }

        .copy-icon:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .format-options, .send-options {
            display: flex;
            justify-content: space-around;
            margin-top: 8px;
            font-size: 12px;
        }

        .format-options label, .send-options label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .format-options input, .send-options input {
            margin-right: 5px;
        }

        .setting-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .setting-row label {
            flex: 0 0 30%;
            text-align: left;
        }

        .setting-row input, .setting-row select {
            flex: 0 0 65%;
            padding: 4px;
            border-radius: 3px;
            border: 1px solid #444;
            background-color: #3E3E4E;
            color: white;
            min-width: 0;
        }

        #saveSettings {
            width: 100%;
            margin-top: 8px;
            padding: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        #saveSettings:hover {
            background-color: #45a049;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>MTYB Tool</h1>

        <div class="features-list">
            <div class="feature">Automated Update Credentials</div>
            <div class="feature">Easy to use</div>
            <div class="feature">User Friendly</div>
        </div>
        <div class="section">
            <h2>Connection Settings</h2>
            <div class="setting-row">
                <label for="protocolSelect">Protocol:</label>
                <select id="protocolSelect">
                    <option value="https">HTTPS</option>
                    <option value="http">HTTP</option>
                </select>
            </div>
            <div class="setting-row">
                <label for="domainInput">Domain:</label>
                <input type="text" id="domainInput" placeholder="Enter domain (e.g., 127.0.0.1)">
            </div>
            <div class="setting-row">
                <label for="portInput">Port:</label>
                <input type="text" id="portInput" placeholder="Enter port (e.g., 8000)">
            </div>
            <div class="setting-row">
                <label for="pathInput">Path:</label>
                <input type="text" id="pathInput" placeholder="Enter path (e.g., admin/update_shopee_credentials)">
            </div>
            <button id="saveSettings">Save Settings</button>
        </div>
        <div class="section">
            <h2>Bearer Token <span class="copy-icon" data-target="token">📋</span></h2>
            <pre id="token">Loading...</pre>
        </div>
        <div class="section">
            <h2>Cookies <span class="copy-icon" data-target="cookies">📋</span></h2>
            <pre id="cookies">Loading...</pre>
        </div>

        <div class="section">
            <h2>JSON Cookies <span class="copy-icon" data-target="cookiesJson">📋</span></h2>
            <pre id="cookiesJson">Loading...</pre>
            <div class="format-options">
                <label><input type="radio" name="jsonFormat" value="array" checked> Array Format</label>
                <label><input type="radio" name="jsonFormat" value="object"> Object Format</label>
            </div>
        </div>

        <div class="section">
            <h2>Send Options</h2>
            <div class="send-options">
                <label><input type="radio" name="cookieFormat" value="string"> Send String Format</label>
                <label><input type="radio" name="cookieFormat" value="json" checked> Send JSON Format</label>
            </div>

            <div class="setting-row" style="margin-top: 10px;">
                <label for="payloadStructure">Payload Structure:</label>
                <select id="payloadStructure">
                    <option value="default">Default (bearerToken, cookies)</option>
                    <option value="shopeeapi" selected>ShopeeAPI (AUTHORIZATION_CODE, COOKIE_JSON)</option>
                    <option value="custom">Custom (Edit JSON)</option>
                </select>
            </div>

            <div id="customPayloadContainer" style="display: none; margin-top: 10px;">
                <textarea id="customPayload" rows="5" style="width: 100%; background-color: #3E3E4E; color: white; border: 1px solid #444; border-radius: 3px; padding: 5px; font-family: monospace; font-size: 12px;"></textarea>
                <div style="margin-top: 5px; font-size: 11px; color: #aaa;">
                    Use {{token}} and {{cookies}} as placeholders
                </div>
            </div>
        </div>

        <button id="sendData">Update Credentials</button>
    </div>

    <script src="popup.js"></script>
</body>

</html>