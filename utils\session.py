from requests import Session
from utils.credential_manager import credential_manager

session = Session()
session.headers.update({
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0",
    "Content-Type": "application/json"
})

# Update credentials initially
credentials = credential_manager.get_credentials()
session.headers.update({
    "Authorization": credentials['authorization_code'],
    "Cookie": credentials['cookie']
})

# Subscribe to credential updates
def update_session_credentials(credentials):
    # Always use the string format for Cook<PERSON> header
    session.headers.update({
        "Authorization": credentials['authorization_code'],
        "Cookie": credentials['cookie']
    })

    # Log the update for debugging
    print(f"Updated session headers with Authorization: {credentials['authorization_code'][:15]}... and Cookie: {credentials['cookie'][:15]}...")

credential_manager.subscribe(update_session_credentials)