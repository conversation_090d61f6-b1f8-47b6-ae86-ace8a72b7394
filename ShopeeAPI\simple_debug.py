#!/usr/bin/env python3
"""
Simple debug script to test send_message endpoint via HTTP requests.
"""

import requests
import json

def test_send_message(username: str, message: str = "Test message"):
    """Test the send_message endpoint via HTTP API."""
    print(f"\n=== Testing send_message for username: {username} ===")
    
    # Test via HTTP API
    api_url = "http://localhost:456"  # Adjust port if needed
    endpoint = f"{api_url}/chat/send_message"
    
    payload = {
        "text": message,
        "username": username,
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    
    print(f"Sending POST request to: {endpoint}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(endpoint, json=payload, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            try:
                return response.json()
            except:
                return {"raw_response": response.text}
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}
            
    except requests.exceptions.ConnectionError:
        print("Connection error - is the ShopeeAPI server running on port 456?")
        return {"error": "Connection error - server not running"}
    except requests.exceptions.Timeout:
        print("Request timeout")
        return {"error": "Request timeout"}
    except Exception as e:
        print(f"Exception calling API: {str(e)}")
        return {"error": f"Exception calling API: {str(e)}"}


def test_conversation_search(username: str):
    """Test the conversation search endpoint."""
    print(f"\n=== Testing conversation search for username: {username} ===")
    
    api_url = "http://localhost:456"
    endpoint = f"{api_url}/chat/conversations/search"
    
    params = {"username": username}
    
    print(f"Sending GET request to: {endpoint}")
    print(f"Params: {params}")
    
    try:
        response = requests.get(endpoint, params=params, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            try:
                return response.json()
            except:
                return {"raw_response": response.text}
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}
            
    except requests.exceptions.ConnectionError:
        print("Connection error - is the ShopeeAPI server running on port 456?")
        return {"error": "Connection error - server not running"}
    except Exception as e:
        print(f"Exception calling API: {str(e)}")
        return {"error": f"Exception calling API: {str(e)}"}


def test_recent_conversations():
    """Test the recent conversations endpoint."""
    print(f"\n=== Testing recent conversations ===")
    
    api_url = "http://localhost:456"
    endpoint = f"{api_url}/chat/conversations/recent"
    
    print(f"Sending GET request to: {endpoint}")
    
    try:
        response = requests.get(endpoint, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Found {len(data.get('conversations', []))} conversations")
                
                # Look for our test usernames
                for username in ["me0tn_14qo", "syahmialfabet"]:
                    found = False
                    for conv in data.get('conversations', []):
                        if conv.get('to_name', '').lower() == username.lower():
                            print(f"Found {username}: {conv}")
                            found = True
                            break
                    if not found:
                        print(f"{username} NOT found in recent conversations")
                
                return data
            except:
                return {"raw_response": response.text}
        else:
            return {"error": f"HTTP {response.status_code}: {response.text}"}
            
    except requests.exceptions.ConnectionError:
        print("Connection error - is the ShopeeAPI server running on port 456?")
        return {"error": "Connection error - server not running"}
    except Exception as e:
        print(f"Exception calling API: {str(e)}")
        return {"error": f"Exception calling API: {str(e)}"}


def main():
    """Main debug function."""
    print("=== Simple ShopeeAPI Debug Script ===")
    
    # Test server connectivity first
    try:
        response = requests.get("http://localhost:456/docs", timeout=5)
        print(f"Server is running - docs endpoint returned {response.status_code}")
    except:
        print("ERROR: ShopeeAPI server is not running on port 456!")
        print("Please start the server first with: python api.py")
        return
    
    # Test recent conversations first
    test_recent_conversations()
    
    # Test conversation search for both usernames
    for username in ["me0tn_14qo", "syahmialfabet"]:
        test_conversation_search(username)
    
    # Test send_message for both usernames
    for username in ["me0tn_14qo", "syahmialfabet"]:
        test_send_message(username, f"Debug test message for {username}")
        print("-" * 50)


if __name__ == "__main__":
    main()
