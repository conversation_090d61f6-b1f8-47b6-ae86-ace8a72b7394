{% extends "base.html" %}

{% block title %}VPN Servers Management{% endblock %}
{% block header %}VPN Servers Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="vpnServersData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Main content -->
        <div class="flex-1">
            <!-- Add New Server Form -->
            <div class="mb-8 bg-white shadow rounded-lg p-6">
                <h2 class="text-2xl font-bold mb-4">Add New VPN Server</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="config-item">
                        <label for="server_domain" class="block text-sm font-medium text-gray-700">Server Domain</label>
                        <input id="server_domain" type="text" x-model="newServer.domain"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="example.com">
                    </div>
                    <div class="config-item">
                        <label for="server_port" class="block text-sm font-medium text-gray-700">Server Port</label>
                        <input id="server_port" type="number" x-model="newServer.port"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="2053">
                    </div>
                    <div class="config-item">
                        <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                        <input id="username" type="text" x-model="newServer.username"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="admin">
                    </div>
                    <div class="config-item">
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input id="password" type="password" x-model="newServer.password"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="••••••••">
                    </div>
                </div>
                <button @click="addServer"
                    class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Add Server
                </button>
            </div>

            <!-- Servers Table -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-2xl font-bold mb-4">VPN Servers</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Domain</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Port</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Username</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(server, index) in servers" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="server.domain"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="server.port"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="server.username"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span x-show="server.status === 'connected'"
                                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Connected
                                        </span>
                                        <span x-show="server.status === 'disconnected'"
                                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            Disconnected
                                        </span>
                                        <span x-show="server.status === 'checking'"
                                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Checking...
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click="editServer(index)"
                                            class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                                        <button @click="deleteServer(index)"
                                            class="text-red-600 hover:text-red-900 mr-2">Delete</button>
                                        <button @click="checkConnection(index)"
                                            class="text-green-600 hover:text-green-900">Check</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading VPN servers...</p>
    </div>

    <!-- Edit Modal -->
    <div x-show="editingServer !== null"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 class="text-lg font-medium mb-4">Edit Server</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Domain</label>
                    <input type="text" x-model="servers[editingServer].domain"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Port</label>
                    <input type="number" x-model="servers[editingServer].port"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Username</label>
                    <input type="text" x-model="servers[editingServer].username"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" x-model="servers[editingServer].password"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button @click="editingServer = null"
                    class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button @click="saveEdit"
                    class="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function vpnServersData() {
        return {
            servers: [],
            newServer: {
                domain: '',
                port: '',
                username: '',
                password: '',
                status: 'disconnected'
            },
            editingServer: null,
            isLoaded: false,
            init() {
                this.loadServers().then(() => {
                    // After servers are loaded, check all connections
                    this.servers.forEach((_, index) => {
                        this.checkConnection(index);
                    });
                });
                this.animateInitialLoad();
            },
            loadServers() {
                return fetch('/admin/vpn/get_servers')
                    .then(response => response.json())
                    .then(data => {
                        this.servers = data.servers;
                        this.isLoaded = true;
                    })
                    .catch(error => {
                        console.error('Error loading servers:', error);
                        alert('Failed to load VPN servers. Please try refreshing the page.');
                    });
            },
            addServer() {
                if (!this.newServer.domain || !this.newServer.port || !this.newServer.username || !this.newServer.password) {
                    alert('All fields are required');
                    return;
                }

                fetch('/admin/vpn/servers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.newServer),
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.servers.push({ ...this.newServer });
                            this.newServer = {
                                domain: '',
                                port: '',
                                username: '',
                                password: '',
                                status: 'disconnected'
                            };
                            this.animateNewRow();
                        } else {
                            alert(data.message || 'Failed to add server');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to add server');
                    });
            },
            editServer(index) {
                this.editingServer = index;
            },
            saveEdit() {
                const server = this.servers[this.editingServer];
                fetch(`/admin/vpn/servers/${this.editingServer}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(server),
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.editingServer = null;
                            this.animateRow(this.editingServer);
                        } else {
                            alert(data.message || 'Failed to update server');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to update server');
                    });
            },
            deleteServer(index) {
                if (confirm('Are you sure you want to delete this server?')) {
                    fetch(`/admin/vpn/servers/${index}`, {
                        method: 'DELETE',
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                this.servers.splice(index, 1);
                            } else {
                                alert(data.message || 'Failed to delete server');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Failed to delete server');
                        });
                }
            },
            checkConnection(index) {
                const server = this.servers[index];
                server.status = 'checking';

                fetch('/admin/vpn/check-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        ...server,
                        server_index: index
                    }),
                })
                    .then(response => response.json())
                    .then(data => {
                        server.status = data.success ? 'connected' : 'disconnected';
                        this.animateStatusChange(index);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        server.status = 'disconnected';
                        this.animateStatusChange(index);
                    });
            },
            animateInitialLoad() {
                anime({
                    targets: 'table thead th',
                    translateY: [-20, 0],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutQuad'
                });

                anime({
                    targets: 'table tbody tr',
                    translateX: [-50, 0],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutQuad'
                });
            },
            animateNewRow() {
                const lastRow = document.querySelector('table tbody tr:last-child');
                anime({
                    targets: lastRow,
                    translateX: [-50, 0],
                    opacity: [0, 1],
                    easing: 'easeOutQuad'
                });
            },
            animateRow(index) {
                const row = document.querySelector(`table tbody tr:nth-child(${index + 1})`);
                anime({
                    targets: row,
                    scale: [1, 1.02, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            },
            animateStatusChange(index) {
                const statusCell = document.querySelector(`table tbody tr:nth-child(${index + 1}) td:nth-child(4)`);
                anime({
                    targets: statusCell,
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}