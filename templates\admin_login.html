<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin <PERSON>gin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='spinkit.css') }}">
    <style>
        .modal-enter {
            opacity: 0;
            transform: scale(0.9);
        }
        .modal-enter-active {
            opacity: 1;
            transform: scale(1);
            transition: opacity 300ms, transform 300ms;
        }
        .modal-exit {
            opacity: 1;
        }
        .modal-exit-active {
            opacity: 0;
            transform: scale(0.9);
            transition: opacity 300ms, transform 300ms;
        }
        .floating-label-input {
            position: relative;
        }
        .floating-label-input input {
            height: 3rem;
            padding-top: 1rem;
        }
        .floating-label-input label {
            position: absolute;
            top: 0.5rem;
            left: 0.75rem;
            transition: all 0.2s ease-out;
            pointer-events: none;
        }
        .floating-label-input input:focus + label,
        .floating-label-input input:not(:placeholder-shown) + label {
            font-size: 0.75rem;
            top: 0;
        }
        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            transform: rotate(180deg);
        }

        .wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 150px;
        }

        .wave .shape-fill {
            fill: #FFFFFF;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 min-h-screen flex items-center justify-center relative">
    <div class="wave">
        <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
        </svg>
    </div>
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
        <h1 class="text-2xl font-bold text-purple-800 mb-6">Admin Login</h1>
        <form id="loginForm" class="space-y-4">
            <div class="floating-label-input">
                <input type="text" id="username" name="username" required class="px-3 mt-1 block w-full rounded-md border-purple-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 placeholder-transparent">
                <label for="username" class="text-sm font-medium text-purple-700">Username</label>
            </div>
            <div class="floating-label-input">
                <input type="password" id="password" name="password" required class="px-3 mt-1 block w-full rounded-md border-purple-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 placeholder-transparent">
                <label for="password" class="text-sm font-medium text-purple-700">Password</label>
            </div>
            <button type="submit" class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
                Login
            </button>
        </form>
    </div>

    <footer class="fixed bottom-0 w-full text-center py-2 bg-white bg-opacity-80 text-purple-800 z-20">
        &copy; 2024 Copyright. Powered by MTYB Official
    </footer>

    <!-- Modal -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" onclick="handleModalClick(event)">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div id="modalContent" class="mt-3 text-center">
                <div id="loadingIndicator" class="mx-auto flex items-center justify-center h-12 w-12">
                    <div class="sk-wave sk-primary">
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                    </div>
                </div>
                <div id="successIndicator" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 hidden">
                    <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div id="errorIndicator" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 hidden">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2" id="modalTitle">Loading</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500" id="modalMessage">
                        Authenticating...
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('loginForm');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const successIndicator = document.getElementById('successIndicator');
        const errorIndicator = document.getElementById('errorIndicator');

        function showModal() {
            modal.classList.remove('hidden');
            modal.classList.add('modal-enter');
            setTimeout(() => {
                modal.classList.remove('modal-enter');
                modal.classList.add('modal-enter-active');
            }, 10);
        }

        function hideModal() {
            modal.classList.add('modal-exit');
            setTimeout(() => {
                modal.classList.add('modal-exit-active');
                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.classList.remove('modal-exit', 'modal-exit-active');
                }, 300);
            }, 10);
        }
        
        function handleModalClick(event) {
            if (event.target === modal) {
                hideModal();
            }
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            showModal();
            loadingIndicator.classList.remove('hidden');
            successIndicator.classList.add('hidden');
            errorIndicator.classList.add('hidden');
            modalTitle.textContent = 'Authenticating';
            modalMessage.textContent = 'Please wait...';

            try {
                const response = await axios.post('/admin/login', {
                    username: username,
                    password: password
                });

                loadingIndicator.classList.add('hidden');
                successIndicator.classList.remove('hidden');
                modalTitle.textContent = 'Success';
                modalMessage.textContent = 'Login successful. Redirecting...';

                // Redirect to admin dashboard after successful login
                setTimeout(() => {
                    window.location.href = '/admin/dashboard';
                }, 1500);
            } catch (error) {
                loadingIndicator.classList.add('hidden');
                errorIndicator.classList.remove('hidden');
                modalTitle.textContent = 'Error';
                modalMessage.textContent = error.response?.data?.error || 'Login failed. Please try again.';
            }
        });
    </script>
</body>
</html>