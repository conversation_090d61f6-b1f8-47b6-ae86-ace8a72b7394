#!/bin/bash
# Docker deployment script for ShopeeAPI

# Configuration
SERVER_HOST="your-server-hostname"  # Change to your server hostname or IP
SERVER_USER="your-username"         # Change to your server username
SERVER_PATH="/path/to/shopee-api"   # Change to your server deployment path
IMAGE_NAME="shopee-api"
CONTAINER_NAME="shopee-api"

# Parse command line arguments
BUILD=false
PUSH=false
DEPLOY=false
RESTART=false
ALL=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --build)
      BUILD=true
      shift
      ;;
    --push)
      PUSH=true
      shift
      ;;
    --deploy)
      DEPLOY=true
      shift
      ;;
    --restart)
      RESTART=true
      shift
      ;;
    --all)
      ALL=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--build] [--push] [--deploy] [--restart] [--all]"
      exit 1
      ;;
  esac
done

# If no arguments are provided, show help
if [[ "$BUILD" == "false" && "$PUSH" == "false" && "$DEPLOY" == "false" && "$RESTART" == "false" && "$ALL" == "false" ]]; then
  echo "Usage: $0 [--build] [--push] [--deploy] [--restart] [--all]"
  exit 0
fi

# Set version based on date and time
VERSION=$(date +"%Y.%m.%d.%H%M")
echo "Using version: $VERSION"

# Build the Docker image
if [[ "$BUILD" == "true" || "$ALL" == "true" ]]; then
  echo "Building Docker image..."
  docker build -t $IMAGE_NAME:$VERSION -t $IMAGE_NAME:latest -f ShopeeAPI/Dockerfile .
  echo "Docker image built: $IMAGE_NAME:$VERSION"
fi

# Push the Docker image to a registry (optional)
if [[ "$PUSH" == "true" || "$ALL" == "true" ]]; then
  echo "Pushing Docker image to registry..."
  # Uncomment and modify the following lines if you're using a private registry
  # docker tag $IMAGE_NAME:$VERSION your-registry.com/$IMAGE_NAME:$VERSION
  # docker tag $IMAGE_NAME:$VERSION your-registry.com/$IMAGE_NAME:latest
  # docker push your-registry.com/$IMAGE_NAME:$VERSION
  # docker push your-registry.com/$IMAGE_NAME:latest
  
  # For now, we'll save the image to a tar file and upload it to the server
  echo "Saving Docker image to a tar file..."
  docker save $IMAGE_NAME:$VERSION | gzip > $IMAGE_NAME-$VERSION.tar.gz
  
  echo "Uploading Docker image to server..."
  scp $IMAGE_NAME-$VERSION.tar.gz $SERVER_USER@$SERVER_HOST:$SERVER_PATH/
  
  # Clean up the local tar file
  rm $IMAGE_NAME-$VERSION.tar.gz
  
  echo "Docker image uploaded to server"
fi

# Deploy the Docker image on the server
if [[ "$DEPLOY" == "true" || "$ALL" == "true" ]]; then
  echo "Deploying Docker image on server..."
  
  # Create a deployment script
  cat > deploy-remote.sh << EOL
#!/bin/bash
# Remote deployment script

# Load the Docker image
if [ -f "$IMAGE_NAME-$VERSION.tar.gz" ]; then
  echo "Loading Docker image..."
  docker load < $IMAGE_NAME-$VERSION.tar.gz
  rm $IMAGE_NAME-$VERSION.tar.gz
fi

# Update the docker-compose.yml file to use the new image
sed -i "s|image: $IMAGE_NAME:.*|image: $IMAGE_NAME:$VERSION|g" docker-compose.yml

# Stop and remove the existing container
docker-compose down

# Start the new container
docker-compose up -d

echo "Deployment completed successfully!"
EOL
  
  # Make the script executable
  chmod +x deploy-remote.sh
  
  # Upload the script to the server
  scp deploy-remote.sh $SERVER_USER@$SERVER_HOST:$SERVER_PATH/
  
  # Execute the script on the server
  ssh $SERVER_USER@$SERVER_HOST "cd $SERVER_PATH && ./deploy-remote.sh"
  
  # Clean up the local script
  rm deploy-remote.sh
  
  echo "Docker image deployed on server"
fi

# Restart the container on the server
if [[ "$RESTART" == "true" || "$ALL" == "true" ]]; then
  echo "Restarting container on server..."
  ssh $SERVER_USER@$SERVER_HOST "cd $SERVER_PATH && docker-compose restart"
  echo "Container restarted on server"
fi

echo "Deployment process completed!"
