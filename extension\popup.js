
function checkAllDataCaptured() {
    chrome.storage.local.get(['bearerToken', 'fullCookieText'], (result) => {
        const token = result.bearerToken;
        const cookies = result.fullCookieText;
        if (token && cookies) {
            document.getElementById('sendData').textContent = 'Credentials Auto Updated';
            sendDataToServer(token, cookies);
        }
    });
}

chrome.storage.local.get(['bearerToken'], (result) => {
    if (chrome.runtime.lastError) {
        console.error('Error fetching bearerToken:', chrome.runtime.lastError);
        document.getElementById('token').textContent = 'Failed to retrieve';
    } else {
        document.getElementById('token').textContent = result.bearerToken || 'Bearer Token not found';
    }
    enableSendButtonIfPossible();
});

chrome.storage.local.get(['fullCookieText'], (result) => {
    if (chrome.runtime.lastError) {
        console.error('Error fetching fullCookieText:', chrome.runtime.lastError);
        document.getElementById('cookies').textContent = 'Failed to retrieve';
    } else {
        document.getElementById('cookies').textContent = result.fullCookieText || 'Cookies not found';
    }
    enableSendButtonIfPossible();
});

function saveDomain() {
    const domain = document.getElementById('domainInput').value.trim();
    if (domain) {
        chrome.storage.local.set({ domain: domain }, () => {
            alert('Domain saved successfully!');
            document.getElementById('domainInput').value = domain;
        });
    } else {
        alert('Please enter a valid domain.');
    }
}

function loadSavedDomain() {
    chrome.storage.local.get(['domain'], (result) => {
        if (result.domain) {
            document.getElementById('domainInput').value = result.domain;
        }
    });
}

function enableSendButtonIfPossible() {
    const token = document.getElementById('token').textContent;
    const cookies = document.getElementById('cookies').textContent;
    const sendButton = document.getElementById('sendData');
    if (token && token !== 'Loading...' && cookies && cookies !== 'Loading...') {
        sendButton.disabled = false;
    }
}

function sendDataToServer(token, cookies) {
    chrome.storage.local.get(['domain'], (result) => {
        const domain = result.domain || 'steam.online-mtyb.com';
        const data = {
            bearerToken: token,
            cookies: cookies
        };

        fetch(`https://${domain}/admin/update_shopee_credentials`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Server responded with status: ' + response.status);
            }
        })
        .then(data => {
            alert(data.message);
            document.getElementById('sendData').disabled = true;
        })
        .catch(error => {
            alert('An error occurred during sending.');
            console.error('Error sending data:', error);
        });
    });
}

document.addEventListener('DOMContentLoaded', function() {
    checkAllDataCaptured();
    loadSavedDomain();
    document.querySelectorAll('.copy-icon').forEach(icon => {
        icon.addEventListener('click', function() {
            const elementId = this.getAttribute('data-target');
            copyToClipboard(elementId);
        });
    });

    document.getElementById('saveDomain').addEventListener('click', saveDomain);
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    const tempTextArea = document.createElement('textarea');
    tempTextArea.value = text;
    document.body.appendChild(tempTextArea);
    
    tempTextArea.select();
    document.execCommand('copy');
    
    document.body.removeChild(tempTextArea);
    
    element.style.backgroundColor = '#4CAF50';
    setTimeout(() => {
        element.style.backgroundColor = '';
    }, 200);
    
    const message = document.createElement('div');
    message.textContent = 'Copied!';
    message.style.position = 'fixed';
    message.style.top = '10px';
    message.style.left = '50%';
    message.style.transform = 'translateX(-50%)';
    message.style.backgroundColor = '#333';
    message.style.color = '#fff';
    message.style.padding = '10px';
    message.style.borderRadius = '5px';
    message.style.zIndex = '1000';
    document.body.appendChild(message);
    
    setTimeout(() => {
        document.body.removeChild(message);
    }, 2000);
}