{% extends "base.html" %}

{% block title %}Stock Management{% endblock %}
{% block header %}Stock Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="stockConfigData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Sidebar -->
        <div class="w-64 mr-8">
            <nav class="space-y-1">
                <template x-for="(section, index) in ['Self Redeem Text SKUs', 'Manual Orders']" :key="index">
                    <a href="#" @click.prevent="currentSection = section; animateSection()"
                        :class="{'bg-gray-100 text-gray-900': currentSection === section, 'text-gray-600 hover:bg-gray-50 hover:text-gray-900': currentSection !== section}"
                        class="group flex items-center px-3 py-2 text-sm font-medium rounded-md sidebar-item">
                        <span x-text="section"></span>
                    </a>
                </template>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1">
            <!-- Self Redeem Text SKUs Section -->
            <div x-show="currentSection === 'Self Redeem Text SKUs'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Self Redeem Text SKUs Management</h2>

                <!-- Add New SKU -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Add New SKU</h3>
                    <div class="config-item">
                        <label for="new_sku" class="block text-sm font-medium text-gray-700">SKU</label>
                        <input id="new_sku" name="new_sku" type="text" x-model="newTextSku.sku"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div class="config-item mt-4">
                        <label for="new_message" class="block text-sm font-medium text-gray-700">Redeem Message
                            Template</label>
                        <textarea id="new_message" name="new_message" x-model="newTextSku.message"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            rows="3" placeholder="Use {result} to include the stock item"></textarea>
                    </div>
                    <div class="config-item mt-4 flex items-center">
                        <input type="checkbox" id="new_is_unlimited" x-model="newTextSku.is_unlimited_stock"
                            class="mr-2">
                        <label for="new_is_unlimited" class="text-sm font-medium text-gray-700">Is Unlimited
                            Stock?</label>
                    </div>
                    <button @click="addTextSku"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Add SKU
                    </button>
                </div>

                <!-- Existing SKUs -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Existing SKUs</h3>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                    SKU</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                                    Message</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                    ♾️ Stock</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                    Stock Level</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(sku, index) in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" x-model="sku.sku"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <textarea x-model="sku.message"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            rows="2"></textarea>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" x-model="sku.is_unlimited_stock" class="mr-2">
                                    </td>
                                    <td class="whitespace-nowrap" x-show="!sku.is_unlimited_stock">
                                        <input type="number" x-model="sku.stock_level" min="0"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            placeholder="0 to disable">
                                    </td>
                                    <td class="whitespace-nowrap" x-show="sku.is_unlimited_stock">
                                        <span class="text-gray-500">N/A</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click.prevent="deleteTextSku(index)"
                                            class="text-red-600 hover:text-red-900">Delete</button>
                                        <button x-show="!sku.is_unlimited_stock" @click="selectTextSku(sku)"
                                            class="ml-2 text-blue-600 hover:text-blue-900">Stock</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- Stock Management Modal -->
                <div class="mb-6" x-show="selectedTextSku !== null && !selectedTextSku.is_unlimited_stock">
                    <h3 class="text-xl font-semibold mb-2">Manage Stock for SKU: <span
                            x-text="selectedTextSku.sku"></span></h3>
                    <div>
                        <div class="mb-4">
                            <label for="new_stock_item" class="block text-sm font-medium text-gray-700">Add New Stock
                                Items</label>
                            <textarea id="new_stock_item" name="new_stock_item" x-model="newStockItem"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                rows="5" placeholder="Enter one item per line"></textarea>
                            <button @click="addStockItems"
                                class="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Add Stock Items
                            </button>
                        </div>

                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stock Item</th>
                                    <th
                                        class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(stock, sIndex) in getStockItems(selectedTextSku.sku)" :key="sIndex">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" x-model="stock"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click.prevent="deleteStockItem(selectedTextSku.sku, sIndex)"
                                                class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>

                    <button @click="selectedTextSku = null"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        Close
                    </button>
                </div>
            </div>

            <!-- Manual Orders Section -->
            <div x-show="currentSection === 'Manual Orders'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Manual Order Management</h2>

                <!-- Create Manual Order Form -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Create Manual Order</h3>
                    <div class="config-item">
                        <label for="manual_order_sn" class="block text-sm font-medium text-gray-700">Order SN</label>
                        <input id="manual_order_sn" name="manual_order_sn" type="text" x-model="manualOrder.order_sn"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div class="config-item mt-4">
                        <label for="manual_var_sku" class="block text-sm font-medium text-gray-700">VAR SKU</label>
                        <input id="manual_var_sku" name="manual_var_sku" type="text" x-model="manualOrder.var_sku"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div class="config-item mt-4">
                        <label for="new_stock_level" class="block text-sm font-medium text-gray-700">Stock Level
                            Alert</label>
                        <input type="number" id="new_stock_level" x-model="newTextSku.stock_level" min="0"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="0 to disable" x-bind:disabled="newTextSku.is_unlimited_stock">
                        <p class="mt-1 text-sm text-gray-500">Set the quantity threshold for email notifications. Set to
                            0 to disable alerts.</p>
                    </div>
                    <button @click="createManualOrder"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Create Manual Order
                    </button>
                </div>

                <!-- Manual Orders List -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Manual Orders</h3>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Order SN</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    VAR SKU</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(order, index) in manualOrders" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.order_sn"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.var_sku"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click.prevent="deleteManualOrder(index)"
                                            class="text-red-600 hover:text-red-900">Delete</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="mt-4">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function stockConfigData() {
        return {
            config: {},
            currentSection: 'Self Redeem Text SKUs',
            isLoaded: false,
            newTextSku: {
                sku: '',
                message: '',
                is_unlimited_stock: false,
                stock_level: 0
            },
            selectedTextSku: null,
            newStockItem: '',
            manualOrder: {
                order_sn: '',
                var_sku: ''
            },
            manualOrders: [],
            init() {
                this.loadConfig();
                this.loadManualOrders();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY) {
                            this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY = [];
                        }
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            loadManualOrders() {
                fetch('/admin/get_manual_orders')
                    .then(response => response.json())
                    .then(data => {
                        this.manualOrders = data;
                    })
                    .catch(error => {
                        console.error('Error loading manual orders:', error);
                    });
            },
            // Text SKU Management
            addTextSku() {
                if (this.newTextSku.sku.trim() === '') {
                    alert('SKU cannot be empty.');
                    return;
                }
                this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.push({
                    sku: this.newTextSku.sku.trim(),
                    message: this.newTextSku.message.trim(),
                    is_unlimited_stock: this.newTextSku.is_unlimited_stock,
                    stock_level: this.newTextSku.is_unlimited_stock ? 0 : parseInt(this.newTextSku.stock_level) || 0
                });
                if (!this.newTextSku.is_unlimited_stock) {
                    if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                    }
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push({
                        sku: this.newTextSku.sku.trim(),
                        stock: []
                    });
                }
                this.newTextSku = { sku: '', message: '', is_unlimited_stock: false };
            },
            deleteTextSku(index) {
                if (confirm('Are you sure you want to delete this SKU?')) {
                    const sku = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY[index];
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.splice(index, 1);
                    if (!sku.is_unlimited_stock && this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        const stockIndex = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.findIndex(s => s.sku === sku.sku);
                        if (stockIndex !== -1) {
                            this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.splice(stockIndex, 1);
                        }
                    }
                }
            },
            selectTextSku(sku) {
                this.selectedTextSku = sku;
            },
            getStockItems(sku) {
                const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === sku);
                return stockEntry ? stockEntry.stock : [];
            },
            addStockItems() {
                if (this.newStockItem.trim() === '') {
                    alert('Stock items cannot be empty.');
                    return;
                }
                if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                }
                let stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === this.selectedTextSku.sku);
                if (!stockEntry) {
                    stockEntry = { sku: this.selectedTextSku.sku, stock: [] };
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push(stockEntry);
                }
                const newItems = this.newStockItem.split('\n').map(item => item.trim()).filter(item => item !== '');
                stockEntry.stock.push(...newItems);
                this.newStockItem = '';
                this.config = { ...this.config };
            },
            deleteStockItem(sku, index) {
                const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === sku);
                if (stockEntry) {
                    stockEntry.stock.splice(index, 1);
                }
            },
            // Manual Orders Management
            createManualOrder() {
                if (this.manualOrder.order_sn.trim() === '' || this.manualOrder.var_sku.trim() === '') {
                    alert('Order SN and VAR SKU cannot be empty.');
                    return;
                }
                this.manualOrders.push({ ...this.manualOrder });
                this.manualOrder = { order_sn: '', var_sku: '' };
                this.saveManualOrders();
            },
            deleteManualOrder(index) {
                if (confirm('Are you sure you want to delete this manual order?')) {
                    this.manualOrders.splice(index, 1);
                    this.saveManualOrders();
                }
            },
            saveManualOrders() {
                fetch('/admin/update_manual_orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.manualOrders),
                })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                        alert('An error occurred while saving manual orders.');
                    });
            },
            saveConfig() {
                this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.forEach(sku => {
                    sku.stock_level = parseInt(sku.stock_level) || 0;
                });
                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        this.animateSaveButton();
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                        alert('An error occurred while saving the configuration.');
                    });
            },
            animateInitialLoad() {
                anime({
                    targets: '.sidebar-item',
                    translateX: [-50, 0],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutQuad'
                });
                this.animateSection();
            },
            animateSection() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });
                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}