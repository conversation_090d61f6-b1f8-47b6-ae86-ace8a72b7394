"""
Common data models used across the application.
"""
from typing import Dict, Any, Optional
from pydantic import BaseModel


class Credentials(BaseModel):
    """Model for API credentials."""
    authorization_code: str
    cookie: str


class Config(BaseModel):
    """Model for API configuration."""
    shop_id: Optional[int] = None
    region_id: Optional[str] = None


class APIResponse(BaseModel):
    """Base model for API responses."""
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: int = 200
