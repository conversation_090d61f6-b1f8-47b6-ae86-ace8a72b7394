#!/usr/bin/env python3
"""
Detailed debug script to understand why syahmialfabet is not found.
"""

import requests
import json
import sys
import os

# Add the parent directory to the path so we can import ShopeeAPI modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ShopeeAPI.client import Shopee<PERSON><PERSON>
from ShopeeAPI.config import Config


def test_conversation_search_direct(username: str):
    """Test the conversation search API directly using ShopeeAPI client."""
    print(f"\n=== Direct API Test: Conversation search for username: {username} ===")
    
    try:
        # Initialize ShopeeAPI
        config = Config()
        api = ShopeeAPI(config)
        
        # Test the search directly
        from urllib.parse import quote
        
        # Prepare the params for the conversation search request
        params = {
            "per_page": "20",
            "keyword": username,
            "type": "3",
            "_uid": f"0-{api.config.shop_id}",
            "_v": "8.5.6",
            "csrf_token": quote(api.session.credential_manager.get_csrf_token()),
            "SPC_CDS_CHAT": api.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": api.config.region_id
        }
        
        print(f"Search URL: {api.config.urls['conversation_search']}")
        print(f"Search params: {params}")
        
        # Send the GET request to search for conversation info
        response = api.session.get(
            api.config.urls["conversation_search"],
            params=params
        )
        
        print(f"Search response status: {response.status_code}")
        print(f"Search response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            search_results = response.json()
            print(f"Search results: {json.dumps(search_results, indent=2, ensure_ascii=False)}")
            
            # Analyze the search results
            if 'data' in search_results:
                data = search_results['data']
                print(f"\nData structure keys: {list(data.keys())}")
                
                if 'users' in data:
                    print(f"Found {len(data['users'])} users:")
                    for i, user in enumerate(data['users']):
                        print(f"  User {i}: {user}")
                        if user.get('username', '').lower() == username.lower():
                            print(f"    *** MATCH FOUND for {username} ***")
                
                if 'orders' in data:
                    print(f"Found {len(data['orders'])} orders:")
                    for i, order in enumerate(data['orders']):
                        print(f"  Order {i}: {order}")
                        if order.get('username', '').lower() == username.lower():
                            print(f"    *** MATCH FOUND for {username} in orders ***")
                
                if 'shops' in data:
                    print(f"Found {len(data['shops'])} shops:")
                    for i, shop in enumerate(data['shops']):
                        print(f"  Shop {i}: {shop}")
                        if shop.get('username', '').lower() == username.lower():
                            print(f"    *** MATCH FOUND for {username} in shops ***")
            
            return search_results
        else:
            print(f"Search failed with status {response.status_code}: {response.text}")
            return {"error": f"Search failed with status {response.status_code}"}
            
    except Exception as e:
        print(f"Exception during direct search: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"error": f"Exception during search: {str(e)}"}


def test_recent_conversations_direct():
    """Test getting recent conversations directly."""
    print(f"\n=== Direct API Test: Recent conversations ===")
    
    try:
        # Initialize ShopeeAPI
        config = Config()
        api = ShopeeAPI(config)
        
        # Get recent conversations
        recent_conversations, status_code = api.chat_service.get_recent_conversations()
        print(f"Recent conversations status: {status_code}")
        
        if status_code == 200:
            print(f"Recent conversations: {json.dumps(recent_conversations, indent=2, ensure_ascii=False)}")
            
            # Check if we have conversations in the response
            conversations = []
            if 'conversations' in recent_conversations:
                conversations = recent_conversations['conversations']
            elif isinstance(recent_conversations, list):
                conversations = recent_conversations
            
            print(f"\nFound {len(conversations)} conversations")
            
            # Look for both usernames in the conversations
            for username in ["me0tn_14qo", "syahmialfabet"]:
                found = False
                for i, conv in enumerate(conversations):
                    conv_username = conv.get('to_name', '').lower()
                    print(f"Conversation {i}: to_name='{conv.get('to_name')}', to_id={conv.get('to_id')}")
                    
                    if conv_username == username.lower():
                        print(f"*** FOUND USER '{username}' in recent conversations! ***")
                        print(f"User details: {json.dumps(conv, indent=2, ensure_ascii=False)}")
                        found = True
                        break
                
                if not found:
                    print(f"User '{username}' NOT found in recent conversations")
            
            return recent_conversations
            
        else:
            print(f"Failed to get recent conversations: {recent_conversations}")
            return {"error": "Failed to get recent conversations"}
            
    except Exception as e:
        print(f"Exception getting recent conversations: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"error": f"Exception getting recent conversations: {str(e)}"}


def test_get_conversation_info_by_username_direct(username: str):
    """Test the get_conversation_info_by_username method directly."""
    print(f"\n=== Direct API Test: get_conversation_info_by_username for {username} ===")
    
    try:
        # Initialize ShopeeAPI
        config = Config()
        api = ShopeeAPI(config)
        
        # Call the method directly
        conversation_info, error = api.chat_service.get_conversation_info_by_username(username)
        
        if error:
            print(f"Error: {error}")
        else:
            print(f"Success: {json.dumps(conversation_info, indent=2, ensure_ascii=False)}")
        
        return conversation_info, error
        
    except Exception as e:
        print(f"Exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, {"error": f"Exception: {str(e)}"}


def main():
    """Main debug function."""
    print("=== Detailed ShopeeAPI Debug Script ===")
    
    # Test usernames
    working_username = "me0tn_14qo"
    failing_username = "syahmialfabet"
    
    # Test recent conversations first
    test_recent_conversations_direct()
    
    # Test both usernames
    for username in [working_username, failing_username]:
        print(f"\n{'='*80}")
        print(f"DETAILED TESTING FOR USERNAME: {username}")
        print(f"{'='*80}")
        
        # Test conversation search
        test_conversation_search_direct(username)
        
        # Test get_conversation_info_by_username method
        test_get_conversation_info_by_username_direct(username)
        
        print(f"\n{'='*80}")
        print(f"COMPLETED DETAILED TESTING FOR: {username}")
        print(f"{'='*80}")


if __name__ == "__main__":
    main()
