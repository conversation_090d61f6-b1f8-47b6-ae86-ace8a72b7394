# Shopee API

A FastAPI application for interacting with the Shopee Seller API.

## Project Structure

```
ShopeeAPI/
├── __init__.py          # Package initialization
├── api.py               # FastAPI application and endpoints
├── main.py              # Entry point for running the application
├── client.py            # Main client for Shopee API
├── config.json.example  # Example configuration file
├── requirements.txt     # Package dependencies
├── README.md            # Project documentation
├── run.bat              # Windows batch script to run the API
├── run.sh               # Linux/Mac shell script to run the API
├── docker-compose.yml   # Docker Compose configuration
├── Dockerfile           # Docker build configuration
├── core/                # Core functionality
│   ├── __init__.py
│   ├── auth.py          # Authentication and credential management
│   ├── config.py        # Configuration settings
│   ├── session.py       # HTTP session management
│   └── exceptions.py    # Custom exceptions
├── services/            # Business logic services
│   ├── __init__.py
│   ├── orders.py        # Order-related operations
│   ├── chat.py          # Chat-related operations
│   └── common.py        # Common service utilities
├── models/              # Data models
│   ├── __init__.py
│   ├── orders.py        # Order-related models
│   ├── chat.py          # Chat-related models
│   └── common.py        # Common models
├── utils/               # Utility functions
│   ├── __init__.py
│   ├── helpers.py       # Helper functions
│   └── validators.py    # Data validation utilities
├── run_tests.py         # Script to run all tests
├── run_tests.bat        # Windows batch script to run tests
├── run_tests.sh         # Linux/Mac shell script to run tests
└── tests/               # Test suite
    ├── __init__.py
    ├── README.md        # Test documentation
    ├── test_auth.py     # Authentication tests
    ├── test_orders.py   # Order service tests
    └── test_chat.py     # Chat service tests
```

## Features

- RESTful API endpoints for Shopee operations
- Authentication management
- Order operations (search, details, shipping)
- Chat functionality (send messages, get conversations)
- Real-time messaging via WebSockets
- Intelligent caching mechanism for conversation messages
- Hot reload for configuration changes
- Swagger documentation via FastAPI

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd <repository-directory>

# Install dependencies
pip install -r ShopeeAPI/requirements.txt
```

## Configuration

Create a `config.json` file in the ShopeeAPI directory based on the provided example:

```json
{
  "AUTHORIZATION_CODE": "Bearer your_auth_token",
  "COOKIE": "your_cookie_string",
  "SHOP_ID": 123456789,
  "REGION_ID": "MY",
  "PAGE_SIZE": 40,
  "REQUEST_TIMEOUT": 60,
  "CACHE": {
    "ENABLED": true,
    "USERNAME_TO_CONVERSATION_ID": {
      "ENABLED": true,
      "EXPIRY_SECONDS": 86400,
      "MAX_SIZE": 1000
    },
    "CONVERSATION_MESSAGES": {
      "ENABLED": true,
      "EXPIRY_SECONDS": 3600,
      "MAX_SIZE": 50,
      "WEBSOCKET_ONLY": true
    }
  },
  "WEBSOCKET": {
    "ENABLED": true,
    "RECONNECT_INTERVAL": 30,
    "MAX_RECONNECT_ATTEMPTS": 10,
    "PING_INTERVAL": 25,
    "PING_TIMEOUT": 5,
    "CLIENT_MAX_SIZE": 100
  },
  "WEBHOOK": {
    "ENABLED": true,
    "MESSAGE_RECEIVED": {
      "ENABLED": true,
      "URL": "http://your-webhook-url/message-received",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    },
    "MESSAGE_SENT": {
      "ENABLED": true,
      "URL": "http://your-webhook-url/message-sent",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    }
  },
  "URLS": {
    "initial_order_list": "https://seller.shopee.com.my/api/v3/order/search_order_list_index",
    "order_details": "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list",
    "order_details_specific": "https://seller.shopee.com.my/api/v3/order/get_one_order",
    "init_order": "https://seller.shopee.com.my/api/v3/shipment/init_order",
    "conversation": "https://seller.shopee.com.my/webchat/api/v1.2/mini/conversation/redirection",
    "conversation_search": "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/search/combined_search",
    "chat_message": "https://seller.shopee.com.my/webchat/api/v1.2/messages",
    "recent_conversations": "https://seller.shopee.com.my/webchat/api/v1.2/conversations",
    "conversation_messages": "https://seller.shopee.com.my/webchat/api/v1.2/conversations",
    "conversation_unread": "https://seller.shopee.com.my/webchat/api/v1.2/conversations",
    "websocket": "wss://seller-push-ws.shopee.com.my/socket.io/?region=my&_v=new&EIO=3&transport=websocket"
  }
}
```

## Running the API

### Development Mode

```bash
# Windows
ShopeeAPI\run.bat

# Linux/Mac
./ShopeeAPI/run.sh

# Or directly with Python
python -m ShopeeAPI.main
```

The API will be available at http://localhost:8000 and the Swagger documentation at http://localhost:8000/docs

The WebSocket documentation and testing page will be available at http://localhost:8000/ws-docs

### Production Mode

For production, it's recommended to use a proper ASGI server like Gunicorn with Uvicorn workers:

```bash
gunicorn -w 4 -k uvicorn.workers.UvicornWorker ShopeeAPI.api:app
```

### Docker

```bash
cd ShopeeAPI
docker-compose up -d
```

## Running Tests

```bash
# Windows
ShopeeAPI\run_tests.bat

# Linux/Mac
./ShopeeAPI/run_tests.sh

# Or directly with Python
python -m ShopeeAPI.run_tests

# Run a specific test file
python -m unittest ShopeeAPI.tests.test_auth

# Run a specific test case
python -m unittest ShopeeAPI.tests.test_auth.TestAuthAPI
```

See the `tests/README.md` file for more information about the test suite.

## API Endpoints

| Endpoint | Method | Description |
| --- | --- | --- |
| `/auth` | POST | Update API credentials |
| `/config` | POST | Update API configuration |
| `/orders/to_ship` | GET | Get orders with 'To Ship' status |
| `/orders/shipped` | GET | Get orders with 'Shipped' status |
| `/orders/completed` | GET | Get orders with 'Completed' status |
| `/orders/search` | GET | Search for an order by order number |
| `/orders/{order_sn}/status` | GET | Get order status |
| `/orders/{order_sn}/details` | GET | Get detailed order information |
| `/orders/{order_sn}/ship` | POST | Ship an order |
| `/buyers/{buyer_id}/orders` | GET | Get orders from a specific buyer |
| `/buyers/username/{username}/orders` | GET | Get orders from a specific buyer by username |
| `/chat/send_message` | POST | Send a text message to a user |
| `/chat/send_image` | POST | Send an image message to a user |
| `/chat/send_order_message` | POST | Send an order message to a user |
| `/chat/conversations/recent` | GET | Get recent conversations (supports unread_only parameter) |
| `/chat/conversations/recent_messages` | GET | Get recent latest messages |
| `/chat/conversations/{conversation_id}/messages` | GET | Get messages from a conversation (supports offset, limit, direction parameters) |
| `/chat/conversations/{conversation_id}/unread` | PUT | Mark a conversation as unread |
| `/chat/conversations/username/{username}/messages` | GET | Get messages from a conversation by username (supports offset, limit, direction="older", force_refresh parameters) |
| `/chat/conversations/username/{username}/unread` | PUT | Mark a conversation as unread by username |
| `/chat/search_conversation` | GET | Search for a conversation by username |
| `/websocket/status` | GET | Check the status of the WebSocket connection |
| `/websocket/reconnect` | POST | Force reconnect the WebSocket connection |
| `/ws/chat` | WebSocket | WebSocket endpoint for real-time chat messages |
| `/ws/chat/{username}` | WebSocket | WebSocket endpoint for real-time chat messages filtered by username |

### Cached Endpoints

#### `/chat/conversations/username/{username}/messages`

This endpoint supports caching and includes a cache indicator in the response.

#### `/buyers/username/{username}/orders`

This endpoint uses the username to conversation ID mapping cache to find the buyer ID for the given username, then retrieves the orders for that buyer.

**Parameters:**

| Parameter | Type | Description |
| --- | --- | --- |
| `username` | string | The username of the buyer |
| `order_type` | string | Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund) |
| `page` | integer | Page number (default: 1) |
| `per_page` | integer | Number of orders per page (default: 20, max: 100) |

**Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `offset` | integer | Number of messages to skip (default: 0) |
| `limit` | integer | Maximum number of messages to return (default: 20, max: 100) |
| `direction` | string | Direction to retrieve messages (only 'older' is supported) |
| `force_refresh` | boolean | Force refresh from Shopee API instead of using cache (default: false) |

**Example Requests:**
- `GET /chat/conversations/username/customer123/messages` - Will use cache if available
- `GET /chat/conversations/username/customer123/messages?force_refresh=true` - Will bypass cache and fetch from Shopee

**Response:**
```json
{
  "data": {
    "messages": [...],
    "cached": true  // or false if fetched from Shopee
  }
}
```

## Using as a Client Library

You can also use the underlying Shopee API client directly:

```python
from ShopeeAPI import ShopeeAPI

# Initialize with credentials
api = ShopeeAPI(
    authorization_code="Bearer your_auth_token",
    cookie="your_cookie_string"
)

# Get orders
orders = api.get_to_ship_orders()

# Search for a specific order
order = api.search_order("123456789")

# Get order details
order_details, status_code = api.get_order_details("123456789")

# Get all orders from a specific buyer by ID
buyer_orders, status_code = api.get_buyer_orders(buyer_id=1368568180)

# Get completed orders from a specific buyer by ID
completed_orders, status_code = api.get_buyer_orders(buyer_id=1368568180, order_type="completed")

# Get orders to ship from a specific buyer by username
to_ship_orders, status_code = api.get_buyer_orders_by_username(username="me0tn_14qo", order_type="to_ship")

# Get orders with pagination
page2_orders, status_code = api.get_buyer_orders_by_username(username="me0tn_14qo", page=2, per_page=50)

# Ship an order
result, status_code = api.ship_order("123456789")
```

## Using WebSockets for Real-time Messages

The API provides WebSocket endpoints for receiving real-time messages from Shopee without having to repeatedly poll the server.

### JavaScript Client Example

```javascript
// Connect to the WebSocket endpoint
const socket = new WebSocket('ws://localhost:8000/ws/chat');

// Handle connection open
socket.onopen = function(event) {
  console.log('Connected to WebSocket server');
};

// Handle incoming messages
socket.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received message:', data);

  // Handle different message types
  if (data.type === 'connection_established') {
    console.log('Connection established with client ID:', data.client_id);
  } else {
    // Handle chat messages
    updateChatUI(data);
  }
};

// Handle errors
socket.onerror = function(error) {
  console.error('WebSocket error:', error);
};

// Handle connection close
socket.onclose = function(event) {
  console.log('Disconnected from WebSocket server, code:', event.code);
};

// To connect to a specific conversation by username
const username = 'customer123';
const usernameSocket = new WebSocket(`ws://localhost:8000/ws/chat/${username}`);
```

### Python Client Example

```python
import asyncio
import websockets
import json

async def connect_to_chat():
    uri = "ws://localhost:8000/ws/chat"
    async with websockets.connect(uri) as websocket:
        # Handle initial connection message
        response = await websocket.recv()
        data = json.loads(response)
        print(f"Connected with client ID: {data['client_id']}")

        # Listen for messages
        while True:
            message = await websocket.recv()
            data = json.loads(message)
            print(f"Received: {data}")

# Run the client
asyncio.get_event_loop().run_until_complete(connect_to_chat())
```

## Webhook Integration

The API provides webhook functionality to notify external systems when messages are sent or received through the WebSocket connection:

### Webhook Configuration

Configure webhooks in the `config.json` file:

```json
"WEBHOOK": {
  "ENABLED": true,
  "MESSAGE_RECEIVED": {
    "ENABLED": true,
    "URL": "http://your-webhook-url/message-received",
    "RETRY_COUNT": 3,
    "RETRY_DELAY": 5
  },
  "MESSAGE_SENT": {
    "ENABLED": true,
    "URL": "http://your-webhook-url/message-sent",
    "RETRY_COUNT": 3,
    "RETRY_DELAY": 5
  }
}
```

### Webhook Payload

When a message is sent or received, a webhook notification will be sent to the configured URL with the following payload:

#### For received messages:
```json
{
  "message": {
    "id": "2347556398049968497",
    "shop_id": 345602862,
    "from_id": 345622437,
    "to_id": 1368568180,
    "from_user_name": "mtyb_official",
    "to_user_name": "me0tn_14qo",
    "type": "text",
    "content": {"text": "hi"},
    "conversation_id": "1484437065047388532",
    "send_by_yourself": false,
    "...": "other message fields"
  },
  "conversation_id": "1484437065047388532",
  "event": "message_received",
  "timestamp": 1747410578
}
```

#### For sent messages:
```json
{
  "message": {
    "id": "2347556170106225009",
    "shop_id": 1367853447,
    "from_id": 1368568180,
    "to_id": 345622437,
    "from_user_name": "me0tn_14qo",
    "to_user_name": "mtyb_official",
    "type": "text",
    "content": {"text": "hi"},
    "conversation_id": "1484437065047388532",
    "send_by_yourself": true,
    "...": "other message fields"
  },
  "conversation_id": "1484437065047388532",
  "event": "message_sent",
  "timestamp": 1747410470
}
```

### Webhook Retry Mechanism

If a webhook delivery fails, the system will retry according to the configured settings:

- `RETRY_COUNT`: Number of retry attempts (default: 3)
- `RETRY_DELAY`: Delay between retries in seconds (default: 5)

## Caching Mechanism

The API implements a sophisticated caching mechanism to reduce the number of requests to Shopee's servers and improve performance:

### Username to Conversation ID Cache

- Caches the mapping between usernames and conversation IDs
- Configurable expiration time and maximum size
- Reduces the need to search for conversation IDs repeatedly
- Automatically updates when new conversations are discovered

### Conversation Messages Cache

- Caches messages from conversations when WebSocket is connected
- New messages received via WebSocket are automatically added to the cache in real-time
- Only actual chat messages are cached (message_type="message"), not notifications
- Reduces the need to fetch messages from Shopee's servers repeatedly
- Only enabled when WebSocket is successfully connected (configurable via WEBSOCKET_ONLY setting)
- Configurable expiration time and maximum size
- Responses include a `cached: true/false` indicator to show if data came from cache
- Supports a `force_refresh=true` parameter to bypass cache and fetch from Shopee directly

### How the Caching Works

1. When a user requests messages for a conversation using the `/chat/conversations/username/{username}/messages` endpoint:
   - The system first checks if caching is enabled and if the WebSocket is connected
   - If caching is enabled and the WebSocket is connected, it checks if there are cached messages for the conversation
   - If cached messages are available, it returns them directly with `cached: true` in the response
   - If no cached messages are available, it makes a request to Shopee's servers and returns the response with `cached: false`

2. When new messages are received via WebSocket:
   - The system automatically adds them to the cache for the relevant conversation
   - This ensures that subsequent requests for that conversation's messages will include the latest messages without needing to query Shopee's servers

3. To bypass the cache and force a fresh request to Shopee's servers:
   - Add the `force_refresh=true` parameter to your request
   - Example: `GET /chat/conversations/username/customer123/messages?force_refresh=true`

### Response Format with Cache Indicator

When using endpoints that support caching, the response will include a `cached` field indicating whether the data came from cache:

```json
{
  "data": {
    "messages": [
      {
        "id": "2347548398165819761",
        "from_user_name": "customer123",
        "to_user_name": "your_shop",
        "type": "text",
        "content": {
          "text": "Hello, I have a question about my order"
        },
        "created_at": "2025-05-16T22:46:04+08:00",
        // ... other message fields
      },
      // ... more messages
    ],
    "cached": true  // Indicates the response came from cache
  }
}
```

- `cached: true` - Data was retrieved from the local cache
- `cached: false` - Data was freshly fetched from Shopee's servers

### Cache Configuration

```json
"CACHE": {
  "ENABLED": true,
  "USERNAME_TO_CONVERSATION_ID": {
    "ENABLED": true,
    "EXPIRY_SECONDS": 86400,
    "MAX_SIZE": 1000
  },
  "CONVERSATION_MESSAGES": {
    "ENABLED": true,
    "EXPIRY_SECONDS": 3600,
    "MAX_SIZE": 50,
    "WEBSOCKET_ONLY": true
  }
}
```

- `ENABLED`: Master switch for all caching
- `USERNAME_TO_CONVERSATION_ID`: Settings for username to conversation ID cache
  - `ENABLED`: Enable/disable this specific cache
  - `EXPIRY_SECONDS`: Time in seconds until cache entries expire (86400 = 24 hours)
  - `MAX_SIZE`: Maximum number of entries to store
- `CONVERSATION_MESSAGES`: Settings for conversation messages cache
  - `ENABLED`: Enable/disable this specific cache
  - `EXPIRY_SECONDS`: Time in seconds until cache entries expire (3600 = 1 hour)
  - `MAX_SIZE`: Maximum number of conversations to cache
  - `WEBSOCKET_ONLY`: Only cache when WebSocket is connected

## Best Practices

- Always handle API errors gracefully
- Implement rate limiting to avoid being blocked
- Refresh credentials when they expire
- Log API requests and responses for debugging
- Use HTTPS in production for secure credential transfer

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
