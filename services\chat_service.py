from utils.api_utils import generate_request_id
from utils.session import session
import config
import uuid
from urllib.parse import quote
from utils.api_utils import (
    get_initial_order_list, process_orders, get_common_params
)
from typing import List, Dict, Any
from services.deepseek_service import generate_reply
import json
import time

def send_order_message(order_sn):
    # Get order details
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return {"error": "Order not found"}, 404

    order = order_data['data']['card_list'][0]['order_card']
    order_id = order['order_ext_info']['order_id']
    to_id = order['order_ext_info']['buyer_user_id']

    conversation_info, error = get_conversation_info_by_ordersn_helper(order_sn)
    if error:
        return error, 500

    if not conversation_info:
        return {"error": "Failed to retrieve conversation info"}, 500

    conversation_id = conversation_info.get('id')

    if not conversation_id:
        return {"error": "Failed to extract conversation_id"}, 500

    message_payload = {
        "request_id": generate_request_id(),
        "to_id": to_id,
        "type": "order",
        "content": {
            "order_id": order_id,
            "shop_id": config.SHOP_ID,
            "uid": str(uuid.uuid4())
        },
        "shop_id": config.SHOP_ID,
        "chat_send_option": {
            "force_send_cancel_order_warning": False,
            "comply_cancel_order_warning": False
        },
        "entry_point": "direct_chat_entry_point",
        "choice_info": {
            "real_shop_id": None
        },
        "conversation_id": conversation_id,
        "re_policy": {
            "dfp_access_f": "P6HFksuWQ8Tap+DnhVxOqA==|5hMi/K0kBKyUzxNNpYITKExyqO91xRwAnZhXKuh6M1gdq6FwNBmbnbrseJFi5oNJb5t6y18W0D0YIw==|u0PcFqU736y8ZD/U|08|3"
        }
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "uuid": str(uuid.uuid4())
    }

    chat_response = session.post(
        config.CHAT_MESSAGE_URL,
        params=params,
        json=message_payload
    )

    if chat_response.status_code != 200:
        return {"error": "Failed to send order message"}, 500

    return chat_response.json()

def send_image_message(payload):
    conversation_info = None
    error = None
    if 'username' in payload:
        conversation_info, error = get_conversation_info_by_username_helper(payload['username'])
    elif 'order_sn' in payload:
        conversation_info, error = get_conversation_info_by_ordersn_helper(payload['order_sn'])
    else:
        return {"error": "Either username or order_sn is required"}, 400

    if error:
        return error, 500

    if not conversation_info:
        return {"error": "Failed to retrieve conversation info"}, 500

    conversation_id = None
    to_id = None

    if 'order_sn' in payload:
        conversation_id = conversation_info.get('id')
        to_id = conversation_info.get('to_id')
    elif 'username' in payload:
        conversations = conversation_info.get('conversation_search_result', {}).get('conversations', [])
        if conversations:
            buyer_info = conversations[0]
            conversation_id = f"{buyer_info.get('shop_id')}_{buyer_info.get('buyer_id')}"
            to_id = buyer_info.get('buyer_id')

    if not conversation_id or not to_id:
        return {"error": "Failed to extract conversation_id or to_id"}, 500

    message_payload = {
        "request_id": generate_request_id(),
        "to_id": to_id,
        "type": "image",
        "content": {
            "uid": str(uuid.uuid4()),
            "width": payload.get('width', 96),
            "height": payload.get('height', 96),
            "url_hash": payload.get('url_hash'),
            "url": payload.get('url'),
            "thumb_url": payload.get('thumb_url'),
            "thumb_width": payload.get('thumb_width', 330),
            "thumb_height": payload.get('thumb_height', 330),
            "file_server_id": payload.get('file_server_id', 0)
        },
        "shop_id": config.SHOP_ID,
        "chat_send_option": {
            "force_send_cancel_order_warning": payload.get('force_send_cancel_order_warning', False),
            "comply_cancel_order_warning": payload.get('comply_cancel_order_warning', False)
        },
        "entry_point": "direct_chat_entry_point",
        "choice_info": {
            "real_shop_id": None
        },
        "conversation_id": conversation_id,
        "re_policy": {
            "dfp_access_f": "P6HFksuWQ8Tap+DnhVxOqA==|5hMi/K0kBKyUzxNNpYITKExyqO91xRwAnZhXKuh6M1gdq6FwNBmbnbrseJFi5oNJb5t6y18W0D0YIw==|u0PcFqU736y8ZD/U|08|3"
        }
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "uuid": str(uuid.uuid4())
    }

    chat_response = session.post(
        config.CHAT_MESSAGE_URL,
        params=params,
        json=message_payload
    )

    if chat_response.status_code != 200:
        return {"error": "Failed to send image message"}, 500

    return chat_response.json()


def send_chat_message(payload):
    conversation_info = None
    error = None
    if 'username' in payload:
        conversation_info, error = get_conversation_info_by_username_helper(payload['username'])
    elif 'order_sn' in payload:
        conversation_info, error = get_conversation_info_by_ordersn_helper(payload['order_sn'])
    else:
        return {"error": "Either username or order_sn is required"}, 400

    if error:
        return error, 500

    if not conversation_info:
        return {"error": "Failed to retrieve conversation info"}, 500

    conversation_id = None
    to_id = None

    if 'order_sn' in payload:
        conversation_id = conversation_info.get('id')
        to_id = conversation_info.get('to_id')
    elif 'username' in payload:
        conversations = conversation_info.get('conversation_search_result', {}).get('conversations', [])
        if conversations:
            buyer_info = conversations[0]
            conversation_id = f"{buyer_info.get('shop_id')}_{buyer_info.get('buyer_id')}"
            to_id = buyer_info.get('buyer_id')

    if not conversation_id or not to_id:
        return {"error": "Failed to extract conversation_id or to_id"}, 500

    message_payload = {
        "request_id": generate_request_id(),
        "to_id": to_id,
        "type": "text",
        "content": {
            "text": payload.get('text'),
            "uid": str(uuid.uuid4())
        },
        "shop_id": config.SHOP_ID,
        "chat_send_option": {
            "force_send_cancel_order_warning": payload.get('force_send_cancel_order_warning', False),
            "comply_cancel_order_warning": payload.get('comply_cancel_order_warning', False)
        },
        "entry_point": "direct_chat_entry_point",
        "choice_info": {
            "real_shop_id": None
        },
        "conversation_id": conversation_id,
        "re_policy": {
            "dfp_access_f": "P6HFksuWQ8Tap+DnhVxOqA==|5hMi/K0kBKyUzxNNpYITKExyqO91xRwAnZhXKuh6M1gdq6FwNBmbnbrseJFi5oNJb5t6y18W0D0YIw==|u0PcFqU736y8ZD/U|08|3"
        }
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "uuid": str(uuid.uuid4())
    }

    chat_response = session.post(
        config.CHAT_MESSAGE_URL,
        params=params,
        json=message_payload
    )

    if chat_response.status_code != 200:
        print(chat_response.content)
        return {"error": "Failed to send chat message"}, 500

    return chat_response.json(), 200

def get_conversation_info_by_username_helper(username):
    if not username:
        return None, {"error": "username parameter is required"}

    # Prepare the params for the conversation search request
    params = {
        "per_page": "20",
        "keyword": username,
        "type": "3",
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }

    # Send the GET request to search for conversation info
    conversation_response = session.get(
        config.CONVERSATION_SEARCH_URL,
        params=params
    )

    if conversation_response.status_code != 200:
        return None, {"error": "Failed to retrieve conversation info"}

    return conversation_response.json(), None

def get_conversation_info_by_ordersn_helper(order_sn):
    if not order_sn:
        return None, {"error": "order_sn parameter is required"}

    # Search for the order to get user_id
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    # Check if order_data has the expected structure
    if 'data' not in order_data or 'card_list' not in order_data['data']:
        return None, {"error": "Unexpected order data structure"}

    # Find the matching order in the card_list
    matching_order = None
    for card in order_data['data']['card_list']:
        if isinstance(card, dict) and 'order_card' in card:
            if card['order_card']['card_header']['order_sn'] == order_sn:
                matching_order = card['order_card']
                break
        elif isinstance(card, dict) and 'card_header' in card:
            if card['card_header']['order_sn'] == order_sn:
                matching_order = card
                break
        elif isinstance(card, dict) and 'package_level_order_card' in card:
            if card['package_level_order_card']['card_header']['order_sn'] == order_sn:
                matching_order = card['package_level_order_card']
                break

    if not matching_order:
        return None, {"error": "Order not found"}

    user_id = matching_order['order_ext_info']['buyer_user_id']

    # Prepare the payload and params for the conversation request
    payload = {
        "user_id": user_id,
        "shop_id": config.SHOP_ID
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": config.CSRF_TOKEN,
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "_api_source": "sc"
    }

    # Send the POST request to get conversation info using the new session
    conversation_response = session.post(
        config.CONVERSATION_URL,
        params=params,
        json=payload
    )

    if conversation_response.status_code != 200:
        return None, {"error": "Failed to retrieve conversation info"}

    return conversation_response.json(), None

def get_recent_conversations():
    params = {
        "direction": "older",
        "_s": "1",
        "biz_id": 0,
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.8.4",
        "on_message_received": False,
        "csrf_token": config.CSRF_TOKEN,
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }
    response = session.get(
        config.RECENT_CONVERSATIONS_URL,
        params=params
    )

    if response.status_code != 200:
        print(response.content)
        return {"error": "Failed to retrieve recent conversations"}, 500

    return response.json()

def get_recent_latest_messages() -> List[Dict[str, Any]]:
    conversations = get_recent_conversations()
    if isinstance(conversations, tuple) and len(conversations) == 2:
        conversations, error = conversations
        if error:
            return []
    elif isinstance(conversations, dict) and 'error' in conversations:
        return []

    filtered_messages = []
    for conv in conversations:
        if conv.get('latest_message_type') == 'text' and not conv.get('pinned'):
            filtered_messages.append({
                'to_name': conv.get('to_name'),
                'to_id': conv.get('to_id'),
                'latest_message_content': conv.get('latest_message_content', {}).get('text'),
                'latest_message_id': conv.get('latest_message_id'),
                'latest_message_type': 'text',
                'id': conv.get('id')
            })

    return filtered_messages

def get_conversation_messages(conversation_id: str) -> List[Dict[str, Any]]:
    params = {
        "shop_id": config.SHOP_ID,
        "offset": "0",
        "limit": "20",
        "direction": "older",
        "biz_id": "0",
        "on_message_received": "true",
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.8.4",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }

    response = session.get(
        f"{config.CONVERSATION_MESSAGES_URL}/{conversation_id}/messages",
        params=params
    )

    if response.status_code != 200:
        return {"error": "Failed to retrieve conversation messages"}, 500

    return response.json()

def handle_ai_action(action_results: List[Dict[str, Any]], username: str) -> Dict[str, Any]:
    """Handle multiple AI actions and execute appropriate responses"""
    responses = []
    
    for action_result in action_results:
        action_type = action_result.get('type')
        
        if action_type == 'chat':
            # Standard chat message
            chat_payload = {
                "text": action_result.get('reply', ''),
                "username": username,
                "force_send_cancel_order_warning": False,
                "comply_cancel_order_warning": False
            }
            chat_response = send_chat_message(chat_payload)
            responses.append({
                "type": "chat",
                "response": chat_response
            })
            
        elif action_type == 'send_order':
            if action_result.get('status') == 'success':
                # First send chat message about the order
                chat_payload = {
                    "text": f"Here are the details for order {action_result['order_sn']} [MTYB]",
                    "username": username,
                    "force_send_cancel_order_warning": False,
                    "comply_cancel_order_warning": False
                }
                chat_response = send_chat_message(chat_payload)
                
                # Then send the order message
                order_response = send_order_message(action_result['order_sn'])
                
                responses.append({
                    "type": "send_order",
                    "chat_response": chat_response,
                    "order_response": order_response
                })
            else:
                # Send error message if order lookup failed
                chat_payload = {
                    "text": f"Sorry, I couldn't find the order details. {action_result.get('error', '')} [MTYB]",
                    "username": username,
                    "force_send_cancel_order_warning": False,
                    "comply_cancel_order_warning": False
                }
                chat_response = send_chat_message(chat_payload)
                responses.append({
                    "type": "send_order",
                    "error": action_result.get('error'),
                    "chat_response": chat_response
                })
    
    return responses

def process_and_reply_to_conversation(conversation_id: str, username: str) -> Dict[str, Any]:
    """Process and reply to unreplied messages with cooldown check"""
    try:
        # Load cooldown data
        cooldown_file = 'ai_reply_cooldown.json'
        try:
            with open(cooldown_file, 'r') as f:
                cooldown_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            cooldown_data = {}
            # Create the file if it doesn't exist
            with open(cooldown_file, 'w') as f:
                json.dump(cooldown_data, f, indent=4)

        # Get conversation messages
        messages = get_conversation_messages(conversation_id)
        if isinstance(messages, tuple) and len(messages) == 2 and messages[1] == 500:
            print(f"Error getting messages for conversation {conversation_id}: {messages[0]['error']}")
            return messages
            
        if not messages or len(messages) == 0:
            print(f"No messages found for conversation {conversation_id}")
            return {"error": "No messages found"}, 404

        # Check cooldown
        last_message = messages[0]
        user_id = str(last_message['from_id'])
        current_time = time.time()
        cooldown_minutes = float(config.AI_REPLY_COOLDOWN_MINUTES or 60)

        if user_id in cooldown_data:
            last_reply_time = cooldown_data[user_id]
            if current_time - last_reply_time < (cooldown_minutes * 60):
                print(f"Cooldown period not elapsed for user {user_id}")
                return {"error": "Cooldown period not elapsed"}, 429

        # Generate AI reply
        ai_response = generate_reply(messages)
        
        if not ai_response.get("success"):
            error_msg = f"AI reply generation failed for conversation {conversation_id}"
            print(error_msg)
            return {"error": error_msg}, 500

        # Handle all AI actions and send appropriate responses
        responses = handle_ai_action(ai_response.get("action_results", []), username)
        
        # Update cooldown
        cooldown_data[user_id] = current_time
        with open(cooldown_file, 'w') as f:
            json.dump(cooldown_data, f, indent=4)
        
        return {
            "success": True,
            "data": {
                "responses": responses,
                "ai_response": ai_response["data"]
            }
        }

    except Exception as e:
        error_msg = f"Failed to process and reply to conversation {conversation_id}: {str(e)}"
        print(error_msg)
        return {"error": error_msg}, 500