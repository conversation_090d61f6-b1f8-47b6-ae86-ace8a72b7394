{% extends "base.html" %}

{% block title %}Dashboard{% endblock %}

{% block header %}
Dashboard
{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                    <i class="fas fa-envelope text-white text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            Total Email Configs
                        </dt>
                        <dd class="text-3xl font-semibold text-gray-900">
                            {{ email_config_count }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                    <i class="fab fa-steam text-white text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            Total Steam Credentials
                        </dt>
                        <dd class="text-3xl font-semibold text-gray-900">
                            {{ steam_credential_count }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                    <i class="fas fa-check-circle text-white text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            Successful Redeems Today
                        </dt>
                        <dd class="text-3xl font-semibold text-gray-900">
                            {{ successful_redeems_today }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

</div>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Account Redeem Statistics</h3>
            <div class="mt-5">
                <ul class="divide-y divide-gray-200">
                    {% for account, count in account_redeems.items() %}
                    <li class="py-4">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    {{ account }}
                                </p>
                            </div>
                            <div class="inline-flex items-center text-base font-semibold text-gray-900">
                                {{ count }}
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Order Redeem Statistics</h3>
            <div class="mt-5">
                <ul class="divide-y divide-gray-200">
                    {% for order, count in order_redeems.items() %}
                    <li class="py-4">
                        <div class="flex items-center space-x-4">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    {{ order }}
                                </p>
                            </div>
                            <div class="inline-flex items-center text-base font-semibold text-gray-900">
                                {{ count }}
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="bg-white overflow-hidden shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Summary</h3>
        <div class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3">
            <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Total Requests
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ summary.total_requests }}
                    </dd>
                </div>
            </div>
            <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Successful Requests
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ summary.successful_requests }}
                    </dd>
                </div>
            </div>
            <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Failed Requests
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ summary.failed_requests }}
                    </dd>
                </div>
            </div>
            <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Unique Accounts
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ summary.unique_accounts }}
                    </dd>
                </div>
            </div>
            <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Unique Orders
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ summary.unique_orders }}
                    </dd>
                </div>
            </div>
            <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Success Rate
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ summary.success_rate }}%
                    </dd>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="bg-white overflow-hidden shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Request Statistics (Last 7 Days)</h3>
        <div class="mt-5">
            <canvas id="requestChart" width="400" height="100"></canvas>
        </div>
    </div>
</div>

<div class="mt-8">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-semibold">Authentication Code Request Records</h2>
        <button id="resetRecordsBtn" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
            Reset Records
        </button>
    </div>
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <table id="authCodeTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth Code
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Error
                        Message</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for record in auth_code_records %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ record.username }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.order_id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.auth_code or 'N/A' }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.timestamp }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            {% if record.status == 'Success' %}
                                bg-green-100 text-green-800
                            {% elif record.status == 'Attempt' %}
                                bg-yellow-100 text-yellow-800
                            {% else %}
                                bg-red-100 text-red-800
                            {% endif %}">
                            {{ record.status }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.error_message or 'N/A' }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- New Table for Active Cooldowns -->
    <div class="bg-white overflow-hidden shadow rounded-lg mb-6 mt-3">
        <div class="px-4 py-5 sm:p-6">
            <h2 class="text-2xl font-semibold mb-4">Active Session Cooldowns</h2>
            <table id="cooldownTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Username</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order
                            ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Remaining Cooldown (s)</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Rows will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- New Netflix Session Cooldowns Table -->
    <div class="bg-white overflow-hidden shadow rounded-lg mb-6 mt-3">
        <div class="px-4 py-5 sm:p-6">
            <h2 class="text-2xl font-semibold mb-4">Netflix Session Cooldowns</h2>
            <table id="netflixCooldownTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order
                            SN</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Cooldown Until</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Remaining Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Rows will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

</div>
<style>
    /* 自定义 DataTable 样式 */
    .dataTables_wrapper {
        font-family: 'Arial', sans-serif;
        padding-left: 30px;
        padding-right: 30px;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 15px 0;
        font-size: 14px;
    }

    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 5px 10px;
    }

    .dataTables_wrapper .dataTables_length select {
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 5px 25px 5px 10px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23000' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E") no-repeat right 10px center/8px 8px;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        border: none;
        padding: 5px 10px;
        margin: 0 2px;
        border-radius: 4px;
        background-color: #f7fafc;
        color: #4a5568;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current,
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background-color: #4a5568;
        color: white !important;
    }

    table.dataTable thead th {
        border-bottom: 2px solid #e2e8f0;
        font-weight: 600;
    }

    table.dataTable tbody td {
        padding: 12px 10px;
    }

    table.dataTable tbody tr:hover {
        background-color: #f7fafc;
    }
</style>

<script>
    $(document).ready(function () {

        $('#authCodeTable').DataTable({
            responsive: true,
            order: [[3, 'desc']],
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
            language: {
                paginate: {
                    previous: "&#8592;",
                    next: "&#8594;"
                },
                lengthMenu: "_MENU_ per page",
                search: "",
                searchPlaceholder: "Search...",
                info: "Showing _START_ to _END_ of _TOTAL_ entries"
            },
            dom: "<'flex justify-between items-center'<l><f>>" +
                "<'overflow-x-auto'tr>" +
                "<'flex justify-between items-center'<i><p>>",
        });

        $('#resetRecordsBtn').click(function () {
            if (confirm('Are you sure you want to reset the records? This action cannot be undone.')) {
                $.ajax({
                    url: '/admin/reset_auth_code_records',
                    method: 'POST',
                    success: function (response) {
                        alert(response.message);
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        alert('Error resetting records: ' + error);
                    }
                });
            }
        });

        // Chart initialization
        var ctx = document.getElementById('requestChart').getContext('2d');
        var chartData = {{ chart_data| safe
    }};
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.dates,
            datasets: [{
                label: 'Total Requests',
                data: chartData.total_requests,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }, {
                label: 'Successful Requests',
                data: chartData.successful_requests,
                borderColor: 'rgb(54, 162, 235)',
                tension: 0.1
            }, {
                label: 'Failed Requests',
                data: chartData.failed_requests,
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    function loadCooldowns() {
        $.ajax({
            url: '/admin/get_cooldowns',
            method: 'GET',
            success: function (response) {
                var tbody = $('#cooldownTable tbody');
                tbody.empty(); // Clear existing rows
                response.cooldowns.forEach(function (cooldown) {
                    var row = '<tr>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + cooldown.username + '</td>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + cooldown.order_id + '</td>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + cooldown.remaining_cooldown + '</td>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' +
                        '<button class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded reset-cooldown-btn" data-username="' + cooldown.username + '" data-order-id="' + cooldown.order_id + '">Reset</button>' +
                        '</td>' +
                        '</tr>';
                    tbody.append(row);
                });
                // Initialize DataTable if not already initialized
                if (!$.fn.DataTable.isDataTable('#cooldownTable')) {
                    $('#cooldownTable').DataTable({
                        responsive: true,
                        order: [[2, 'asc']],
                        pageLength: 10,
                        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        language: {
                            paginate: {
                                previous: "&#8592;",
                                next: "&#8594;"
                            },
                            lengthMenu: "_MENU_ per page",
                            search: "",
                            searchPlaceholder: "Search...",
                            info: "Showing _START_ to _END_ of _TOTAL_ entries"
                        },
                        dom: "<'flex justify-between items-center'<l><f>>" +
                            "<'overflow-x-auto'tr>" +
                            "<'flex justify-between items-center'<i><p>>",
                    });
                }
            },
            error: function (xhr, status, error) {
                console.error('Error fetching cooldowns:', error);
            }
        });
    }

    // Initial load of cooldowns
    loadCooldowns();

    // Handle Reset Cooldown button click
    $(document).on('click', '.reset-cooldown-btn', function () {
        var username = $(this).data('username');
        var order_id = $(this).data('order-id');
        if (confirm('Are you sure you want to reset the cooldown for ' + username + ' with Order ID ' + order_id + '?')) {
            $.ajax({
                url: '/admin/reset_cooldown',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    'username': username,
                    'order_id': order_id
                }),
                success: function (response) {
                    alert(response.message);
                    // Reload cooldowns after reset
                    $('#cooldownTable').DataTable().destroy(); // Destroy existing DataTable
                    loadCooldowns(); // Reload data and reinitialize DataTable
                },
                error: function (xhr, status, error) {
                    var errMsg = xhr.responseJSON && xhr.responseJSON.error ? xhr.responseJSON.error : 'An error occurred';
                    alert('Error resetting cooldown: ' + errMsg);
                }
            });
        }
    });

    function loadNetflixCooldowns() {
        $.ajax({
            url: '/admin/get_netflix_cooldowns',
            method: 'GET',
            success: function (response) {
                var tbody = $('#netflixCooldownTable tbody');
                tbody.empty(); // Clear existing rows
                response.cooldowns.forEach(function (cooldown) {
                    // Convert cooldown_until timestamp to Date object
                    var cooldownUntil = new Date(cooldown.cooldown_until * 1000);
                    
                    // Format remaining time using the provided remaining_seconds
                    var hours = Math.floor(cooldown.remaining_seconds / 3600);
                    var minutes = Math.floor((cooldown.remaining_seconds % 3600) / 60);
                    var seconds = Math.floor(cooldown.remaining_seconds % 60);
                    var remainingTimeFormatted =
                        String(hours).padStart(2, '0') + ':' +
                        String(minutes).padStart(2, '0') + ':' +
                        String(seconds).padStart(2, '0');

                    var row = '<tr>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + cooldown.order_sn + '</td>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + cooldownUntil.toLocaleString() + '</td>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + remainingTimeFormatted + '</td>' +
                        '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' +
                        '<button class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded reset-netflix-cooldown-btn" data-order-sn="' + cooldown.order_sn + '">Reset</button>' +
                        '</td>' +
                        '</tr>';
                    tbody.append(row);
                });
            },
            error: function (xhr, status, error) {
                console.error('Error fetching Netflix cooldowns:', error);
            }
        });
    }

    // Initial load of Netflix cooldowns
    loadNetflixCooldowns();

    // Handle Reset Netflix Cooldown button click
    $(document).on('click', '.reset-netflix-cooldown-btn', function () {
        var order_sn = $(this).data('order-sn');
        if (confirm('Are you sure you want to reset the cooldown for Order SN ' + order_sn + '?')) {
            $.ajax({
                url: '/admin/reset_netflix_cooldown',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    'order_sn': order_sn
                }),
                success: function (response) {
                    alert(response.message);
                    // Reload Netflix cooldowns after reset
                    loadNetflixCooldowns();
                },
                error: function (xhr, status, error) {
                    var errMsg = xhr.responseJSON && xhr.responseJSON.error ? xhr.responseJSON.error : 'An error occurred';
                    alert('Error resetting Netflix cooldown: ' + errMsg);
                }
            });
        }
    });

    // Refresh Netflix cooldowns every 30 seconds
    setInterval(loadNetflixCooldowns, 30000);
    });
</script>



{% endblock %}