#!/bin/bash
# Simple update script for ShopeeAPI manual deployment

# Configuration
SERVER_HOST="your-server-hostname"  # Change to your server hostname or IP
SERVER_USER="your-username"         # Change to your server username
SERVER_PATH="/path/to/shopee-api"   # Change to your server deployment path

# Create a timestamp for versioning
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_DIR="backups/$TIMESTAMP"

echo "Creating package for ShopeeAPI update..."

# Create backup directory
mkdir -p $BACKUP_DIR

# Create a tar archive of the ShopeeAPI directory
tar -czf $BACKUP_DIR/shopee-api.tar.gz ShopeeAPI/

# Copy the backup locally
cp $BACKUP_DIR/shopee-api.tar.gz shopee-api.tar.gz

echo "Uploading package to server..."
scp shopee-api.tar.gz $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

# Create update script
cat > remote-update.sh << EOL
#!/bin/bash
# Remote update script for ShopeeAPI

# Configuration
TIMESTAMP=\$(date +"%Y%m%d%H%M%S")
BACKUP_DIR="backups/\$TIMESTAMP"

# Create backup directory
mkdir -p \$BACKUP_DIR

# Backup current configuration
if [ -f "ShopeeAPI/config.json" ]; then
    cp ShopeeAPI/config.json \$BACKUP_DIR/config.json.backup
fi

# Backup current code
if [ -d "ShopeeAPI" ]; then
    tar -czf \$BACKUP_DIR/shopee-api-old.tar.gz ShopeeAPI/
fi

# Stop the service
if systemctl is-active --quiet shopee-api; then
    echo "Stopping ShopeeAPI service..."
    sudo systemctl stop shopee-api
fi

# Extract the new code
echo "Extracting new code..."
tar -xzf shopee-api.tar.gz

# Restore configuration
if [ -f "\$BACKUP_DIR/config.json.backup" ]; then
    echo "Restoring configuration..."
    cp \$BACKUP_DIR/config.json.backup ShopeeAPI/config.json
fi

# Update dependencies
if [ -d "venv" ]; then
    echo "Updating dependencies..."
    source venv/bin/activate
    pip install -r ShopeeAPI/requirements.txt
fi

# Start the service
if systemctl is-enabled --quiet shopee-api; then
    echo "Starting ShopeeAPI service..."
    sudo systemctl start shopee-api
fi

# Clean up
rm shopee-api.tar.gz

echo "Update completed successfully!"
EOL

# Make the script executable
chmod +x remote-update.sh

# Upload the script to the server
scp remote-update.sh $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

# Execute the script on the server
ssh $SERVER_USER@$SERVER_HOST "cd $SERVER_PATH && ./remote-update.sh"

# Clean up local files
rm shopee-api.tar.gz
rm remote-update.sh

echo "ShopeeAPI has been updated on the server!"
