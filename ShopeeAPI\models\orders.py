"""
Data models for order-related operations.
"""
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class OrderItem(BaseModel):
    """Model for an order item."""
    item_id: int
    model_id: int
    name: str
    variation_name: Optional[str] = None
    quantity: int
    price: float
    image_url: Optional[str] = None


class OrderAddress(BaseModel):
    """Model for an order shipping address."""
    name: str
    phone: str
    address: str
    city: str
    state: str
    region: str
    zipcode: str
    full_address: str


class OrderDetails(BaseModel):
    """Model for detailed order information."""
    order_sn: str
    order_id: int
    status: str
    create_time: int
    update_time: int
    buyer_username: str
    items: List[OrderItem]
    total_amount: float
    shipping_fee: float
    address: Optional[OrderAddress] = None
    note: Optional[str] = None
    tracking_number: Optional[str] = None
    shipping_carrier: Optional[str] = None


class OrderResponse(BaseModel):
    """Model for API responses containing order data."""
    data: Dict[str, Any]
    error: Optional[str] = None


class OrderStatusResponse(BaseModel):
    """Model for order status response."""
    order_sn: str
    status: str


class BuyerOrderProduct(BaseModel):
    """Model for a product in a buyer order."""
    product_id: int
    title: str
    image: str
    image_hash: str
    count: int
    price: float
    model_name: Optional[str] = None
    model_id: Optional[int] = None
    rate_star: Optional[str] = None
    rate_comment: Optional[str] = None
    status: Optional[int] = None
    stock: Optional[int] = None
    coin_offset: Optional[int] = None
    sku: Optional[str] = None
    is_pre_order: Optional[bool] = None
    is_vsku: Optional[bool] = None
    vsku_item_id: Optional[int] = None


class BuyerOrder(BaseModel):
    """Model for a buyer order."""
    id: int
    shop_id: int
    serial_number: str
    title: str
    image: str
    image_hash: str
    status: str
    products_count: int
    carrier: Optional[str] = None
    tracking_number: Optional[str] = None
    seller_id: int
    buyer_id: int
    refund_serial_number: Optional[str] = None
    refund_amount: float = 0
    payment_method: Optional[int] = None
    products: List[BuyerOrderProduct]
    remark: Optional[str] = None
    seller_note: Optional[str] = None
    seller_rate_star: Optional[int] = None
    seller_rate_comment: Optional[str] = None
    created_at: str
    updated_at: str
    ship_by_date: Optional[str] = None
    pay_time: Optional[str] = None
    delivery_time: Optional[str] = None
    complete_time: Optional[str] = None
    rate_by_date: Optional[str] = None
    rate_cancel_by_date: Optional[str] = None
    buyer_cancel_pending_time: Optional[str] = None
    cancel_time: Optional[str] = None
    cancel_reason: Optional[int] = None
    cancel_user_id: Optional[int] = None
    status_ext: Optional[int] = None
    receivables: Optional[float] = None
    payables: Optional[float] = None
    shipping_fee: Optional[float] = None
    tax_amount: Optional[float] = None
    voucher_price: Optional[float] = None
    credit_card_promotion: Optional[float] = None
    price_before_discount: Optional[float] = None
    total_price: Optional[float] = None
    estimated_escrow: Optional[float] = None
    wallet_discount: Optional[float] = None
    pay_by_wallet: Optional[bool] = None
    buyer_is_rated: Optional[int] = None
    buyer_cancel_reason: Optional[int] = None
    logistics_status: Optional[int] = None
    pickup_cutoff_time: Optional[str] = None
    list_type: Optional[int] = None
    shipping_method: Optional[int] = None
    order_type: Optional[int] = None
    third_party_tns: Optional[List[str]] = None
    checkout_id: Optional[int] = None
    return_id: Optional[int] = None
    return_type: Optional[int] = None


class BuyerOrdersResponse(BaseModel):
    """Model for buyer orders response."""
    orders: List[BuyerOrder]


class ShipOrderRequest(BaseModel):
    """Model for ship order request."""
    order_sn: str
    tracking_number: Optional[str] = None
    shipping_carrier: Optional[str] = None
