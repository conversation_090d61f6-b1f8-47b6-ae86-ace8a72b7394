<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redemption Error</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .neumorphic {
            background: #f0f4f8;
            box-shadow: 
                8px 8px 16px rgba(174, 174, 192, 0.4),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
            border-radius: 1rem;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .shake {
            animation: shake 0.5s ease-in-out;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-red-50 to-pink-50 min-h-screen flex items-center justify-center p-4">
    <div class="neumorphic p-8 max-w-md w-full">
        <div class="text-center">
            <div class="mb-6">
                <svg class="mx-auto h-16 w-16 text-red-500 shake" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-4">Redemption Error</h1>
            <p class="text-gray-600 mb-6">{{ error }}</p>
            <div class="space-y-4">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <p class="text-sm text-gray-500">This link can only be used once.</p>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <p class="text-sm text-gray-500">Do not share or refresh this page.</p>
                </div>
            </div>
        </div>
    </div>
    <script>
        function isShopeeWebView() {
            const userAgent = navigator.userAgent;
            console.log('Current User Agent:', userAgent);  // 输出完整的 User-Agent
            
            const userAgentLower = userAgent.toLowerCase();
            console.log('Lowercase User Agent:', userAgentLower);  // 输出小写的 User-Agent
            
            const isShopee = userAgentLower.includes('shopee');
            console.log('Is Shopee WebView:', isShopee);  // 输出检测结果
            
            return isShopee;
        }

        // 在页面加载时执行检测
        document.addEventListener('DOMContentLoaded', function() {
            isShopeeWebView();
        });
    </script>
</body>
</html> 