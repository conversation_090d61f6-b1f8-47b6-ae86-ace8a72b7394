{% extends "base.html" %}

{% block title %}Canva Management{% endblock %}
{% block header %}Canva Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="canvaData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Sidebar -->
        <div class="w-64 mr-8">
            <nav class="space-y-1">
                <template x-for="(section, index) in ['Canva Orders', 'Canva Config']" :key="index">
                    <a href="#" @click.prevent="currentSection = section; animateSection()"
                        :class="{'bg-gray-100 text-gray-900': currentSection === section, 'text-gray-600 hover:bg-gray-50 hover:text-gray-900': currentSection !== section}"
                        class="group flex items-center px-3 py-2 text-sm font-medium rounded-md sidebar-item">
                        <span x-text="section"></span>
                    </a>
                </template>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1">
            <!-- Canva Orders Section -->
            <div x-show="currentSection === 'Canva Orders'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Canva Orders Management</h2>
                <div class="mb-6">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order SN</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="order in orders" :key="order.order_sn">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.order_sn"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.expiry_date"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.redeemed ? 'Redeemed' : 'Not Redeemed'"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click="resetOrder(order.order_sn)" 
                                            class="text-yellow-600 hover:text-yellow-900">Reset</button>
                                        <button @click="extendValidity(order.order_sn)"
                                            class="ml-2 text-blue-600 hover:text-blue-900">Extend</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Canva Config Section -->
            <div x-show="currentSection === 'Canva Config'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Canva Configuration</h2>
                
                <!-- Account Types Configuration -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Account Types</h3>
                    <div class="space-y-4">
                        <template x-for="(typeConfig, typeName) in config.types" :key="typeName">
                            <div class="border rounded-lg p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="text-lg font-medium" x-text="typeConfig.name"></h4>
                                    <button @click="deleteType(typeName)" 
                                        class="text-red-600 hover:text-red-900">Delete Type</button>
                                </div>
                                
                                <!-- Invitation Links -->
                                <div class="space-y-2">
                                    <h5 class="text-sm font-medium text-gray-700">Invitation Links</h5>
                                    <template x-for="(link, index) in typeConfig.invitation_links" :key="index">
                                        <div class="flex items-center space-x-2">
                                            <input type="text" x-model="typeConfig.invitation_links[index]"
                                                class="flex-1 border rounded px-2 py-1 text-sm">
                                            <button @click="removeLink(typeName, index)"
                                                class="text-red-600 hover:text-red-900">Remove</button>
                                        </div>
                                    </template>
                                    <button @click="addLink(typeName)"
                                        class="text-blue-600 hover:text-blue-900 text-sm">+ Add Link</button>
                                </div>
                            </div>
                        </template>
                        
                        <!-- Add New Type Button -->
                        <button @click="addNewType"
                            class="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-600 hover:border-gray-400 hover:text-gray-700">
                            + Add New Account Type
                        </button>
                    </div>
                </div>

                <!-- SKU Configuration -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">SKU Configuration</h3>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validity (Days)</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(sku_config, sku) in config.SKU_VALIDITY" :key="sku">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="sku"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="number" x-model.number="sku_config.validity" class="w-20 border rounded">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <select x-model="sku_config.type" class="border rounded">
                                            <template x-for="(_, type) in config.types" :key="type">
                                                <option :value="type" x-text="type"></option>
                                            </template>
                                        </select>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click="deleteSku(sku)" class="text-red-600 hover:text-red-900">Delete</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    <button @click="addNewSku" class="mt-4 text-blue-600 hover:text-blue-900">+ Add New SKU</button>
                </div>

                <button @click="saveConfig" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function canvaData() {
    return {
        orders: [],
        config: {
            INVITATION_LINK_BASE: '',
            SKU_VALIDITY: {}
        },
        currentSection: 'Canva Orders',
        isLoaded: false,

        async init() {
            await this.loadOrders();
            await this.loadConfig();
            this.isLoaded = true;
            
            // 添加定期刷新
            setInterval(() => {
                if (this.currentSection === 'Canva Orders') {
                    this.loadOrders();
                }
            }, 5000); // 每5秒刷新一次
        },

        async loadOrders() {
            try {
                const response = await fetch('/api/canva_orders');
                const data = await response.json();
                this.orders = Object.entries(data).map(([order_sn, details]) => ({
                    order_sn,
                    ...details
                }));
            } catch (error) {
                console.error('Error loading orders:', error);
            }
        },

        async loadConfig() {
            try {
                const response = await fetch('/api/canva/config');
                this.config = await response.json();
            } catch (error) {
                console.error('Error loading config:', error);
            }
        },

        async resetOrder(order_sn) {
            if (!confirm('Are you sure you want to reset this order?')) return;
            
            try {
                const response = await fetch('/api/canva/reset_order', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ order_sn })
                });
                
                if (response.ok) {
                    await this.loadOrders();
                    alert('Order reset successfully');
                } else {
                    throw new Error('Failed to reset order');
                }
            } catch (error) {
                console.error('Error resetting order:', error);
                alert('Failed to reset order');
            }
        },

        async extendValidity(order_sn) {
            const days = prompt('Enter number of days to extend:');
            if (!days || isNaN(days)) return;

            try {
                const response = await fetch('/api/canva/extend_validity', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ order_sn, days: parseInt(days) })
                });

                if (response.ok) {
                    await this.loadOrders();
                    alert('Validity extended successfully');
                } else {
                    throw new Error('Failed to extend validity');
                }
            } catch (error) {
                console.error('Error extending validity:', error);
                alert('Failed to extend validity');
            }
        },

        async saveConfig() {
            try {
                const response = await fetch('/api/canva/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(this.config)
                });

                if (response.ok) {
                    alert('Configuration saved successfully');
                } else {
                    throw new Error('Failed to save configuration');
                }
            } catch (error) {
                console.error('Error saving config:', error);
                alert('Failed to save configuration');
            }
        },

        deleteSku(sku) {
            if (confirm(`Are you sure you want to delete SKU ${sku}?`)) {
                delete this.config.SKU_VALIDITY[sku];
            }
        },

        addNewType() {
            const typeName = prompt('Enter new type name:');
            if (!typeName) return;
            
            if (!this.config.types) {
                this.config.types = {};
            }
            
            this.config.types[typeName.toLowerCase()] = {
                name: typeName,
                invitation_links: []
            };
        },

        deleteType(typeName) {
            if (confirm(`Are you sure you want to delete type ${typeName}?`)) {
                delete this.config.types[typeName];
            }
        },

        addLink(typeName) {
            const link = prompt('Enter invitation link:');
            if (!link) return;
            this.config.types[typeName].invitation_links.push(link);
        },

        removeLink(typeName, index) {
            this.config.types[typeName].invitation_links.splice(index, 1);
        },

        addNewSku() {
            const sku = prompt('Enter SKU name:');
            if (!sku) return;
            
            this.config.SKU_VALIDITY[sku] = {
                validity: 30,
                type: Object.keys(this.config.types)[0] || 'team'
            };
        }
    }
}
</script>
{% endblock %}
