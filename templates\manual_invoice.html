{% extends "base.html" %}

{% block title %}Manual Invoice{% endblock %}
{% block header %}Manual Invoice{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="manualInvoiceData()" x-init="init()">
    <!-- Create Payment Link Button -->
    <div class="mb-6">
        <button @click="showModal()" 
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
            Create New Payment Link
        </button>
    </div>

    <!-- Payment Links Table -->
    <div class="bg-white shadow-sm rounded-lg p-6">
        <h2 class="text-2xl font-bold mb-6">Payment Links</h2>
        <table id="paymentLinksTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Username</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Created At</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <!-- Populated by JavaScript -->
            </tbody>
        </table>
    </div>

    <!-- Modal -->
    <div x-show="isModalOpen" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <!-- Form content -->
            <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Username -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                        <input type="text" x-model="formData.username" 
                            class="mt-1 block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm 
                            focus:border-green-500 focus:ring-1 focus:ring-green-500 
                            placeholder-gray-400 transition duration-150 ease-in-out">
                    </div>

                    <!-- Amount -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Amount (MYR) *</label>
                        <input type="number" x-model="formData.amount" step="0.01" required
                            class="mt-1 block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm 
                            focus:border-green-500 focus:ring-1 focus:ring-green-500 
                            placeholder-gray-400 transition duration-150 ease-in-out">
                    </div>

                    <!-- VAR SKU -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">VAR SKU *</label>
                        <select x-model="formData.var_sku" required
                            class="mt-1 block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm 
                            focus:border-green-500 focus:ring-1 focus:ring-green-500 
                            bg-white transition duration-150 ease-in-out">
                            <option value="">Select SKU</option>
                            <template x-for="sku in availableSkus" :key="sku">
                                <option x-text="sku" :value="sku"></option>
                            </template>
                        </select>
                    </div>

                    <!-- Expiry Date -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                        <input type="datetime-local" x-model="formData.expire_by"
                            class="mt-1 block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm 
                            focus:border-green-500 focus:ring-1 focus:ring-green-500 
                            placeholder-gray-400 transition duration-150 ease-in-out">
                        <p class="mt-1 text-sm text-gray-500">Leave empty for default (3 days)</p>
                    </div>

                    <!-- Description -->
                    <div class="col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
                        <textarea x-model="formData.description" required rows="3"
                            class="mt-1 block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm 
                            focus:border-green-500 focus:ring-1 focus:ring-green-500 
                            placeholder-gray-400 transition duration-150 ease-in-out resize-y"
                            placeholder="Enter description here..."></textarea>
                    </div>

                    <!-- Email -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" x-model="formData.email"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                    </div>

                    <!-- Phone -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone</label>
                        <input type="tel" x-model="formData.phone"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                    </div>

                    <!-- Reference ID -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Reference ID</label>
                        <input type="text" x-model="formData.reference_id"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                    </div>

                    <!-- Checkout Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Checkout Name</label>
                        <input type="text" x-model="formData.checkout_name"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                        <p class="mt-1 text-sm text-gray-500">Custom name to display on the checkout page</p>
                    </div>

                    <!-- Accept Partial Payment -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" x-model="formData.accept_partial" id="accept_partial"
                                class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500">
                            <label for="accept_partial" class="ml-2 block text-sm text-gray-700">Accept Partial Payment</label>
                        </div>
                        <div x-show="formData.accept_partial" class="mt-2">
                            <label class="block text-sm font-medium text-gray-700">Minimum First Payment (MYR)</label>
                            <input type="number" x-model="formData.first_min_partial_amount" step="0.01"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <div class="space-y-3">
                        <template x-for="(note, index) in formData.notes" :key="index">
                            <div class="flex space-x-2">
                                <input type="text" x-model="note.key" placeholder="Key"
                                    class="w-1/3 rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                                <input type="text" x-model="note.value" placeholder="Value"
                                    class="w-1/2 rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500">
                                <button type="button" @click="removeNote(index)"
                                    class="px-2 py-1 bg-red-500 text-white rounded-md hover:bg-red-600">
                                    Remove
                                </button>
                            </div>
                        </template>
                        <button type="button" @click="addNote"
                            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                            Add Note
                        </button>
                    </div>
                </div>
            </form>

            <!-- Modal footer -->
            <div class="mt-6 flex justify-end space-x-3">
                <button @click="hideModal()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                    Cancel
                </button>
                <button @click="createPaymentLink()" 
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    Create Payment Link
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function manualInvoiceData() {
    return {
        isLoaded: false,
        isModalOpen: false,
        availableSkus: [],
        paymentLinksTable: null,
        formData: {
            username: '',
            amount: '',
            quantity: 1,
            var_sku: '',
            description: '',
            email: '',
            phone: '',
            reference_id: '',
            expire_by: '',
            accept_partial: false,
            first_min_partial_amount: '',
            checkout_name: '',
            notes: []
        },
        
        init() {
            if (!this.isLoaded) {
                this.loadSkus();
                this.initializeDataTable();
                this.isLoaded = true;
            }
        },

        showModal() {
            this.isModalOpen = true;
            document.body.style.overflow = 'hidden';
        },

        hideModal() {
            this.isModalOpen = false;
            document.body.style.overflow = 'auto';
        },

        initializeDataTable() {
            const self = this;
            this.paymentLinksTable = $('#paymentLinksTable').DataTable({
                ajax: {
                    url: '/admin/get_payment_links',
                    dataSrc: function(json) {
                        return Object.values(json);
                    }
                },
                columns: [
                    { data: 'id' },
                    { data: 'customer.name' },
                    { 
                        data: 'amount',
                        render: function(data) {
                            return `MYR ${(data/100).toFixed(2)}`;
                        }
                    },
                    { data: 'status' },
                    { 
                        data: 'created_at',
                        render: function(data) {
                            return new Date(data * 1000).toLocaleString();
                        }
                    },
                    {
                        data: 'short_url',
                        render: function(data) {
                            return `<a href="${data}" target="_blank" class="text-blue-600 hover:text-blue-800">View Link</a>`;
                        }
                    }
                ],
                order: [[4, 'desc']],
                pageLength: 10,
                responsive: true,
                language: {
                    paginate: {
                        previous: "&#8592;",
                        next: "&#8594;"
                    },
                    lengthMenu: "_MENU_ per page",
                    search: "",
                    searchPlaceholder: "Search...",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries"
                }
            });
        },

        refreshTable() {
            if (this.paymentLinksTable) {
                this.paymentLinksTable.ajax.reload();
            }
        },

        loadSkus() {
            fetch('/admin/get_config')
                .then(response => response.json())
                .then(data => {
                    this.availableSkus = data.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.map(sku => sku.sku);
                })
                .catch(error => {
                    console.error('Error loading SKUs:', error);
                });
        },

        addNote() {
            this.formData.notes.push({ key: '', value: '' });
        },

        removeNote(index) {
            this.formData.notes.splice(index, 1);
        },

        createPaymentLink() {
            // Convert notes array to object
            const notesObject = {};
            this.formData.notes.forEach(note => {
                if (note.key && note.value) {
                    notesObject[note.key] = note.value;
                }
            });

            // Set up notifications based on user input
            const notify = {};
            if (this.formData.email && this.formData.email.trim()) {
                notify.email = true;
            }
            if (this.formData.phone && this.formData.phone.trim()) {
                notify.sms = true;
            }

            // Calculate expiry time (3 days from now)
            const threeDaysFromNow = new Date();
            threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
            const expiryTimestamp = Math.floor(threeDaysFromNow.getTime() / 1000);

            // Generate a reference ID if not provided
            const referenceId = this.formData.reference_id || `INV_${Date.now()}`;

            // Convert amount to smallest currency unit (cents)
            const amountInCents = Math.round(parseFloat(this.formData.amount) * 100);

            // Set default customer values if empty
            const customerName = this.formData.username || 'None';
            const customerEmail = this.formData.email || '<EMAIL>';
            const customerPhone = this.formData.phone || '+6000000000';

            // Prepare payment link data
            const paymentData = {
                amount: amountInCents,
                currency: 'MYR',
                customer: {
                    name: customerName,
                    email: customerEmail,
                    contact: customerPhone
                },
                description: this.formData.description,
                reference_id: referenceId,
                var_sku: this.formData.var_sku,
                accept_partial: this.formData.accept_partial,
                first_min_partial_amount: this.formData.accept_partial && this.formData.first_min_partial_amount ? 
                    Math.round(parseFloat(this.formData.first_min_partial_amount) * 100) : undefined,
                expire_by: expiryTimestamp,
                notes: Object.keys(notesObject).length > 0 ? notesObject : undefined,
                reminder_enable: true,
                notify: Object.keys(notify).length > 0 ? notify : undefined,
                options: this.formData.checkout_name ? {
                    checkout: {
                        name: this.formData.checkout_name
                    }
                } : undefined
            };

            // Validate required fields (only amount and description are truly required now)
            if (!paymentData.amount || !this.formData.var_sku || !this.formData.description) {
                alert('Please fill in all required fields (Amount, VAR SKU, Description)');
                return;
            }

            // Create payment link
            fetch('/api/curlec/payment-links', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'MTYB_OFFICIAL'
                },
                body: JSON.stringify(paymentData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error.description || 'Failed to create payment link');
                }
                alert('Payment link created successfully!');
                this.hideModal();
                this.refreshTable();
            })
            .catch(error => {
                console.error('Error creating payment link:', error);
                alert('Failed to create payment link: ' + error.message);
            });
        }
    }
}
</script>

<style>
/* DataTable Custom Styling */
.dataTables_wrapper {
    font-family: 'Arial', sans-serif;
    padding: 1rem;
}

/* Fix the length and filter positioning */
.dataTables_wrapper .dataTables_length {
    float: left;
    margin: 15px 0;
    font-size: 14px;
}

.dataTables_wrapper .dataTables_filter {
    float: right;
    margin: 15px 0;
    font-size: 14px;
}

/* Clear the float after length and filter */
.dataTables_wrapper .dataTables_length::after,
.dataTables_wrapper .dataTables_filter::after {
    content: "";
    clear: both;
    display: table;
}

/* Rest of your existing styles */
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin: 15px 0;
    font-size: 14px;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 5px 10px;
    margin-left: 8px; /* Add some spacing between label and input */
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 5px 25px 5px 10px;
    margin: 0 8px; /* Add some spacing around the select */
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: none;
    padding: 5px 10px;
    margin: 0 2px;
    border-radius: 4px;
    background-color: #f7fafc;
    color: #4a5568;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #4a5568;
    color: white !important;
}
</style>
{% endblock %} 