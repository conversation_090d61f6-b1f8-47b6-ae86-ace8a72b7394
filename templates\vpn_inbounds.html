{% extends "base.html" %}

{% block title %}VPN Inbounds Management{% endblock %}
{% block header %}VPN Inbounds Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="vpnInboundsData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Main content -->
        <div class="flex-1">
            <!-- Add New Inbound Form -->
            <div class="mb-8 bg-white shadow rounded-lg p-6">
                <div class="config-item col-span-2">
                    <label for="server" class="block text-sm font-medium text-gray-700">Server</label>
                    <select id="server" x-model="newInbound.server"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="">Select a server</option>
                        <template x-for="server in servers" :key="server.domain">
                            <option :value="server.domain + ':' + server.port"
                                x-text="server.domain + ' (' + server.status + ')'"></option>
                        </template>
                    </select>
                </div>

                <h2 class="text-2xl font-bold mb-4">Add New Inbound</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="config-item">
                        <label for="port" class="block text-sm font-medium text-gray-700">Port</label>
                        <input id="port" type="number" x-model="newInbound.port"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="2053">
                    </div>
                    <div class="config-item">
                        <label for="protocol" class="block text-sm font-medium text-gray-700">Protocol</label>
                        <select id="protocol" x-model="newInbound.protocol"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="vless">VLESS</option>
                            <option value="vmess">VMess</option>
                            <option value="trojan">Trojan</option>
                        </select>
                    </div>
                    <div class="config-item">
                        <label for="network" class="block text-sm font-medium text-gray-700">Network</label>
                        <select id="network" x-model="newInbound.network"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="tcp">TCP</option>
                            <option value="ws">WebSocket</option>
                            <option value="grpc">gRPC</option>
                        </select>
                    </div>
                    <div class="config-item">
                        <label for="security" class="block text-sm font-medium text-gray-700">Security</label>
                        <select id="security" x-model="newInbound.security"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="none">None</option>
                            <option value="tls">TLS</option>
                            <option value="reality">Reality</option>
                        </select>
                    </div>
                </div>
                <button @click="addInbound"
                    class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Add Inbound
                </button>
            </div>

            <!-- Inbounds Table -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-2xl font-bold mb-4">VPN Inbounds</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Server</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Port</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Protocol</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Network</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Security</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Clients</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-if="!inbounds || inbounds.length === 0">
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        No inbounds found
                                    </td>
                                </tr>
                            </template>
                            <template x-for="(inbound, index) in inbounds" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="inbound?.server || ''"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="inbound?.port || ''"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="inbound?.protocol || ''"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="getStreamSettings(inbound)?.network || ''"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="getStreamSettings(inbound)?.security || ''"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="getClientCount(inbound)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button @click="editInbound(inbound)" 
                                            class="text-indigo-600 hover:text-indigo-900 mr-2">Edit</button>
                                        <button @click="deleteInbound(inbound)" 
                                            class="text-red-600 hover:text-red-900">Delete</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading VPN inbounds...</p>
    </div>

    <!-- Edit Modal -->
    <div x-show="editingInbound !== null"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 class="text-lg font-medium mb-4">Edit Inbound</h3>
            <div class="space-y-4" x-show="editingInbound">
                <!-- Server Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Server</label>
                    <select x-model="editingInbound.server"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="">Select a server</option>
                        <template x-for="server in servers" :key="server.domain">
                            <option :value="server.domain + ':' + server.port"
                                x-text="server.domain + ' (' + server.status + ')'"></option>
                        </template>
                    </select>
                </div>

                <!-- Port -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Port</label>
                    <input type="number" x-model="editingInbound.port"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>

                <!-- Protocol -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Protocol</label>
                    <select x-model="editingInbound.protocol"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="vless">VLESS</option>
                        <option value="vmess">VMess</option>
                        <option value="trojan">Trojan</option>
                    </select>
                </div>

                <!-- Network -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Network</label>
                    <select x-model="editingInbound.network"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="tcp">TCP</option>
                        <option value="ws">WebSocket</option>
                        <option value="grpc">gRPC</option>
                    </select>
                </div>

                <!-- Security -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Security</label>
                    <select x-model="editingInbound.security"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="none">None</option>
                        <option value="tls">TLS</option>
                        <option value="reality">Reality</option>
                    </select>
                </div>
            </div>

            <!-- Modal Buttons -->
            <div class="mt-6 flex justify-end space-x-3">
                <button @click="editingInbound = null"
                    class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button @click="saveEdit"
                    class="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function vpnInboundsData() {
        return {
            servers: [],
            inbounds: [],
            newInbound: {
                server: '',
                port: '',
                protocol: 'vless',
                network: 'tcp',
                security: 'none'
            },
            editingInbound: null,
            isLoaded: false,

            init() {
                this.editingInbound = null;
                this.inbounds = [];
                
                // Add console log to track initialization
                console.log('Initializing vpnInboundsData');
                
                this.loadServers()
                    .then(() => this.loadInbounds())
                    .then(() => {
                        console.log('Data loading complete');
                    })
                    .catch(error => {
                        console.error('Error during initialization:', error);
                    });
            },

            loadInbounds() {
                console.log('Loading inbounds...');
                return fetch('/admin/vpn/inbounds/list')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Process and normalize the inbounds data
                            const processedInbounds = (data.obj || []).map((inbound, index) => {
                                try {
                                    // Ensure each inbound has a unique identifier
                                    const processed = {
                                        ...inbound,
                                        uniqueId: index, // Add a unique ID if none exists
                                        settings: this.parseJsonSafely(inbound.settings),
                                        streamSettings: this.parseJsonSafely(inbound.streamSettings),
                                        allocate: this.parseJsonSafely(inbound.allocate)
                                    };
                                    return processed;
                                } catch (e) {
                                    console.error('Error processing inbound:', e, inbound);
                                    return null;
                                }
                            }).filter(Boolean); // Remove any null entries

                            this.inbounds = processedInbounds;
                            console.log('Processed inbounds:', this.inbounds);
                            this.isLoaded = true;
                        } else {
                            console.error('Failed to load inbounds:', data.msg);
                            this.inbounds = [];
                        }
                    })
                    .catch(error => {
                        console.error('Error loading inbounds:', error);
                        this.inbounds = [];
                        this.isLoaded = true;
                    });
            },

            parseJsonSafely(value) {
                if (!value) return null;
                try {
                    return typeof value === 'string' ? JSON.parse(value) : value;
                } catch (e) {
                    console.error('Error parsing JSON:', e, value);
                    return null;
                }
            },

            getStreamSettings(inbound) {
                if (!inbound) return { network: 'unknown', security: 'none' };
                try {
                    const settings = this.parseJsonSafely(inbound.streamSettings);
                    return {
                        network: settings?.network || 'unknown',
                        security: settings?.security || 'none'
                    };
                } catch (e) {
                    console.error('Error getting stream settings:', e);
                    return { network: 'unknown', security: 'none' };
                }
            },

            getClientCount(inbound) {
                if (!inbound) return 0;
                try {
                    const settings = this.parseJsonSafely(inbound.settings);
                    return settings?.clients?.length || 0;
                } catch (e) {
                    console.error('Error getting client count:', e);
                    return 0;
                }
            },

            loadServers() {
                return fetch('/admin/vpn/get_servers')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.servers = data.servers;
                        } else {
                            console.error('Failed to load servers:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error loading servers:', error);
                    });
            },
            

            addInbound() {
                if (!this.newInbound.server) {
                    alert('Please select a server');
                    return;
                }
                if (!this.newInbound.port) {
                    alert('Port is required');
                    return;
                }

                const inboundData = this.prepareInboundData(this.newInbound);
                if (!inboundData) return;

                fetch('/admin/vpn/inbounds/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(inboundData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.loadInbounds();
                            this.newInbound = {
                                server: '',
                                port: '',
                                protocol: 'vless',
                                network: 'tcp',
                                security: 'none'
                            };
                        } else {
                            alert(data.msg || 'Failed to add inbound');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to add inbound');
                    });
            },

            editInbound(inbound) {
                // Get the existing inbound details first
                fetch(`/admin/vpn/inbounds/${inbound.id}?server=${inbound.server}&serverPort=${inbound.serverPort}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const inboundData = data.obj;
                            const streamSettings = JSON.parse(inboundData.streamSettings);
                            const settings = JSON.parse(inboundData.settings);

                            // Create a new object with all required properties
                            this.editingInbound = {
                                id: inbound.id,
                                server: inbound.server + ':' + inbound.serverPort,
                                port: inboundData.port,
                                protocol: inboundData.protocol,
                                network: streamSettings.network || 'tcp',
                                security: streamSettings.security || 'none',
                                // Keep existing client information
                                clients: settings.clients || []
                            };
                        } else {
                            alert(data.msg || 'Failed to load inbound details');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to load inbound details');
                    });
            },

            saveEdit() {
                if (!this.editingInbound) return;

                const inboundData = this.prepareInboundData(this.editingInbound);
                if (!inboundData) return;

                fetch(`/admin/vpn/inbounds/update/${this.editingInbound.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(inboundData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.loadInbounds();
                            this.editingInbound = null;
                        } else {
                            alert(data.msg || 'Failed to update inbound');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to update inbound');
                    });
            },

            deleteInbound(inbound) {
                if (!confirm('Are you sure you want to delete this inbound?')) return;

                fetch(`/admin/vpn/inbounds/del/${inbound.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        server: inbound.server,
                        serverPort: inbound.serverPort
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.loadInbounds();
                        } else {
                            alert(data.msg || 'Failed to delete inbound');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to delete inbound');
                    });
            },

            prepareInboundData(inbound) {
                if (!inbound.server) {
                    alert('Please select a server');
                    return null;
                }

                const [serverDomain, serverPort] = inbound.server.split(':');

                return {
                    server: serverDomain,
                    serverPort: serverPort,
                    port: parseInt(inbound.port),
                    protocol: inbound.protocol,
                    id: inbound.id, // Pass existing ID if available
                    network: inbound.network,
                    security: inbound.security,
                    clients: inbound.clients // Pass existing clients if available
                };
            }
        }
    }
</script>
{% endblock %}