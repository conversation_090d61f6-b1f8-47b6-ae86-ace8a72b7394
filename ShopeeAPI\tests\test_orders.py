"""
Tests for the order functionality of the ShopeeAPI.
"""
import unittest
import requests
import json
import sys
import os
import time
from unittest.mock import patch, MagicMock

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from ShopeeAPI.client import ShopeeAPI
except ImportError:
    from client import ShopeeAPI


class TestOrdersAPI(unittest.TestCase):
    """Test cases for the orders API endpoints."""
    
    API_URL = "http://localhost:8000"
    
    def setUp(self):
        """Set up test environment."""
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        # Default test order number
        self.test_order_sn = "2412064GY1714Y"
    
    def test_to_ship_endpoint(self):
        """Test the /orders/to_ship endpoint."""
        # Skip this test if the API server is not running
        try:
            requests.get(f"{self.API_URL}/status", timeout=1)
        except requests.RequestException:
            self.skipTest("API server is not running")
            
        # Call the endpoint
        response = requests.get(f"{self.API_URL}/orders/to_ship")
        
        # Check if the request was successful
        self.assertEqual(response.status_code, 200)
        
        # Verify response structure
        response_json = response.json()
        self.assertIn("data", response_json)
        self.assertIn("card_list", response_json["data"])
    
    def test_search_order_endpoint(self):
        """Test the /orders/search endpoint."""
        # Skip this test if the API server is not running
        try:
            requests.get(f"{self.API_URL}/status", timeout=1)
        except requests.RequestException:
            self.skipTest("API server is not running")
            
        # Call the endpoint
        response = requests.get(
            f"{self.API_URL}/orders/search",
            params={"order_sn": self.test_order_sn}
        )
        
        # Check if the request was successful
        self.assertEqual(response.status_code, 200)
        
        # Verify response structure
        response_json = response.json()
        self.assertIn("data", response_json)


class TestOrdersClient(unittest.TestCase):
    """Test cases for the order functionality in the ShopeeAPI client."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a mock ShopeeAPI instance
        self.api = MagicMock(spec=ShopeeAPI)
        
        # Default test order number
        self.test_order_sn = "2412064GY1714Y"
    
    @patch('ShopeeAPI.client.ShopeeAPI')
    def test_get_to_ship_orders(self, mock_api):
        """Test getting to-ship orders using the ShopeeAPI client."""
        # Configure the mock
        mock_response = {
            "data": {
                "card_list": [
                    {"order_sn": "123456789", "status": "To Ship"}
                ]
            }
        }
        mock_api.return_value.get_to_ship_orders.return_value = mock_response
        
        # Create an instance of the mocked API
        api = mock_api()
        
        # Call the method
        response = api.get_to_ship_orders()
        
        # Assertions
        self.assertEqual(response, mock_response)
        self.assertEqual(len(response["data"]["card_list"]), 1)
        
        # Verify the method was called
        api.get_to_ship_orders.assert_called_once()
    
    @patch('ShopeeAPI.client.ShopeeAPI')
    def test_search_order(self, mock_api):
        """Test searching for an order using the ShopeeAPI client."""
        # Configure the mock
        mock_response = {
            "data": {
                "card_list": [
                    {"order_sn": "2412064GY1714Y", "status": "To Ship"}
                ]
            }
        }
        mock_api.return_value.search_order.return_value = mock_response
        
        # Create an instance of the mocked API
        api = mock_api()
        
        # Call the method
        response = api.search_order(self.test_order_sn)
        
        # Assertions
        self.assertEqual(response, mock_response)
        self.assertEqual(response["data"]["card_list"][0]["order_sn"], self.test_order_sn)
        
        # Verify the method was called with the correct parameters
        api.search_order.assert_called_once_with(self.test_order_sn)


if __name__ == "__main__":
    unittest.main()
