import imaplib
import email
import re
import time
from datetime import datetime, timedelta
from config import load_config
from services.netflix_session_service import netflix_session_manager
from services.order_service import get_order_details, ship_order
import logging
from utils.var_sku_extractor import extract_var_sku
import random
import pytz
import json
import os
from collections import defaultdict
from config import load_config
from services.chat_service import send_chat_message

logger = logging.getLogger(__name__)

NETFLIX_SIGNIN_CODE_PATTERN = r'<td[^>]*class="[^"]*copy\s+lrg-number\s+regular\s+content-padding[^"]*"[^>]*style="[^"]*padding-left:\s*40px;\s*padding-right:\s*40px;\s*font-size:\s*28px;\s*line-height:\s*32px;\s*letter-spacing:\s*6px;\s*font-family:\s*\'Netflix Sans\',\s*Helvetica,\s*Roboto,\s*Segoe UI,\s*sans-serif;\s*font-weight:\s*400;\s*color:\s*#232323;\s*padding-top:\s*20px;[^"]*"[^>]*>\s*(\d{4})\s*</td>'
NETFLIX_SIGNIN_CODE_PATTERN_MALAY = r'<td[^>]*class="[^"]*content-padding[^"]*"[^>]*style="[^"]*padding-left:\s*40px;\s*padding-right:\s*40px;\s*font-size:\s*28px;\s*line-height:\s*32px;\s*letter-spacing:\s*6px;\s*font-family:\s*\'Netflix Sans\'[^>]*>\s*(\d{4})\s*</td>'
NETFLIX_SESSIONS_FILE = 'netflix_sessions.json'
NETFLIX_ACCOUNT_LIMIT = 5


def save_netflix_sessions(sessions):
    with open(NETFLIX_SESSIONS_FILE, 'w') as f:
        json.dump(sessions, f, indent=2)

def initialize_netflix_sessions():
    if not os.path.exists(NETFLIX_SESSIONS_FILE) or os.path.getsize(NETFLIX_SESSIONS_FILE) == 0:
        save_netflix_sessions({})

initialize_netflix_sessions()

def load_netflix_sessions():
    if os.path.exists(NETFLIX_SESSIONS_FILE):
        with open(NETFLIX_SESSIONS_FILE, 'r') as f:
            content = f.read().strip()
            if content:
                return json.loads(content)
    return {}

def get_or_assign_netflix_account(order_sn, netflix_accounts):
    config = load_config()
    netflix_account_limit = int(config.get('NETFLIX_ACCOUNT_LIMIT', 5))  # Convert to int
    sessions = load_netflix_sessions()

    # Initialize 'orders' and 'accounts' keys if they don't exist
    if 'orders' not in sessions:
        sessions['orders'] = {}
    if 'accounts' not in sessions:
        sessions['accounts'] = {}

    # Check if the order is already assigned an account
    if order_sn in sessions['orders']:
        assigned_account = sessions['orders'][order_sn]['account']
        if assigned_account in netflix_accounts:
            return assigned_account
    
    # If not assigned or the assigned account is no longer valid, assign a new account
    available_accounts = [
        account for account in netflix_accounts
        if len(sessions['accounts'].get(account, [])) < netflix_account_limit
    ]
    
    if not available_accounts:
        return None  # No available accounts
    
    new_account = random.choice(available_accounts)
    
    # Update sessions data
    if new_account not in sessions['accounts']:
        sessions['accounts'][new_account] = []
    sessions['accounts'][new_account].append(order_sn)
    
    sessions['orders'][order_sn] = {
        'account': new_account,
        'last_redeem': None
    }
    
    save_netflix_sessions(sessions)
    return new_account

def update_last_redeem(order_sn, redeem_time):
    sessions = load_netflix_sessions()
    
    # Ensure the orders structure exists
    if 'orders' not in sessions:
        sessions['orders'] = {}
    
    # Update last_redeem in the orders structure
    if order_sn in sessions['orders']:
        sessions['orders'][order_sn]['last_redeem'] = redeem_time.isoformat()
        save_netflix_sessions(sessions)

def get_netflix_signin_code(order_sn):
    try:
        config = load_config()
        netflix_accounts = config.get('NETFLIX_ACCOUNTS', [])
        redeem_periods = config.get('NETFLIX_REDEEM_PERIODS', {})
        time_zone = config.get('TIME_ZONE', 'UTC')

        if not netflix_accounts:
            logger.error("No Netflix accounts configured")
            return {"error": "No Netflix accounts configured"}, 500

        # 检查是否为 manual invoice 订单
        try:
            with open('manual_invoice.json', 'r') as f:
                manual_invoices = json.load(f)
            
            # 通过 reference_id 查找匹配的发票
            matching_invoice = None
            for invoice_id, invoice_data in manual_invoices.items():
                if invoice_data.get('reference_id') == order_sn:
                    matching_invoice = invoice_data
                    break
                    
            is_manual_invoice = matching_invoice is not None
            
        except FileNotFoundError:
            logger.warning("manual_invoice.json not found")
            is_manual_invoice = False
        except json.JSONDecodeError:
            logger.error("Invalid JSON in manual_invoice.json")
            return {"error": "Internal server error"}, 500
            
        if is_manual_invoice:
            # 使用找到的匹配发票数据
            invoice_data = matching_invoice
            
            # 检查支付状态 - 检查 status 和 amount_paid
            if invoice_data.get('status') != 'paid' or invoice_data.get('amount_paid') != invoice_data.get('amount'):
                return {"error": "Cannot process unpaid invoice"}, 400
                
            # 获取 var_sku
            var_sku = invoice_data.get('var_sku')
            if not var_sku:
                return {"error": "VAR SKU not found in invoice"}, 400
                
            # 使用创建时间作为 ship_by_date
            ship_by_date = invoice_data.get('created_at')
            if not ship_by_date:
                return {"error": "Created time not found in invoice"}, 400
            
        else:
            # 原有的 Shopee 订单处理逻辑
            order_details_response, status_code = get_order_details(order_sn)
            if status_code != 200:
                return order_details_response, status_code

            # 检查订单状态
            buyer_cancel_reason = order_details_response['data'].get('buyer_cancel_reason')
            if buyer_cancel_reason != 0:
                return {"error": f"Cannot process request for cancelled order"}, 400
            
            refund_id = order_details_response['data'].get('refund_id')
            if refund_id != 0 and refund_id != None:
                return {"error": f"Cannot process request for return/refund order"}, 400

            var_skus = extract_var_sku(order_details_response)
            if not var_skus:
                return {"error": "Unable to determine VAR SKU for this order"}, 400

            var_sku = var_skus[0]
            ship_by_date = order_details_response['data'].get('ship_by_date')

        redeem_period = redeem_periods.get(var_sku)
        if not redeem_period:
            return {"error": f"Invalid VAR SKU: {var_sku}"}, 400

        if not ship_by_date:
            return {"error": "Unable to determine ship_by_date"}, 400

        # 使用配置的时区
        configured_tz = pytz.timezone(time_zone)
        # -timedelta(days=1) 偏差一天
        start_date = datetime.fromtimestamp(ship_by_date, tz=configured_tz) - timedelta(days=1)
        current_date = datetime.now(configured_tz)
        end_date = start_date + timedelta(days=redeem_period)
        
        if current_date > end_date:
            return {"error": "The redeem period for this order has expired"}, 400

        if netflix_session_manager.is_on_cooldown(order_sn):
            return {"error": "Please wait before requesting another sign-in code"}, 429

        netflix_account = get_or_assign_netflix_account(order_sn, netflix_accounts)
        if not netflix_account:
            return {"error": "No available Netflix accounts or account limit reached"}, 500

        sessions = load_netflix_sessions()
        current_time = time.time()
        
        if 'orders' in sessions and order_sn in sessions['orders']:
            order_data = sessions['orders'][order_sn]
            cooldown_until = order_data.get('cooldown_until')
            
            if cooldown_until and current_time < cooldown_until:
                remaining = int(cooldown_until - current_time)
                hours = remaining // 3600
                minutes = (remaining % 3600) // 60
                return {"error": f"Please wait {hours}h {minutes}m before requesting another sign-in code"}, 429

        def process_request(order_sn, timed_out=False):
            if timed_out:
                return {"error": "Request timed out"}, 408

            signin_code = fetch_netflix_signin_code(netflix_account)
            if not signin_code:
                return {"error": "Failed to fetch Netflix sign-in code"}, 500

            netflix_session_manager.update_session(order_sn, signin_code, netflix_account)
            netflix_session_manager.set_cooldown(order_sn)
            update_last_redeem(order_sn, current_date)

            # Send chat message after successful code retrieval
            chat_message = config.get('CHAT_MESSAGES', {}).get(
                "netflix_signin_code_success",
                "Successfully retrieved Netflix sign-in code. Email: {email}, Code: {code}"
            ).format(
                email=netflix_account,
                code=signin_code
            )
            
            chat_payload = {
                "order_sn": order_sn,
                "text": chat_message,
                "force_send_cancel_order_warning": False,
                "comply_cancel_order_warning": False
            }
            send_chat_message(chat_payload)

            ship_response, ship_status = ship_order(order_sn)
            if ship_status != 200:
                logger.warning(f"Failed to ship order {order_sn}: {ship_response}")

            return {
                "signin_code": signin_code,
                "netflix_account": netflix_account,
                "redeem_period": redeem_period,
                "order_shipped": ship_status == 200
            }, 200

        if netflix_session_manager.queue_request(order_sn, process_request):
            return {"message": "Request queued successfully", "netflix_account": netflix_account}, 202
        else:
            return {"error": "You got another request in queue, please wait for the previous request to complete"}, 500

    except Exception as e:
        logger.error(f"Error in get_netflix_signin_code: {str(e)}", exc_info=True)
        return {"error": "Internal server error"}, 500

def fetch_netflix_signin_code(netflix_account):
    config = load_config()
    email_config = config.get('NETFLIX_SIGNIN_EMAIL', {})
    email_address = email_config.get('email')
    app_password = email_config.get('app_password')
    max_retries = config.get('NETFLIX_MAX_RETRIES', 3)
    retry_delay = config.get('NETFLIX_RETRY_DELAY', 5)

    if not email_address or not app_password:
        logger.error("Netflix sign-in email configuration is missing")
        return None

    for attempt in range(max_retries):
        try:
            mail = imaplib.IMAP4_SSL('imap.gmail.com')
            mail.login(email_address, app_password)
            mail.select('inbox')

            _, search_data = mail.search(None, 'UNSEEN', '(OR SUBJECT "Netflix: Your sign-in code" SUBJECT "Netflix: Kod daftar masuk anda")')
            mail_ids = search_data[0].split()

            if not mail_ids:
                logger.warning(f"No new Netflix verification emails found (attempt {attempt + 1}/{max_retries})")
                time.sleep(retry_delay)
                continue

            for num in reversed(mail_ids):
                _, data = mail.fetch(num, '(RFC822)')
                _, bytes_data = data[0]

                email_message = email.message_from_bytes(bytes_data)

                to_header = email_message['To']
                if netflix_account not in to_header:
                    logger.warning(f"Received email for unexpected account: {to_header}")
                    continue

                for part in email_message.walk():
                    if part.get_content_type() == "text/html":
                        html_body = part.get_payload(decode=True).decode()
                        # Try English pattern first
                        match = re.search(NETFLIX_SIGNIN_CODE_PATTERN, html_body, re.IGNORECASE)
                        if not match:
                            # Try Malay pattern if English pattern doesn't match
                            match = re.search(NETFLIX_SIGNIN_CODE_PATTERN_MALAY, html_body, re.IGNORECASE)
                        if match:
                            signin_code = match.group(1)
                            logger.info(f"Netflix Sign In Code {signin_code} extracted from email")
                            return signin_code

            logger.warning(f"No valid Netflix verification code found in recent emails (attempt {attempt + 1}/{max_retries})")
            time.sleep(retry_delay)

        except Exception as e:
            logger.error(f"Error fetching Netflix sign-in code: {str(e)}")
            time.sleep(retry_delay)
        finally:
            try:
                mail.close()
                mail.logout()
            except:
                pass

    logger.error("Failed to fetch Netflix sign-in code after all retries")
    return None

def get_random_netflix_account():
    config = load_config()
    netflix_accounts = config.get('NETFLIX_ACCOUNTS', [])
    if not netflix_accounts:
        logger.error("No Netflix accounts configured")
        return None
    return random.choice(netflix_accounts)

def get_netflix_signin_code_records():
    return netflix_session_manager.get_signin_code_records()

def reset_netflix_signin_code_records():
    netflix_session_manager.reset_signin_code_records()
    return {"message": "Netflix sign-in code records have been reset"}, 200

def get_netflix_cooldowns():
    cooldowns = []
    current_time = time.time()
    for order_sn, session_data in netflix_session_manager.sessions.items():
        cooldown_until = session_data.get('cooldown_until')
        if cooldown_until and current_time < cooldown_until:
            remaining = int(cooldown_until - current_time)
            cooldowns.append({
                'order_sn': order_sn,
                'remaining_cooldown': remaining
            })
    return cooldowns

def reset_netflix_cooldown(order_sn):
    # Reset cooldown in session manager
    if order_sn in netflix_session_manager.sessions:
        netflix_session_manager.sessions[order_sn]['cooldown_until'] = None
        
        # Also reset last_redeem in netflix_sessions.json
        sessions = load_netflix_sessions()
        if 'orders' in sessions and order_sn in sessions['orders']:
            sessions['orders'][order_sn]['last_redeem'] = None
            save_netflix_sessions(sessions)
            
        logger.info(f"Netflix cooldown and last_redeem reset for order {order_sn}")
        return True
    else:
        logger.warning(f"Attempted to reset cooldown for non-existent Netflix session: {order_sn}")
        return False

def get_netflix_signin_code_status(order_sn):
    status = netflix_session_manager.get_request_status(order_sn)
    
    if status == 'in_progress':
        return {"status": "in_progress", "message": "Request is still being processed"}, 200
    elif status == 'completed':
        session_data = netflix_session_manager.sessions.get(order_sn, {})
        cooldown_until = netflix_session_manager.get_cooldown_until(order_sn)
        current_time = time.time()
        
        result = {
            "status": "completed",
            "result": {
                "signin_code": session_data.get('signin_code'),
                "netflix_account": session_data.get('netflix_account'),
                "redeem_period": session_data.get('redeem_period'),
                "order_shipped": session_data.get('order_shipped', False)
            }
        }
        
        if cooldown_until and current_time < cooldown_until:
            result["cooldown_time"] = int(cooldown_until - current_time)
        
        return result, 200
    else:
        return {"status": "not_found", "message": "No request found for this order"}, 200


def remove_expired_orders():
    """Remove expired Netflix orders and return details of removed orders"""
    try:
        with open('netflix_sessions.json', 'r') as f:
            data = json.load(f)
        
        removed_orders = []
        current_date = datetime.now()
        
        # Iterate through orders to find expired ones
        orders_to_remove = []
        for order_id, order_info in data['orders'].items():
            # Extract date from order_id (format: YYMMDD...)
            order_date_str = '20' + order_id[0:6]  # Convert YYMMDD to YYYYMMDD
            order_date = datetime.strptime(order_date_str, '%Y%m%d')
            
            # Calculate expiry date (30 days from order date)
            expiry_date = order_date + timedelta(days=30)
            
            # Check if order is expired
            if current_date > expiry_date:
                orders_to_remove.append(order_id)
                removed_orders.append({
                    'order_id': order_id,
                    'email': order_info['account'],
                    'order_date': order_date.strftime('%Y-%m-%d'),
                    'expiry_date': expiry_date.strftime('%Y-%m-%d')
                })
        
        # Remove expired orders from both orders and accounts
        if orders_to_remove:
            # Remove from orders
            for order_id in orders_to_remove:
                email = data['orders'][order_id]['account']
                del data['orders'][order_id]
                
                # Remove from accounts
                if email in data['accounts']:
                    data['accounts'][email] = [
                        oid for oid in data['accounts'][email] 
                        if oid not in orders_to_remove
                    ]
                    # Remove email key if no orders left
                    if not data['accounts'][email]:
                        del data['accounts'][email]
            
            # Save updated data
            with open('netflix_sessions.json', 'w') as f:
                json.dump(data, f, indent=2)
        
        return removed_orders
        
    except Exception as e:
        print(f"Error removing expired orders: {str(e)}")
        return []
