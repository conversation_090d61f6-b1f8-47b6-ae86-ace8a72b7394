from flask import Blueprint, jsonify, request
from services.email_service import get_steam_auth_code_for_user
from services.session_service import session_manager
from services.order_service import get_order_status, ship_order
from services.chat_service import send_chat_message
import config
import threading
import time
from utils.decorators import check_shopee_credentials
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
email_bp = Blueprint('email', __name__)

@email_bp.route('/get_steam_credentials', methods=['POST'])
@check_shopee_credentials
def api_get_steam_credentials():
    payload = request.json
    if not payload or 'username' not in payload or 'order_id' not in payload:
        return jsonify({"error": "Username and order_id are required in the payload"}), 400

    username = payload['username']
    order_id = payload['order_id']

    # 验证 order_id 是否为有效的订单号
    order_status_result, status_code = get_order_status(order_id)
    if status_code != 200:
        return jsonify({"error": "Invalid Order ID"}), 400

    status = order_status_result.get('status')
    if status not in ['Completed', 'Shipped', 'To Ship']:
        return jsonify({"error": f"Invalid order status: {status}"}), 400

    # 从 config 模块获取最新的 Steam 凭据
    if username in config.STEAM_CREDENTIALS:
        credentials = config.STEAM_CREDENTIALS[username]
        return jsonify({
            "username": username,
            "password": credentials['password']
        })
    else:
        return jsonify({"error": "Steam credentials not found for this username"}), 404

@email_bp.route('/get_steam_auth_code', methods=['POST'])
@check_shopee_credentials
def api_get_steam_auth_code():
    payload = request.json
    if not payload or 'username' not in payload or 'order_id' not in payload:
        logger.warning("Invalid payload received")
        return jsonify({"error": "Username and order_id are required in the payload"}), 400

    username = payload['username']
    order_id = payload['order_id']
    logger.info(f"Received request for auth code: username={username}, order_id={order_id}")

    # Validate order_id
    order_status_result, status_code = get_order_status(order_id)
    if status_code != 200:
        session_manager.record_auth_code_attempt(username, order_id, status='Failure', error_message='Invalid Order ID')
        return jsonify({"error": "Invalid Order ID"}), 400

    status = order_status_result.get('status')
    logger.info(f"Order status for {order_id}: {status}")

    # 修改：移除了在此处的 ship order 逻辑
    if status not in ['Completed', 'Shipped', 'To Ship']:
        logger.warning(f"Invalid order status: {status}")
        return jsonify({"error": f"Invalid order status: {status}"}), 400

    event = threading.Event()
    result = {}

    def process_request(username, order_id, timed_out=False):
        nonlocal result
        logger.info(f"Processing request for {username}:{order_id}")
        try:
            if timed_out:
                logger.warning(f"Request timed out for {username}:{order_id}")
                session_manager.record_auth_code_attempt(username, order_id, status='Failure', error_message='Request timed out')
                result = {"error": "Request timed out"}
            else:
                logger.info(f"Attempting to get auth code for {username}")
                auth_code = get_steam_auth_code_for_user(username)
                logger.info(f"Auth code retrieval result for {username}: {'Success' if auth_code else 'Failure'}")
                if auth_code:
                    logger.info(f"Auth code obtained for {username}")

                    if session_manager.set_auth_code(username, order_id, auth_code):
                        logger.info(f"Auth code set for {username}:{order_id}")
                        session_manager.set_cooldown(username, order_id)  # Cooldown after success
                        
                        # 新增：在成功获取 auth code 后执行 ship order
                        if status == 'To Ship' and config.AUTO_SHIP_ORDER:
                            logger.info(f"Attempting to ship order {order_id} for user {username}")
                            ship_result, ship_status_code = ship_order(order_id)
                            if ship_status_code == 200:
                                logger.info(f"Order {order_id} shipped successfully for user {username}")
                            else:
                                error_message = ship_result.get('error', 'Unknown error during shipping')
                                logger.error(f"Auto-ship failed for order {order_id}: {error_message}")
                        
                        # 新增：添加延迟
                        time.sleep(config.AUTH_CODE_DELAY)
                        
                        result = {"auth_code": auth_code, "order_id": order_id}
                        
                        # Send chat message if enabled
                        if config.SEND_CHAT_ON_AUTH_SUCCESS:
                            next_redeem_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time() + config.SESSION_COOLDOWN_TIME))
                            chat_message = config.CHAT_MESSAGES.get("steam_auth_code_success", "Successfully redeemed auth code. Next redeem time: {next_redeem_time}.").format(
                                auth_code=auth_code,
                                next_redeem_time=next_redeem_time
                            )
                            chat_payload = {
                                "order_sn": order_id,
                                "text": chat_message,
                                "force_send_cancel_order_warning": False,
                                "comply_cancel_order_warning": False
                            }
                            send_chat_message(chat_payload)
                    else:
                        logger.warning(f"Failed to set auth code for {username}:{order_id}")
                        session_manager.record_auth_code_attempt(username, order_id, status='Failure', error_message='Failed to set auth code')
                        result = {"error": "Failed to set auth code"}
                else:
                    logger.warning(f"Auth code not found for {username}")
                    session_manager.record_auth_code_attempt(username, order_id, status='Failure', error_message='Steam authorization code not found or invalid username')
                    result = {"error": "Steam authorization code not found or invalid username"}
        except Exception as e:
            logger.error(f"Exception in process_request: {str(e)}", exc_info=True)
            result = {"error": "Internal server error"}
        finally:
            event.set()
            logger.info(f"Request processing completed for {username}:{order_id}")

    # Queue the request
    logger.info(f"Attempting to queue request for {username}:{order_id}")
    if not session_manager.queue_request(username, order_id, process_request):
        # Determine if the failure was due to cooldown or duplicate request
        cooldown_until = session_manager.get_cooldown_until(username, order_id)
        if cooldown_until and time.time() < cooldown_until:
            remaining = int(cooldown_until - time.time())
            session_manager.record_auth_code_attempt(username, order_id, status='Failure', error_message='Cooldown active')
            return jsonify({"error": f"A request for this order is already in progress or in cooldown. Please try again in {remaining} seconds."}), 429
        else:
            session_manager.record_auth_code_attempt(username, order_id, status='Failure', error_message='Duplicate request')
            return jsonify({"error": "A request for this order is already in progress"}), 429

    logger.info(f"Request for {username}:{order_id} queued successfully")
    # Wait for processing to complete, timeout after REQUEST_TIMEOUT seconds
    if not event.wait(timeout=config.REQUEST_TIMEOUT):
        logger.warning(f"Request timed out for {username}:{order_id}")
        session_manager.record_auth_code_attempt(username, order_id, status='Failure', error_message='Request timed out')
        return jsonify({"error": "Request timed out"}), 408

    logger.info(f"Request processing completed for {username}:{order_id}")
    if "error" in result:
        return jsonify(result), 400
    return jsonify(result)

def get_user_email_credentials(username):
    try:
        return get_email_config(username)
    except ValueError as e:
        logger.error(f"Error getting email credentials: {str(e)}")
        return None, None

def get_email_config(username):
    if username in config.EMAIL_CONFIGS:
        email_config = config.EMAIL_CONFIGS[username]
        if "email" in email_config and "app_password" in email_config:
            return email_config["email"], email_config["app_password"]
        else:
            raise ValueError(f"Email or app password not set for user: {username}")
    else:
        raise ValueError(f"No email configuration found for user: {username}")