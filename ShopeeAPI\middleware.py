"""
Middleware for FastAPI application.
"""
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for handling authentication.
    """
    async def dispatch(self, request: Request, call_next):
        """
        Process the request and add authentication if needed.
        """
        # Currently a placeholder - no authentication is performed here
        # This is just to satisfy the import in api.py
        response = await call_next(request)
        return response
