"""
Fixed lifespan function for ShopeeAPI.
"""
import os
import time
import threading
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI

# Define watchdog availability
WATCHDOG_AVAILABLE = False
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    print("Watchdog package not installed. Config file hot reload will be limited.")

# File watcher variables
observer = None
event_handler = None

@asynccontextmanager
async def fixed_lifespan(app: FastAPI):
    """
    Fixed lifespan context manager for FastAPI app.
    This version properly handles the WATCHDOG_AVAILABLE variable.
    """
    # Import necessary functions and variables
    from ShopeeAPI.api import reload_api_from_config, get_api, _config_path, ConfigFileHandler

    # Declare globals
    global observer, event_handler

    # Print debug information
    print(f"Starting lifespan with WATCHDOG_AVAILABLE={WATCHDOG_AVAILABLE}")
    print(f"Config path: {_config_path}")

    # Verify that the config path is correct (should be in the ShopeeAPI directory)
    expected_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
    if _config_path != expected_path:
        print(f"WARNING: Config path {_config_path} does not match expected path {expected_path}")
        print(f"Updating config path to {expected_path}")
        # Update the global variable in the api module
        import sys
        if 'ShopeeAPI.api' in sys.modules:
            sys.modules['ShopeeAPI.api']._config_path = expected_path
            print(f"Updated config path in api module to {expected_path}")

    # Set initial last modified time
    if os.path.exists(_config_path):
        last_modified = os.path.getmtime(_config_path)
        print(f"Config file last modified: {time.ctime(last_modified)}")
    else:
        print(f"Warning: Config file {_config_path} does not exist")

    # Force reload config at startup
    reload_api_from_config(force=True)

    # Determine if we should use polling
    use_polling = not WATCHDOG_AVAILABLE
    print(f"Using polling: {use_polling}")

    # Start file watcher if watchdog is available
    if WATCHDOG_AVAILABLE:
        try:
            print("Initializing Observer...")
            observer = Observer()
            event_handler = ConfigFileHandler()
            observer.schedule(event_handler, os.path.dirname(_config_path), recursive=False)

            print("Setting daemon status...")
            observer.setDaemon(True)

            print("Starting observer...")
            observer.start()

            print(f"Started config file watcher for {_config_path}")
        except Exception as e:
            print(f"Error starting file watcher: {e}")
            # Fall back to polling mechanism
            use_polling = True

    # If watchdog is not available or failed to start, set up a simple polling mechanism
    if use_polling:
        print("Using polling mechanism for config file changes")

        # Start a background thread to periodically check for config changes
        def poll_config_changes():
            while True:
                try:
                    reload_api_from_config()
                    time.sleep(5)  # Check every 5 seconds
                except Exception as e:
                    print(f"Error polling config changes: {e}")
                    time.sleep(10)  # Wait longer on error

        polling_thread = threading.Thread(target=poll_config_changes, daemon=True)
        polling_thread.start()

    # Initialize WebSocket connection to Shopee
    websocket_task = None
    try:
        api = get_api()
        if api.config.websocket["enabled"]:
            try:
                # Start the WebSocket connection maintenance task
                websocket_task = asyncio.create_task(api.websocket_service.maintain_connection())
                print("Started WebSocket connection maintenance task")
            except Exception as e:
                print(f"Error starting WebSocket connection: {e}")
    except Exception as e:
        print(f"Error initializing API: {e}")

    # Yield control back to FastAPI
    yield

    # Shutdown: Stop the config file watcher
    if WATCHDOG_AVAILABLE and observer:
        try:
            print("Stopping observer...")
            observer.stop()
            observer.join()
            print("Stopped config file watcher")
        except Exception as e:
            print(f"Error stopping file watcher: {e}")

    # Close WebSocket connection and cancel task
    try:
        api = get_api()
        if hasattr(api, 'websocket_service'):
            # Use our new close method to properly clean up resources
            await api.websocket_service.close()
            print("Closed WebSocket connection and cleaned up resources")

        # Also cancel the maintenance task
        if websocket_task:
            websocket_task.cancel()
            print("Stopped WebSocket connection maintenance task")
    except Exception as e:
        print(f"Error closing WebSocket connection: {e}")
