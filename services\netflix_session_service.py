import time
from threading import RLock
from datetime import datetime, timedelta
import logging
from config import load_config
from queue import Queue
import threading
import os
import json

logger = logging.getLogger(__name__)

NETFLIX_SESSIONS_FILE = 'netflix_sessions.json'

def load_netflix_sessions():
    if os.path.exists(NETFLIX_SESSIONS_FILE):
        with open(NETFLIX_SESSIONS_FILE, 'r') as f:
            content = f.read().strip()
            if content:
                return json.loads(content)
    return {'orders': {}, 'accounts': {}}

def save_netflix_sessions(sessions):
    with open(NETFLIX_SESSIONS_FILE, 'w') as f:
        json.dump(sessions, f, indent=2)

class NetflixSessionManager:
    def __init__(self):
        logger.info("Initializing NetflixSessionManager")
        self.sessions = {}
        self.lock = RLock()
        self.used_signin_codes = set()
        self.request_queue = Queue()
        self.active_requests = set()
        self.processing_thread = threading.Thread(target=self._process_queue, daemon=True)
        logger.info("Starting processing thread")
        self.processing_thread.start()
        self.config = load_config()
        self.signin_code_records = []
        self.last_redeems = {}
        self.netflix_account_index = 0
        self.load_sessions_from_file()
        logger.info("NetflixSessionManager initialization completed")

    def load_sessions_from_file(self):
        file_path = 'netflix_sessions.json'
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                data = json.load(f)
                self.sessions = data.get('orders', {})
        else:
            self.sessions = {}

    def get_all_sessions(self):
        return self.sessions

    def reset_cooldown(self, order_sn):
        if order_sn in self.sessions:
            self.sessions[order_sn]['cooldown_until'] = None
            self.sessions[order_sn]['last_redeem'] = None
            self.save_sessions_to_file()
            return True
        return False

    def save_sessions_to_file(self):
        with open('netflix_sessions.json', 'w') as f:
            json.dump({'orders': self.sessions}, f, indent=2)

    def update_session(self, order_sn, signin_code, netflix_account):
        with self.lock:
            if order_sn in self.sessions:
                self.sessions[order_sn]['signin_code'] = signin_code
                self.sessions[order_sn]['netflix_account'] = netflix_account
                self.record_signin_code_attempt(order_sn, signin_code, status='Success')
                logger.info(f"Netflix session updated for {order_sn} with signin code {signin_code} and account {netflix_account}")
            else:
                logger.warning(f"Attempted to update non-existent Netflix session for {order_sn}")
                self.record_signin_code_attempt(order_sn, signin_code, status='Failure', error_message='Invalid session')

    def queue_request(self, order_sn, callback):
        logger.info(f"Attempting to queue Netflix request for {order_sn}")
        with self.lock:
            cooldown_until = self.get_cooldown_until(order_sn)
            current_time = time.time()
            if cooldown_until and current_time < cooldown_until:
                remaining = int(cooldown_until - current_time)
                logger.warning(f"Cannot queue Netflix request for {order_sn}; cooldown active for another {remaining} seconds")
                return False

            if order_sn in self.active_requests:
                logger.warning(f"Netflix request for {order_sn} already in progress")
                return False

            self.active_requests.add(order_sn)
            self.request_queue.put((order_sn, callback))
            logger.info(f"Netflix request for {order_sn} queued successfully")
            return True

    def _process_queue(self):
        logger.info("Starting to process Netflix queue")
        while True:
            try:
                logger.info("Waiting for new Netflix request in queue")
                order_sn, callback = self.request_queue.get()
                logger.info(f"Got new Netflix request for {order_sn}")
                try:
                    self._process_request(order_sn, callback)
                except Exception as e:
                    logger.error(f"Error processing Netflix request for {order_sn}: {str(e)}", exc_info=True)
                finally:
                    with self.lock:
                        self.active_requests.remove(order_sn)
                    self.request_queue.task_done()
                logger.info(f"Finished processing Netflix request for {order_sn}")
            except Exception as e:
                logger.error(f"Unexpected error in Netflix _process_queue: {str(e)}", exc_info=True)
                time.sleep(1)

    def get_last_redeem(self, order_sn):
        with self.lock:
            return self.last_redeems.get(order_sn)

    def set_last_redeem(self, order_sn, timestamp):
        with self.lock:
            self.last_redeems[order_sn] = timestamp

    def _process_request(self, order_sn, callback):
        logger.info(f"Starting to process Netflix request for {order_sn}")
        start_time = time.time()
        while time.time() - start_time < self.config.get('NETFLIX_REQUEST_TIMEOUT', 30):
            with self.lock:
                cooldown_until = self.get_cooldown_until(order_sn)
                if cooldown_until and time.time() < cooldown_until:
                    logger.info(f"Netflix session {order_sn} still in cooldown")
                    callback(order_sn, timed_out=False)
                    return
            
            logger.info(f"Checking if can request Netflix signin code for {order_sn}")
            if self.can_request_signin_code(order_sn):
                logger.info(f"Requesting Netflix signin code for {order_sn}")
                callback(order_sn)
                return
            logger.info(f"Cannot request Netflix signin code for {order_sn} yet, waiting...")
            time.sleep(1)
        logger.warning(f"Netflix request for {order_sn} timed out")
        callback(order_sn, timed_out=True)

    def get_request_status(self, order_sn):
        with self.lock:
            if order_sn in self.active_requests:
                return 'in_progress'
            elif order_sn in self.sessions and 'signin_code' in self.sessions[order_sn]:
                return 'completed'
            else:
                return 'not_found'
            
    def can_request_signin_code(self, order_sn):
        with self.lock:
            current_time = time.time()
            logger.info(f"Checking if can request Netflix signin code for {order_sn}")

            cooldown_until = self.get_cooldown_until(order_sn)
            if cooldown_until and current_time < cooldown_until:
                logger.info(f"Netflix order {order_sn} in cooldown, cannot request signin code")
                self.record_signin_code_attempt(order_sn, status='Failure', error_message='Order in cooldown')
                return False

            self.sessions[order_sn] = {'cooldown_until': None}
            
            self.record_signin_code_attempt(order_sn, status='Attempt')
            logger.info(f"Can request Netflix signin code for {order_sn}")
            return True

    def set_cooldown(self, order_sn):
        with self.lock:
            current_time = time.time()
            cooldown_time = current_time + self.config.get('NETFLIX_SESSION_COOLDOWN_TIME', 600)
            
            # Update in-memory cooldown
            self.sessions[order_sn]['cooldown_until'] = cooldown_time
            
            # Update file-based cooldown
            sessions = load_netflix_sessions()
            if 'orders' not in sessions:
                sessions['orders'] = {}
            if order_sn not in sessions['orders']:
                sessions['orders'][order_sn] = {}
            
            sessions['orders'][order_sn]['cooldown_until'] = cooldown_time
            save_netflix_sessions(sessions)
            
            logger.info(f"Netflix cooldown set for {order_sn} until {cooldown_time}")

    def get_cooldown_until(self, order_sn):
        with self.lock:
            # Check file-based cooldown first
            sessions = load_netflix_sessions()
            if ('orders' in sessions and 
                order_sn in sessions['orders'] and 
                'cooldown_until' in sessions['orders'][order_sn]):
                return sessions['orders'][order_sn]['cooldown_until']
            
            # Fallback to in-memory cooldown
            return self.sessions.get(order_sn, {}).get('cooldown_until')

    def record_signin_code_attempt(self, order_sn, signin_code=None, status='Attempt', error_message=None):
        record = {
            'order_sn': order_sn,
            'signin_code': signin_code,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': status,
            'error_message': error_message
        }
        self.signin_code_records.append(record)
        logger.info(f"Recorded Netflix signin code attempt: {record}")
        return record

    def get_signin_code_records(self):
        with self.lock:
            logger.info("Retrieving Netflix signin code records")
            return self.signin_code_records

    def reset_signin_code_records(self):
        with self.lock:
            self.signin_code_records = []
            logger.info("Netflix signin code records have been reset")

    def get_next_netflix_account_index(self):
        with self.lock:
            index = self.netflix_account_index
            self.netflix_account_index += 1
            return index

    def is_on_cooldown(self, order_sn):
        with self.lock:
            cooldown_until = self.get_cooldown_until(order_sn)
            if cooldown_until and time.time() < cooldown_until:
                logger.info(f"Netflix order {order_sn} is on cooldown")
                return True
            logger.info(f"Netflix order {order_sn} is not on cooldown")
            return False

netflix_session_manager = NetflixSessionManager()