#!/bin/bash
# Docker Hub deployment script for ShopeeAPI
# This script builds and pushes the ShopeeAPI Docker image to a private Docker Hub repository

# Configuration
DOCKER_HUB_USERNAME="<EMAIL>"  # Change to your Docker Hub username
DOCKER_HUB_REPO="shopeeapi"                    # Change to your repository name
IMAGE_NAME="${DOCKER_HUB_USERNAME}/${DOCKER_HUB_REPO}"

# Parse command line arguments
BUILD=false
PUSH=false
ALL=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --build)
      BUILD=true
      shift
      ;;
    --push)
      PUSH=true
      shift
      ;;
    --all)
      ALL=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--build] [--push] [--all]"
      exit 1
      ;;
  esac
done

# If no arguments are provided, show help
if [[ "$BUILD" == "false" && "$PUSH" == "false" && "$ALL" == "false" ]]; then
  echo "Usage: $0 [--build] [--push] [--all]"
  echo "  --build: Build the Docker image"
  echo "  --push: Push the Docker image to Docker Hub"
  echo "  --all: Build and push the Docker image"
  exit 0
fi

# Set version based on date and time
VERSION=$(date +"%Y.%m.%d.%H%M")
echo "Using version: $VERSION"

# Build the Docker image
if [[ "$BUILD" == "true" || "$ALL" == "true" ]]; then
  echo "Building Docker image..."
  docker build -t $IMAGE_NAME:$VERSION -t $IMAGE_NAME:latest -f ShopeeAPI/Dockerfile .
  echo "Docker image built: $IMAGE_NAME:$VERSION"
fi

# Push the Docker image to Docker Hub
if [[ "$PUSH" == "true" || "$ALL" == "true" ]]; then
  echo "Logging in to Docker Hub..."
  docker login
  
  echo "Pushing Docker image to Docker Hub..."
  docker push $IMAGE_NAME:$VERSION
  docker push $IMAGE_NAME:latest
  
  echo "Docker image pushed to Docker Hub: $IMAGE_NAME:$VERSION"
fi

echo "Process completed!"
