from threading import Lock
import json
from typing import Union, Dict, Any, Optional

class CredentialManager:
    _instance = None
    _lock = Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance.initialize()
            return cls._instance

    def initialize(self):
        self.authorization_code = ''
        self._cookie_str = ''
        self._cookie_json = None
        self._subscribers = []

    def set_cookie(self, cookie: Union[str, Dict[str, Any], list, None]) -> None:
        """
        Set cookie value, handling both string and JSON formats.
        """
        if cookie is None:
            self._cookie_str = ''
            self._cookie_json = None
        elif isinstance(cookie, (dict, list)):
            self._cookie_json = cookie
            # Convert JSON to string format for backward compatibility
            self._cookie_str = self._json_to_cookie_string(cookie)
        elif isinstance(cookie, str):
            self._cookie_str = cookie
            # Try to parse as JSON if it looks like JSON
            if cookie.strip() and (
                (cookie.strip().startswith('{') and cookie.strip().endswith('}')) or
                (cookie.strip().startswith('[') and cookie.strip().endswith(']'))
            ):
                try:
                    self._cookie_json = json.loads(cookie)
                except json.JSONDecodeError:
                    self._cookie_json = None
            else:
                self._cookie_json = None
        else:
            # Handle other types by converting to string
            self._cookie_str = str(cookie)
            self._cookie_json = None

    def _json_to_cookie_string(self, cookie_json: Union[Dict[str, Any], list]) -> str:
        """
        Convert JSON cookie format to string format.
        """
        if not cookie_json:
            return ''

        # Handle array of cookie objects with name/value properties
        if isinstance(cookie_json, list):
            return '; '.join([f"{c.get('name', '')}={c.get('value', '')}"
                            for c in cookie_json if 'name' in c and 'value' in c])

        # Handle dictionary with nested cookie objects
        elif isinstance(cookie_json, dict):
            cookie_parts = []
            for name, value in cookie_json.items():
                if isinstance(value, dict) and 'value' in value:
                    # Handle nested structure like {"SPC_STK": {"value": "test", ...}}
                    cookie_parts.append(f"{name}={value['value']}")
                else:
                    # Handle simple structure like {"SPC_STK": "test"}
                    cookie_parts.append(f"{name}={value}")
            return '; '.join(cookie_parts)

        # This should never happen since we've handled all possible types
        return ''

    @property
    def cookie(self) -> str:
        """
        Get cookie string for backward compatibility.
        """
        return self._cookie_str

    @cookie.setter
    def cookie(self, value: Union[str, Dict[str, Any], list, None]) -> None:
        """
        Set cookie value, handling both string and JSON formats.
        """
        self.set_cookie(value)

    def update_credentials(self, authorization_code: str, cookie: Union[str, Dict[str, Any], list, None]):
        with self._lock:
            self.authorization_code = authorization_code
            self.set_cookie(cookie)
            self._notify_subscribers()

    def get_credentials(self):
        """
        Get current credentials.

        Returns:
            Dict containing authorization_code, cookie string, and cookie_json if available
        """
        creds = {
            'authorization_code': self.authorization_code,
            'cookie': self.cookie
        }

        # Include JSON cookies if available
        if self._cookie_json:
            creds['cookie_json'] = self._cookie_json

        return creds

    def subscribe(self, callback):
        if callback not in self._subscribers:
            self._subscribers.append(callback)

    def _notify_subscribers(self):
        for subscriber in self._subscribers:
            try:
                subscriber(self.get_credentials())
            except Exception as e:
                print(f"Error notifying subscriber: {e}")

# 创建全局实例
credential_manager = CredentialManager()