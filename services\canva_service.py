import json
import datetime
import secrets
import hashlib
from urllib.parse import urlencode
from services.order_service import get_order_status, ship_order, get_order_details
from utils.var_sku_extractor import extract_var_sku

class CanvaService:
    def __init__(self):
        self.load_config()
        self.load_orders()

    def load_config(self):
        with open('canva_config.json', 'r') as f:
            self.config = json.load(f)

    def load_orders(self):
        try:
            with open('canva_orders.json', 'r') as f:
                self.orders = json.load(f)
        except FileNotFoundError:
            self.orders = {}
            self.save_orders()

    def save_orders(self):
        with open('canva_orders.json', 'w') as f:
            json.dump(self.orders, f, indent=4)

    def create_invitation_url(self, order_sn):
        """为订单创建一个一次性的邀请URL"""
        # 检查订单状态
        status_response, status_code = get_order_status(order_sn)
        if status_code != 200:
            return None, "Order not found"

        status = status_response.get('status')
        
        # 如果订单状态是 To Ship，先执行发货
        if status == 'To Ship':
            ship_response, ship_code = ship_order(order_sn)
            if ship_code != 200:
                return None, "Failed to ship order"
            status = 'Shipped'

        # 只有 Shipped 或 Completed 状态的订单可以获取邀请链接
        if status not in ['Shipped', 'Completed']:
            return None, f"Invalid order status: {status}"

        # 检查是否已经在 canva_orders.json 中
        if order_sn in self.orders:
            # 如果已经兑换过，直接返回错误
            if self.orders[order_sn].get('redeemed', False):
                return None, "This order has already been redeemed and cannot be used again"
        else:
            # 如果是新订单，添加到 canva_orders.json
            self.orders[order_sn] = {
                'order_date': datetime.datetime.now().isoformat(),
                'expiry_date': (datetime.datetime.now() + datetime.timedelta(days=30)).isoformat(),
                'redeemed': False,
                'redeemed_at': None
            }

        # 生成新的redemption token
        token = self.generate_redemption_token(order_sn)
        self.orders[order_sn]['redemption_token'] = token
        self.save_orders()

        # 构建一次性邀请URL
        params = {
            'token': token,
            'order': order_sn
        }
        invitation_url = f"/canva_redeem_invitation?{urlencode(params)}"
        return invitation_url, None

    def generate_redemption_token(self, order_sn):
        """生成一个唯一的redemption token"""
        timestamp = datetime.datetime.now().timestamp()
        random_string = secrets.token_hex(16)
        data = f"{order_sn}:{timestamp}:{random_string}"
        return hashlib.sha256(data.encode()).hexdigest()[:32]

    def redeem_invitation(self, token, order_sn):
        """兑换邀请链接"""
        # 重新加载订单数据，确保数据最新
        self.load_orders()
        
        # 检查订单是否在 canva_orders.json 中
        if order_sn not in self.orders:
            return None, "Order not found"

        order = self.orders[order_sn]

        # 添加锁定机制，防止并发问题
        try:
            with open('canva_orders.json', 'r') as f:
                current_orders = json.load(f)
                
            # 再次验证订单状态，确保没有被其他请求修改
            if current_orders[order_sn].get('redeemed', False):
                return None, "Order already redeemed"
                
            if current_orders[order_sn].get('redemption_token') != token:
                return None, "Invalid token"
                
            # 更新当前内存中的订单数据
            self.orders = current_orders
        except Exception as e:
            return None, f"Error verifying order status: {str(e)}"

        # 检查订单状态
        status_response, status_code = get_order_status(order_sn)
        if status_code != 200:
            return None, "Failed to verify order status"

        status = status_response.get('status')
        if status not in ['Shipped', 'Completed']:
            return None, f"Invalid order status: {status}"

        # 检查是否为 manual invoice 订单
        try:
            with open('manual_invoice.json', 'r') as f:
                manual_invoices = json.load(f)
            
            # 通过 reference_id 查找匹配的发票
            matching_invoice = None
            for invoice_id, invoice_data in manual_invoices.items():
                if invoice_data.get('reference_id') == order_sn:
                    matching_invoice = invoice_data
                    break
                
            is_manual_invoice = matching_invoice is not None
            
        except FileNotFoundError:
            is_manual_invoice = False
        except json.JSONDecodeError:
            return None, "Internal server error"
        
        if is_manual_invoice:
            var_sku = matching_invoice.get('var_sku')
            if not var_sku:
                return None, "VAR SKU not found in invoice"
        else:
            order_details_response, status_code = get_order_details(order_sn)
            if status_code != 200:
                return None, "Failed to get order details"

            var_skus = extract_var_sku(order_details_response)
            if not var_skus:
                return None, "Unable to determine VAR SKU for this order"
            var_sku = var_skus[0]

        # 重新加载配置，确保使用最新的配置数据
        self.load_config()

        if var_sku not in self.config['SKU_VALIDITY']:
            return None, f"Invalid SKU: {var_sku}"

        account_type = self.config['SKU_VALIDITY'][var_sku]['type']
        if account_type not in self.config['types']:
            return None, f"Invalid account type: {account_type}"

        invitation_links = self.config['types'][account_type]['invitation_links']
        if not invitation_links:
            return None, f"No available invitation links for type {account_type}"

        canva_invitation_link = secrets.choice(invitation_links)
        
        try:
            # 标记为已兑换
            self.orders[order_sn]['redeemed'] = True
            self.orders[order_sn]['redeemed_at'] = datetime.datetime.now().isoformat()
            self.orders[order_sn]['redemption_token'] = None  # Clear the used token
            self.save_orders()
            
            # 再次验证保存是否成功
            self.load_orders()
            if not self.orders[order_sn].get('redeemed', False):
                return None, "Failed to mark order as redeemed"
            
            return canva_invitation_link, None
        except Exception as e:
            return None, f"Error saving order status: {str(e)}"

    def reset_order(self, order_sn):
        """重置订单的兑换状态"""
        if order_sn not in self.orders:
            return False, "Order not found in Canva orders"

        # Reset all relevant fields
        self.orders[order_sn].update({
            'redeemed': False,
            'redeemed_at': None,
            'redemption_token': None
        })
        
        self.save_orders()  # Make sure to save changes
        return True, None

    def extend_validity(self, order_sn, days):
        """延长订单的有效期"""
        if order_sn not in self.orders:
            return False, "Order not found in Canva orders"

        try:
            order = self.orders[order_sn]
            current_expiry = datetime.datetime.fromisoformat(order['expiry_date'].replace('Z', '+00:00'))
            new_expiry = current_expiry + datetime.timedelta(days=days)
            order['expiry_date'] = new_expiry.isoformat()
            self.save_orders()
            return True, None
        except Exception as e:
            return False, f"Error extending validity: {str(e)}"

    def check_expired_orders(self):
        """Check for expired orders and mark them as redeemed"""
        current_time = datetime.datetime.now()
        
        for order_sn, order_data in self.orders.items():
            try:
                # Parse ISO format date string, handling timezone info if present
                expiry_date = datetime.datetime.fromisoformat(order_data['expiry_date'].replace('Z', '+00:00'))
                
                # Check if order is expired and not yet redeemed
                if current_time > expiry_date and not order_data.get('redeemed', False):
                    # Mark as redeemed and save the redemption time
                    order_data['redeemed'] = True
                    order_data['redeemed_at'] = current_time.isoformat()
                    order_data['redemption_token'] = None
                    print(f"Order {order_sn} marked as redeemed due to expiration")
            except Exception as e:
                print(f"Error processing order {order_sn}: {str(e)}")
        
        # Save any changes made
        self.save_orders()
