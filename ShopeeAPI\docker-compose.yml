version: '3'

services:
  shopee-api:
    build:
      context: ..
      dockerfile: ShopeeAPI/Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./config.json:/app/ShopeeAPI/config.json
      - ./logs:/app/logs
    restart: unless-stopped
    environment:
      - PORT=8000
      - ENVIRONMENT=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # Add init: true to properly handle signals and zombie processes
    init: true