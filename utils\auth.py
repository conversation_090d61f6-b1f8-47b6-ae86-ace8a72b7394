from functools import wraps
from flask import request, jsonify
from config import API_KEY

def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if auth_header:
            try:
                auth_type, token = auth_header.split()
                if auth_type.lower() == 'bearer' and token == API_KEY:
                    return f(*args, **kwargs)
            except ValueError:
                pass
        return jsonify({"error": "Invalid or missing API key"}), 401
    return decorated_function