#!/bin/bash
# Docker Hub deployment script for ShopeeAPI on Linux servers
# This script pulls the ShopeeAPI Docker image from Docker Hub and deploys it

# Configuration
DOCKER_HUB_USERNAME="your-dockerhub-username"  # Change to your Docker Hub username
DOCKER_HUB_REPO="shopeeapi"                    # Change to your repository name
IMAGE_NAME="${DOCKER_HUB_USERNAME}/${DOCKER_HUB_REPO}"
CONTAINER_NAME="shopeeapi"
CONFIG_PATH="/path/to/config.json"             # Change to your config.json path
LOGS_PATH="/path/to/logs"                      # Change to your logs directory path

# Parse command line arguments
PULL=false
DEPLOY=false
RESTART=false
ALL=false
VERSION="latest"  # Default to latest version

while [[ $# -gt 0 ]]; do
  case $1 in
    --pull)
      PULL=true
      shift
      ;;
    --deploy)
      DEPLOY=true
      shift
      ;;
    --restart)
      RESTART=true
      shift
      ;;
    --all)
      ALL=true
      shift
      ;;
    --version)
      VERSION="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--pull] [--deploy] [--restart] [--all] [--version VERSION]"
      exit 1
      ;;
  esac
done

# If no arguments are provided, show help
if [[ "$PULL" == "false" && "$DEPLOY" == "false" && "$RESTART" == "false" && "$ALL" == "false" ]]; then
  echo "Usage: $0 [--pull] [--deploy] [--restart] [--all] [--version VERSION]"
  echo "  --pull: Pull the Docker image from Docker Hub"
  echo "  --deploy: Deploy the Docker image"
  echo "  --restart: Restart the container"
  echo "  --all: Pull, deploy, and restart"
  echo "  --version VERSION: Specify a version (default: latest)"
  exit 0
fi

# Pull the Docker image from Docker Hub
if [[ "$PULL" == "true" || "$ALL" == "true" ]]; then
  echo "Logging in to Docker Hub..."
  docker login
  
  echo "Pulling Docker image from Docker Hub: $IMAGE_NAME:$VERSION"
  docker pull $IMAGE_NAME:$VERSION
fi

# Deploy the Docker image
if [[ "$DEPLOY" == "true" || "$ALL" == "true" ]]; then
  echo "Stopping existing container if running..."
  docker stop $CONTAINER_NAME 2>/dev/null || true
  docker rm $CONTAINER_NAME 2>/dev/null || true
  
  echo "Creating directories if they don't exist..."
  mkdir -p $(dirname $CONFIG_PATH)
  mkdir -p $LOGS_PATH
  
  echo "Deploying Docker container..."
  docker run -d \
    --name $CONTAINER_NAME \
    -p 8000:8000 \
    -v $CONFIG_PATH:/app/config.json \
    -v $LOGS_PATH:/app/logs \
    --restart unless-stopped \
    -e PORT=8000 \
    -e ENVIRONMENT=production \
    $IMAGE_NAME:$VERSION
  
  echo "Container deployed: $CONTAINER_NAME"
fi

# Restart the container
if [[ "$RESTART" == "true" || "$ALL" == "true" ]]; then
  echo "Restarting container..."
  docker restart $CONTAINER_NAME
  echo "Container restarted: $CONTAINER_NAME"
fi

echo "Process completed!"
