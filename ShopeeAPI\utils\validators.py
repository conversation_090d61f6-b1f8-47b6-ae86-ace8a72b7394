"""
Validation utilities for the Shopee API.
"""
import re
from typing import Optional, Dict, Any, List


def validate_order_sn(order_sn: str) -> bool:
    """
    Validate an order number format.

    Args:
        order_sn: Order number to validate

    Returns:
        True if valid, False otherwise
    """
    # Order numbers are typically numeric strings
    return bool(re.match(r'^\d+$', order_sn))


def validate_credentials(authorization_code: str, cookie: str) -> Dict[str, List[str]]:
    """
    Validate API credentials.

    Args:
        authorization_code: Authorization code to validate
        cookie: Cookie string to validate

    Returns:
        Dictionary with validation errors, empty if valid
    """
    errors = {"authorization_code": [], "cookie": []}

    # Validate authorization code
    if not authorization_code:
        errors["authorization_code"].append("Authorization code is required")
    elif not authorization_code.startswith("Bearer "):
        errors["authorization_code"].append("Authorization code must start with 'Bearer '")

    # Validate cookie
    if not cookie:
        errors["cookie"].append("Cookie is required")
    else:
        # Check for essential cookies
        essential_cookies = ["SPC_CDS", "SPC_CDS_VER", "CTOKEN"]
        for cookie_name in essential_cookies:
            if f"{cookie_name}=" not in cookie:
                errors["cookie"].append(f"Missing essential cookie: {cookie_name}")

    # Remove empty error lists
    return {k: v for k, v in errors.items() if v}


def validate_config(config: Dict[str, Any]) -> Dict[str, List[str]]:
    """
    Validate configuration data.

    Args:
        config: Configuration dictionary to validate

    Returns:
        Dictionary with validation errors, empty if valid
    """
    errors = {}

    # Validate shop_id
    if "SHOP_ID" in config:
        shop_id = config["SHOP_ID"]
        if not isinstance(shop_id, int) or shop_id <= 0:
            errors["SHOP_ID"] = ["Shop ID must be a positive integer"]

    # Validate region_id
    if "REGION_ID" in config:
        region_id = config["REGION_ID"]
        if not isinstance(region_id, str) or not region_id:
            errors["REGION_ID"] = ["Region ID must be a non-empty string"]

    # Validate URLs
    if "URLS" in config:
        urls = config["URLS"]
        if not isinstance(urls, dict):
            errors["URLS"] = ["URLs must be a dictionary"]
        else:
            url_errors = []
            for key, url in urls.items():
                if not isinstance(url, str):
                    url_errors.append(f"Invalid URL for {key}: {url}")
                elif key != "websocket" and not url.startswith("http"):
                    url_errors.append(f"Invalid URL for {key}: {url}")
                elif key == "websocket" and not (url.startswith("ws") or url.startswith("wss")):
                    url_errors.append(f"Invalid URL for {key}: {url}")
            if url_errors:
                errors["URLS"] = url_errors

    # Validate webhook configuration
    if "WEBHOOK" in config:
        webhook = config["WEBHOOK"]
        if not isinstance(webhook, dict):
            errors["WEBHOOK"] = ["Webhook configuration must be a dictionary"]
        else:
            webhook_errors = []

            # Validate enabled flag
            if "ENABLED" in webhook and not isinstance(webhook["ENABLED"], bool):
                webhook_errors.append("ENABLED must be a boolean")

            # Validate message received webhook
            if "MESSAGE_RECEIVED" in webhook:
                msg_received = webhook["MESSAGE_RECEIVED"]
                if not isinstance(msg_received, dict):
                    webhook_errors.append("MESSAGE_RECEIVED must be a dictionary")
                else:
                    if "ENABLED" in msg_received and not isinstance(msg_received["ENABLED"], bool):
                        webhook_errors.append("MESSAGE_RECEIVED.ENABLED must be a boolean")
                    if "URL" in msg_received and msg_received["URL"] and not isinstance(msg_received["URL"], str):
                        webhook_errors.append("MESSAGE_RECEIVED.URL must be a string")
                    if "RETRY_COUNT" in msg_received and not isinstance(msg_received["RETRY_COUNT"], int):
                        webhook_errors.append("MESSAGE_RECEIVED.RETRY_COUNT must be an integer")
                    if "RETRY_DELAY" in msg_received and not isinstance(msg_received["RETRY_DELAY"], (int, float)):
                        webhook_errors.append("MESSAGE_RECEIVED.RETRY_DELAY must be a number")

            # Validate message sent webhook
            if "MESSAGE_SENT" in webhook:
                msg_sent = webhook["MESSAGE_SENT"]
                if not isinstance(msg_sent, dict):
                    webhook_errors.append("MESSAGE_SENT must be a dictionary")
                else:
                    if "ENABLED" in msg_sent and not isinstance(msg_sent["ENABLED"], bool):
                        webhook_errors.append("MESSAGE_SENT.ENABLED must be a boolean")
                    if "URL" in msg_sent and msg_sent["URL"] and not isinstance(msg_sent["URL"], str):
                        webhook_errors.append("MESSAGE_SENT.URL must be a string")
                    if "RETRY_COUNT" in msg_sent and not isinstance(msg_sent["RETRY_COUNT"], int):
                        webhook_errors.append("MESSAGE_SENT.RETRY_COUNT must be an integer")
                    if "RETRY_DELAY" in msg_sent and not isinstance(msg_sent["RETRY_DELAY"], (int, float)):
                        webhook_errors.append("MESSAGE_SENT.RETRY_DELAY must be a number")

            if webhook_errors:
                errors["WEBHOOK"] = webhook_errors

    # Validate cache configuration
    if "CACHE" in config:
        cache = config["CACHE"]
        if not isinstance(cache, dict):
            errors["CACHE"] = ["Cache configuration must be a dictionary"]
        else:
            cache_errors = []

            # Validate enabled flag
            if "ENABLED" in cache and not isinstance(cache["ENABLED"], bool):
                cache_errors.append("ENABLED must be a boolean")

            # Validate username_to_conversation_id configuration
            if "USERNAME_TO_CONVERSATION_ID" in cache:
                username_cache = cache["USERNAME_TO_CONVERSATION_ID"]
                if not isinstance(username_cache, dict):
                    cache_errors.append("USERNAME_TO_CONVERSATION_ID must be a dictionary")
                else:
                    if "ENABLED" in username_cache and not isinstance(username_cache["ENABLED"], bool):
                        cache_errors.append("USERNAME_TO_CONVERSATION_ID.ENABLED must be a boolean")

                    if "EXPIRY_SECONDS" in username_cache:
                        expiry = username_cache["EXPIRY_SECONDS"]
                        if not isinstance(expiry, int) or expiry <= 0:
                            cache_errors.append("USERNAME_TO_CONVERSATION_ID.EXPIRY_SECONDS must be a positive integer")

                    if "MAX_SIZE" in username_cache:
                        max_size = username_cache["MAX_SIZE"]
                        if not isinstance(max_size, int) or max_size <= 0:
                            cache_errors.append("USERNAME_TO_CONVERSATION_ID.MAX_SIZE must be a positive integer")

            if cache_errors:
                errors["CACHE"] = cache_errors

    return errors
