import time
from threading import Lock
from config import load_config, REQUEST_TIMEOUT, SESSION_COOLDOWN_TIME
import threading
from queue import Queue
from datetime import datetime
import logging
from threading import RLock
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class SessionManager:
    def __init__(self):
        logger.info("Initializing SessionManager")
        self.sessions = {}
        self.lock = RLock()
        self.global_steam_lock = None
        self.steam_lock_expiry = 0
        self.used_auth_codes = set()
        self.request_queue = Queue()
        self.active_requests = set()
        self.running = True
        
        # Reduce number of workers to avoid thread limits
        self.thread_pool = ThreadPoolExecutor(max_workers=5)  # Reduced from 10
        
        try:
            self.processing_thread = threading.Thread(target=self._process_queue, daemon=True)
            self.processing_thread.start()
            
            self.update_config_thread = threading.Thread(target=self._update_config_periodically, daemon=True)
            self.update_config_thread.start()
        except RuntimeError as e:
            logger.error(f"Failed to start threads: {e}")
            # Implement fallback behavior or raise appropriate exception
            
        self.config = load_config()
        self.auth_code_records = []
        logger.info("SessionManager initialization completed")

    def _update_config_periodically(self):
        while self.running:
            try:
                new_config = load_config()
                self.update_config(new_config)
                time.sleep(60)
            except Exception as e:
                logger.error(f"Error in config update thread: {str(e)}", exc_info=True)
                time.sleep(5)
    
    def cleanup(self):
        """Cleanup method to properly shut down threads"""
        logger.info("Starting SessionManager cleanup")
        self.running = False
        if self.processing_thread.is_alive():
            self.request_queue.put((None, None, None))  # Sentinel value
            self.processing_thread.join(timeout=5)
        if self.update_config_thread.is_alive():
            self.update_config_thread.join(timeout=5)
        # Shutdown thread pool
        self.thread_pool.shutdown(wait=True)
        logger.info("SessionManager cleanup completed")

    def update_config(self, new_config):
        with self.lock:
            self.config = new_config
            logger.info("Configuration updated")

    def record_auth_code_attempt(self, username, order_id, auth_code=None, status='Attempt', error_message=None):
        record = {
            'username': username,
            'order_id': order_id,
            'auth_code': auth_code,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': status,
            'error_message': error_message
        }
        self.auth_code_records.append(record)
        logger.info(f"Recorded auth code attempt: {record}")
        return record

    def get_cooldown_until(self, username, order_id):
        with self.lock:
            if username in self.sessions and order_id in self.sessions[username]:
                return self.sessions[username][order_id].get('cooldown_until')
            return None
    
    def _process_queue(self):
        logger.info("Starting to process queue")
        while self.running:
            try:
                logger.info("Waiting for new request in queue")
                username, order_id, callback = self.request_queue.get()
                
                # Check for sentinel value
                if username is None and order_id is None and callback is None:
                    break
                    
                logger.info(f"Got new request for {username}:{order_id}")
                try:
                    # Submit the task to thread pool instead of processing directly
                    self.thread_pool.submit(self._process_request, username, order_id, callback)
                except Exception as e:
                    logger.error(f"Error submitting request for {username}:{order_id}: {str(e)}", exc_info=True)
                    with self.lock:
                        self.active_requests.remove(f"{username}:{order_id}")
                finally:
                    self.request_queue.task_done()
            except Exception as e:
                logger.error(f"Unexpected error in _process_queue: {str(e)}", exc_info=True)
                time.sleep(1)

    def _process_request(self, username, order_id, callback):
        logger.info(f"Starting to process request for {username}:{order_id}")
        start_time = time.time()
        while time.time() - start_time < self.config.get('REQUEST_TIMEOUT', 30):
            with self.lock:
                cooldown_until = self.get_cooldown_until(username, order_id)
                if cooldown_until and time.time() < cooldown_until:
                    logger.info(f"Session {username}:{order_id} still in cooldown")
                    callback(username, order_id, timed_out=False)
                    return
            
            logger.info(f"Checking if can request auth code for {username}:{order_id}")
            if self.can_request_auth_code(username, order_id):
                logger.info(f"Requesting auth code for {username}:{order_id}")
                callback(username, order_id)
                return
            logger.info(f"Cannot request auth code for {username}:{order_id} yet, waiting...")
            time.sleep(1)
        logger.warning(f"Request for {username}:{order_id} timed out")
        callback(username, order_id, timed_out=True)

    def queue_request(self, username, order_id, callback):
        logger.info(f"Attempting to queue request for {username}:{order_id}")
        with self.lock:
            request_key = f"{username}:{order_id}"

            # Check if the session is in cooldown
            cooldown_until = self.get_cooldown_until(username, order_id)
            current_time = time.time()
            if cooldown_until and current_time < cooldown_until:
                remaining = int(cooldown_until - current_time)
                logger.warning(f"Cannot queue request for {request_key}; cooldown active for another {remaining} seconds")
                return False

            # Check if the request is already active
            if request_key in self.active_requests:
                logger.warning(f"Request for {request_key} already in progress")
                return False

            # Proceed to queue the request
            self.active_requests.add(request_key)
            self.request_queue.put((username, order_id, callback))
            logger.info(f"Request for {request_key} queued successfully")
            return True

    def can_request_auth_code(self, username, order_id):
        with self.lock:
            current_time = time.time()
            logger.info(f"Checking if can request auth code for {username}:{order_id}")

            # Check for global Steam lock
            if self.global_steam_lock and self.global_steam_lock != order_id:
                if current_time < self.steam_lock_expiry:
                    logger.info(f"Global Steam lock active for {self.global_steam_lock}, cannot request for {order_id}")
                    self.record_auth_code_attempt(username, order_id, status='Failure', error_message='Global Steam lock active')
                    return False
                else:
                    logger.info("Global Steam lock expired, resetting")
                    self.global_steam_lock = None
                    self.steam_lock_expiry = 0

            # If all checks pass, allow the request
            if username not in self.sessions:
                self.sessions[username] = {}
            
            self.sessions[username][order_id] = {'cooldown_until': None}
            
            # Set global Steam lock
            if not self.global_steam_lock:
                self.global_steam_lock = order_id
                self.steam_lock_expiry = current_time + self.config.get('REQUEST_TIMEOUT', 30)
                logger.info(f"Set global Steam lock for {order_id}")
            
            self.record_auth_code_attempt(username, order_id, status='Attempt')
            logger.info(f"Can request auth code for {username}:{order_id}")
            return True
        
    def reset_auth_code_records(self):
        with self.lock:
            self.auth_code_records = []
            logger.info("Auth code records have been reset")
    
    def set_cooldown(self, username, order_id):
        with self.lock:
            current_time = time.time()
            if username not in self.sessions:
                self.sessions[username] = {}
            self.sessions[username][order_id] = {
                'cooldown_until': current_time + self.config.get('SESSION_COOLDOWN_TIME', 60)
            }
            logger.info(f"Cooldown set for {username}:{order_id} until {self.sessions[username][order_id]['cooldown_until']}")
            
            # Release global Steam lock
            if self.global_steam_lock == order_id:
                self.global_steam_lock = None
                self.steam_lock_expiry = 0
                logger.info(f"Released global Steam lock for {order_id}")

    def mark_auth_code_used(self, username, order_id):
        with self.lock:
            if username in self.sessions and order_id in self.sessions[username]:
                auth_code = self.sessions[username][order_id].get('auth_code')
                if auth_code:
                    self.used_auth_codes.add(auth_code)
                    logger.info(f"Auth code {auth_code} marked as used for {username}:{order_id}")
                self.sessions[username][order_id]['auth_code_expiry'] = None
                self.sessions[username][order_id]['auth_code'] = None
            
            # Release the global Steam lock
            if self.global_steam_lock == order_id:
                self.global_steam_lock = None
                self.steam_lock_expiry = 0
                logger.info(f"Released global Steam lock for {order_id}")

    def set_auth_code(self, username, order_id, auth_code):
        with self.lock:
            if username in self.sessions and order_id in self.sessions[username]:
                if auth_code not in self.used_auth_codes:
                    self.sessions[username][order_id]['auth_code'] = auth_code
                    self.record_auth_code_attempt(username, order_id, auth_code, status='Success')
                    logger.info(f"Auth code {auth_code} set for {username}:{order_id}")
                    return True
                else:
                    self.record_auth_code_attempt(username, order_id, auth_code, status='Failure', error_message='Auth code already used')
                    logger.warning(f"Auth code {auth_code} already used for {username}:{order_id}")
            else:
                self.record_auth_code_attempt(username, order_id, auth_code, status='Failure', error_message='Invalid session')
                logger.warning(f"Invalid session for {username}:{order_id}")
            return False

    def get_auth_code_records(self):
        with self.lock:
            logger.info("Retrieving auth code records")
            return self.auth_code_records
    
    def get_auth_code(self, username, order_id):
        with self.lock:
            if username in self.sessions and order_id in self.sessions[username]:
                auth_code = self.sessions[username][order_id].get('auth_code')
                logger.info(f"Retrieved auth code for {username}:{order_id} - {auth_code}")
                return auth_code
            logger.info(f"No auth code found for {username}:{order_id}")
            return None
    
    def release_steam_lock(self, order_id):
        with self.lock:
            if self.global_steam_lock == order_id:
                self.global_steam_lock = None
                self.steam_lock_expiry = 0
                logger.info(f"Released global Steam lock for {order_id}")

    def reset_auth_code_cooldown(self, order_id):
        with self.lock:
            if order_id in self.auth_code_cooldowns:
                del self.auth_code_cooldowns[order_id]
                logger.info(f"Reset auth code cooldown for {order_id}")

    def get_all_cooldowns(self):
        """
        Retrieves all active cooldowns with remaining time for each (username, order_id).
        """
        with self.lock:
            cooldowns = []
            current_time = time.time()
            for username, orders in self.sessions.items():
                for order_id, data in orders.items():
                    cooldown_until = data.get('cooldown_until')
                    if cooldown_until and current_time < cooldown_until:
                        remaining = int(cooldown_until - current_time)
                        cooldowns.append({
                            'username': username,
                            'order_id': order_id,
                            'remaining_cooldown': remaining
                        })
            logger.info(f"Retrieved all active cooldowns: {cooldowns}")
            return cooldowns
    
    def reset_cooldown(self, username, order_id):
        """
        Resets the cooldown for a specific (username, order_id).
        """
        with self.lock:
            if username in self.sessions and order_id in self.sessions[username]:
                self.sessions[username][order_id]['cooldown_until'] = None
                logger.info(f"Cooldown reset for {username}:{order_id}")
                return True
            else:
                logger.warning(f"Attempted to reset cooldown for non-existent session: {username}:{order_id}")
                return False
    
def get_session_manager():
    global _session_manager
    if not hasattr(get_session_manager, '_session_manager'):
        get_session_manager._session_manager = SessionManager()
        get_session_manager._initialized = True
    elif hasattr(get_session_manager, '_initialized'):
        get_session_manager._session_manager.update_config(load_config())
    return get_session_manager._session_manager

session_manager = get_session_manager()