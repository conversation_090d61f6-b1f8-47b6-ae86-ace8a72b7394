from functools import wraps
from flask import jsonify
from services.order_service import get_to_ship_orders
from config import update_config
import traceback

def check_shopee_credentials(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            result = get_to_ship_orders()
            if isinstance(result, dict) and 'data' in result:
                return f(*args, **kwargs)
            else:
                update_config()
                return jsonify({"error": "Credentials are invalid. Please contact seller to update it."}), 401
        except Exception as e:
            update_config()
            print(traceback.format_exc())
            return jsonify({"error": "Failed to check credentials. Please contact seller to update it."}), 500
    return decorated_function