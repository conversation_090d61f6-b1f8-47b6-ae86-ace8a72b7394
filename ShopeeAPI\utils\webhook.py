"""
Webhook utilities for the Shopee API.

This module provides functions for sending webhook notifications.
"""
import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# Set this module's logger to INFO level (change to DEBUG for more detailed logs)
logger.setLevel(logging.INFO)


class WebhookManager:
    """
    Manager for sending webhook notifications.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the webhook manager.

        Args:
            config: Webhook configuration dictionary
        """
        self.config = config
        self.session = None

        # Log webhook configuration on initialization
        logger.info(f"Initializing WebhookManager with config: {json.dumps(config, indent=2)}")

        # Check if webhooks are enabled - ONLY check uppercase keys to match config.json
        if not config.get("ENABLED", False):
            logger.info("Webhooks are disabled in configuration")
        else:
            logger.info("Webhooks are enabled in configuration")

            # Check message_received webhook configuration - ONLY check uppercase keys
            message_received_config = config.get("MESSAGE_RECEIVED", {})
            if message_received_config.get("ENABLED", False):
                logger.info(f"Message received webhooks enabled, URL: {message_received_config.get('URL', 'Not set')}")
            else:
                logger.info("Message received webhooks are disabled")

            # Check message_sent webhook configuration - ONLY check uppercase keys
            message_sent_config = config.get("MESSAGE_SENT", {})
            if message_sent_config.get("ENABLED", False):
                logger.info(f"Message sent webhooks enabled, URL: {message_sent_config.get('URL', 'Not set')}")
            else:
                logger.info("Message sent webhooks are disabled")

    async def initialize(self):
        """Initialize the aiohttp session."""
        try:
            if self.session is None or self.session.closed:
                logger.debug("Creating new aiohttp ClientSession for webhook requests")
                self.session = aiohttp.ClientSession()
                logger.debug("Successfully created aiohttp ClientSession")
            else:
                logger.debug("Using existing aiohttp ClientSession")
        except Exception as e:
            logger.error(f"Error initializing aiohttp ClientSession: {e}")
            import traceback
            logger.debug(f"Session initialization error details: {traceback.format_exc()}")

    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    async def send_webhook(self, webhook_type: str, data: Dict[str, Any]) -> bool:
        """
        Send a webhook notification.

        Args:
            webhook_type: Type of webhook (message_received or message_sent)
            data: Data to send in the webhook

        Returns:
            bool: True if the webhook was sent successfully, False otherwise
        """
        # Check if webhooks are enabled - ONLY check uppercase keys to match config.json
        if not self.config.get("ENABLED", False):
            logger.info(f"Webhooks are disabled, not sending {webhook_type} webhook")
            return False

        # Get the webhook type config - ONLY use uppercase keys to match config.json
        webhook_config = self.config.get(webhook_type, {})

        # Check if this specific webhook type is enabled - ONLY check uppercase keys
        if not webhook_config.get("ENABLED", False):
            logger.info(f"{webhook_type} webhooks are disabled")
            return False

        # Get webhook URL - ONLY use uppercase keys
        url = webhook_config.get("URL", "")
        if not url:
            logger.warning(f"No URL configured for {webhook_type} webhook")
            return False

        # Get retry settings - ONLY use uppercase keys
        retry_count = webhook_config.get("RETRY_COUNT", 3)
        retry_delay = webhook_config.get("RETRY_DELAY", 5)

        # Initialize session if needed
        await self.initialize()

        # Add timestamp to data
        data["timestamp"] = time.time()

        # Try to send the webhook with retries
        for attempt in range(retry_count + 1):
            try:
                async with self.session.post(
                    url,
                    json=data,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                ) as response:
                    response_text = await response.text()

                    if response.status < 400:
                        logger.info(f"Successfully sent {webhook_type} webhook to {url}")
                        return True
                    else:
                        logger.warning(
                            f"Failed to send {webhook_type} webhook to {url}: "
                            f"HTTP {response.status} - {response_text}"
                        )
            except Exception as e:
                logger.error(f"Error sending {webhook_type} webhook to {url}: {e}")

            # If we've reached the maximum number of retries, give up
            if attempt >= retry_count:
                logger.error(f"Failed to send {webhook_type} webhook after {retry_count} retries")
                return False

            # Wait before retrying
            await asyncio.sleep(retry_delay)

        return False

    async def send_message_received_webhook(self, message_data: Dict[str, Any]) -> bool:
        """
        Send a webhook notification for a received message.

        Args:
            message_data: Message data

        Returns:
            bool: True if the webhook was sent successfully, False otherwise
        """
        # Use uppercase webhook type to match config.json
        return await self.send_webhook("MESSAGE_RECEIVED", message_data)

    async def send_message_sent_webhook(self, message_data: Dict[str, Any]) -> bool:
        """
        Send a webhook notification for a sent message.

        Args:
            message_data: Message data

        Returns:
            bool: True if the webhook was sent successfully, False otherwise
        """
        # Use uppercase webhook type to match config.json
        return await self.send_webhook("MESSAGE_SENT", message_data)
