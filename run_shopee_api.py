"""
Runner script for ShopeeAPI service.
"""
import os
import uvicorn
import sys
import pathlib

# Ensure the current directory is in the Python path
current_dir = str(pathlib.Path(__file__).parent.absolute())
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Print the current working directory and config path for debugging
print(f"Current working directory: {os.getcwd()}")
print(f"ShopeeAPI directory: {os.path.join(os.getcwd(), 'ShopeeAPI')}")
print(f"Expected config path: {os.path.join(os.getcwd(), 'ShopeeAPI', 'config.json')}")

if __name__ == "__main__":
    # Define port
    port = int(os.environ.get("PORT", 8000))

    # Run the application
    print(f"Starting ShopeeAPI service on port {port}...")
    uvicorn.run("ShopeeAPI.api:app", host="0.0.0.0", port=port, reload=True)