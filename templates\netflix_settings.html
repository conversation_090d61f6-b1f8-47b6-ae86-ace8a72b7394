{% extends "base.html" %}

{% block title %}Netflix Settings{% endblock %}
{% block header %}Netflix Settings{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="netflixSettingsData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Main content -->
        <div class="flex-1">
            <!-- Performance Settings -->
            <div class="section-content mb-8">
                <div class="bg-white p-6 rounded-lg shadow-md config-item">
                    <h2 class="text-xl font-bold mb-4">Performance Settings</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="netflix_max_retries" class="block text-sm font-medium text-gray-700">
                                Maximum Retries
                            </label>
                            <input type="number" 
                                   id="netflix_max_retries" 
                                   x-model="config.NETFLIX_MAX_RETRIES"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>

                        <div>
                            <label for="netflix_request_timeout" class="block text-sm font-medium text-gray-700">
                                Request Timeout (seconds)
                            </label>
                            <input type="number" 
                                   id="netflix_request_timeout" 
                                   x-model="config.NETFLIX_REQUEST_TIMEOUT"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>

                        <div>
                            <label for="netflix_retry_delay" class="block text-sm font-medium text-gray-700">
                                Retry Delay (seconds)
                            </label>
                            <input type="number" 
                                   id="netflix_retry_delay" 
                                   x-model="config.NETFLIX_RETRY_DELAY"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>

                        <div>
                            <label for="netflix_session_cooldown" class="block text-sm font-medium text-gray-700">
                                Session Cooldown Time (seconds)
                            </label>
                            <input type="number" 
                                   id="netflix_session_cooldown" 
                                   x-model="config.NETFLIX_SESSION_COOLDOWN_TIME"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Redeem Period Settings -->
            <div class="section-content mb-8">
                <div class="bg-white p-6 rounded-lg shadow-md config-item">
                    <h2 class="text-xl font-bold mb-4">Netflix Redeem Periods</h2>
                    <div class="mb-4">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        SKU
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Period (Days)
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(period, sku) in config.NETFLIX_REDEEM_PERIODS" :key="sku">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap" x-text="sku"></td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="number" 
                                                   x-model="config.NETFLIX_REDEEM_PERIODS[sku]"
                                                   class="block w-24 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click="deleteRedeemPeriod(sku)"
                                                class="text-red-600 hover:text-red-900">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <button @click="addNetflixRedeemPeriod"
                            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Netflix Redeem Period
                        </button>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="mt-6">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function netflixSettingsData() {
        return {
            config: {},
            isLoaded: false,
            init() {
                this.loadConfig();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        if (!this.config.NETFLIX_REDEEM_PERIODS) {
                            this.config.NETFLIX_REDEEM_PERIODS = {};
                        }
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            addNetflixRedeemPeriod() {
                const newSku = prompt("Enter the SKU for the new Netflix redeem period:");
                if (newSku) {
                    if (!this.config.NETFLIX_REDEEM_PERIODS) {
                        this.config.NETFLIX_REDEEM_PERIODS = {};
                    }
                    this.config.NETFLIX_REDEEM_PERIODS[newSku] = 30; // Default to 30 days
                }
            },
            deleteRedeemPeriod(sku) {
                if (confirm('Are you sure you want to delete this redeem period?')) {
                    delete this.config.NETFLIX_REDEEM_PERIODS[sku];
                    this.config = { ...this.config }; // Trigger Alpine.js reactivity
                }
            },
            saveConfig() {
                // Convert numeric fields to numbers
                const numericFields = [
                    'NETFLIX_MAX_RETRIES',
                    'NETFLIX_REQUEST_TIMEOUT',
                    'NETFLIX_RETRY_DELAY',
                    'NETFLIX_SESSION_COOLDOWN_TIME'
                ];
                
                numericFields.forEach(field => {
                    if (this.config[field] !== undefined) {
                        this.config[field] = Number(this.config[field]);
                    }
                });

                // Convert NETFLIX_REDEEM_PERIODS values to numbers
                if (this.config.NETFLIX_REDEEM_PERIODS) {
                    for (let key in this.config.NETFLIX_REDEEM_PERIODS) {
                        this.config.NETFLIX_REDEEM_PERIODS[key] = Number(this.config.NETFLIX_REDEEM_PERIODS[key]);
                    }
                }

                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    this.animateSaveButton();
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the configuration.');
                });
            },
            animateInitialLoad() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}