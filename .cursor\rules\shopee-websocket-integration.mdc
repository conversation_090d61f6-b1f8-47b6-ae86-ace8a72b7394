---
description: 
globs: 
alwaysApply: false
---
# Shopee WebSocket Integration Guide

## Project Structure
The Shopee API integration is primarily handled in the [ShopeeAPI](mdc:ShopeeAPI) directory. Key files include:

- [ShopeeAPI/services/chat.py](mdc:ShopeeAPI/services/chat.py) - Chat service implementation
- [ShopeeAPI/models/chat.py](mdc:ShopeeAPI/models/chat.py) - Chat data models
- [ShopeeAPI/core/session.py](mdc:ShopeeAPI/core/session.py) - Session management

## WebSocket Integration
The project connects to Shop<PERSON>'s WebSocket endpoint:
```
wss://seller-push-ws.shopee.com.my/socket.io/?region=my&_v=new&EIO=3&transport=websocket
```

### Key Features
1. Real-time message reception through WebSocket connection
2. Message forwarding to FastAPI endpoints
3. Client notification system

### Message Format
WebSocket messages follow this structure:
```json
{
  "message_type": "message",
  "message_content": {
    "id": "...",
    "shop_id": "...",
    "content": {
      "text": "..."
    }
  }
}
```

### Integration Points
- WebSocket client connection in FastAPI
- Real-time message processing
- Client notification through WebSocket endpoints

## Best Practices
1. Maintain persistent WebSocket connections
2. Implement reconnection logic
3. Handle message queuing
4. Validate message integrity
5. Monitor connection health

