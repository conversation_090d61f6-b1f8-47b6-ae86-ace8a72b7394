#!/usr/bin/env python3
"""
Final comprehensive test for the send_message fix.
"""

import requests
import json
import time


def test_server_connectivity():
    """Test if the server is running."""
    try:
        response = requests.get("http://localhost:456/docs", timeout=5)
        return response.status_code == 200
    except:
        return False


def test_send_message(username: str, message: str):
    """Test sending a message to a specific username."""
    print(f"\n=== Testing send_message for: {username} ===")
    
    payload = {
        "text": message,
        "username": username,
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:456/chat/send_message", 
            json=payload, 
            timeout=30
        )
        print(f"Response status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print(f"✅ SUCCESS: Message sent to {username}")
            return True
        else:
            print(f"❌ FAILED: Message to {username} failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False


def main():
    """Main test function."""
    print("=== Final ShopeeAPI Send Message Test ===")
    
    # Check server connectivity
    if not test_server_connectivity():
        print("❌ ERROR: Server is not running on port 456!")
        print("Please start the server first.")
        return
    
    print("✅ Server is running")
    
    # Test both usernames
    working_username = "me0tn_14qo"
    failing_username = "syahmialfabet"
    
    # Test the working username first
    working_result = test_send_message(working_username, f"Test message for {working_username}")
    
    # Test the previously failing username
    failing_result = test_send_message(failing_username, f"Test message for {failing_username}")
    
    # Summary
    print(f"\n{'='*60}")
    print("FINAL RESULTS:")
    print(f"{'='*60}")
    print(f"{working_username}: {'✅ SUCCESS' if working_result else '❌ FAILED'}")
    print(f"{failing_username}: {'✅ SUCCESS' if failing_result else '❌ FAILED'}")
    
    if working_result and failing_result:
        print("\n🎉 ALL TESTS PASSED! The fix is working correctly.")
    elif failing_result:
        print(f"\n🎉 FIX SUCCESSFUL! {failing_username} is now working.")
    else:
        print(f"\n❌ ISSUE PERSISTS: {failing_username} is still not working.")
    
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
