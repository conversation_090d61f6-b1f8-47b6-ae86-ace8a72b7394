# ShopeeAPI Test Suite

This directory contains the test suite for the ShopeeAPI package.

## Test Structure

- `test_auth.py`: Tests for authentication functionality
- `test_orders.py`: Tests for order-related operations
- `test_chat.py`: Tests for chat-related operations

## Running Tests

You can run the tests using the unittest module:

```bash
# Run all tests
python -m unittest discover -s ShopeeAPI/tests

# Run a specific test file
python -m unittest ShopeeAPI/tests/test_auth.py

# Run a specific test case
python -m unittest ShopeeAPI.tests.test_auth.TestAuthAPI
```

## Test Requirements

- The FastAPI server should be running for API endpoint tests
- Tests that require a running server will be skipped if the server is not available
- Mock tests for the client functionality do not require a running server

## Test Configuration

The tests use the following configuration:

- API URL: http://localhost:8000
- Default test order number: 2412064GY1714Y

## Adding New Tests

When adding new tests:

1. Create a new test file if needed
2. Add test cases that inherit from `unittest.TestCase`
3. Use descriptive test method names that start with `test_`
4. Add proper assertions to verify functionality
5. Use mocks for external dependencies when appropriate
6. Add skip conditions for tests that require external resources

## Test Coverage

The test suite aims to cover:

- API endpoints
- Client functionality
- Error handling
- Edge cases

## Notes

- The standalone test scripts (`test_api.py`, `direct_test.py`) have been integrated into this structured test suite
- The hot reload functionality is tested in `test_auth.py`
