<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canva Pro Invitation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='spinkit.css') }}">
    <style>
        .floating-label-input {
            position: relative;
        }

        .floating-label-input input {
            height: 3rem;
            padding-top: 1rem;
        }

        .floating-label-input label {
            position: absolute;
            top: 0.5rem;
            left: 0.75rem;
            transition: all 0.2s ease-out;
            pointer-events: none;
        }

        .floating-label-input input:focus+label,
        .floating-label-input input:not(:placeholder-shown)+label {
            font-size: 0.75rem;
            top: 0;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 min-h-screen flex items-center justify-center relative">
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
        <h1 class="text-2xl font-bold text-purple-800 mb-6">Get Canva Pro</h1>
        <form id="canvaForm" class="space-y-4">
            <div class="floating-label-input">
                <input type="text" id="orderId" name="orderId" required
                    class="px-3 mt-1 block w-full rounded-md border-purple-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 placeholder-transparent">
                <label for="orderId" class="text-sm font-medium text-purple-700">Shopee Order ID</label>
            </div>
            <button type="submit"
                class="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 disabled:bg-purple-400 disabled:text-gray-200 disabled:cursor-not-allowed">
                Get Invitation
            </button>
        </form>
    </div>

    <!-- Modal -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div id="modalContent" class="mt-3 text-center">
                <div id="loadingIndicator" class="mx-auto flex items-center justify-center h-12 w-12">
                    <div class="sk-wave sk-primary">
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                        <div class="sk-wave-rect"></div>
                    </div>
                </div>
                <div id="successIndicator" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 hidden">
                    <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div id="errorIndicator" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 hidden">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2" id="modalTitle">Processing</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500" id="modalMessage">Processing your request...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('canvaForm');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const successIndicator = document.getElementById('successIndicator');
        const errorIndicator = document.getElementById('errorIndicator');
        const submitButton = form.querySelector('button[type="submit"]');

        // 获取URL参数函数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // 自动填充订单ID
        var orderIdParam = getUrlParameter('OrderId');
        if (orderIdParam) {
            document.getElementById('orderId').value = orderIdParam;
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const orderId = document.getElementById('orderId').value.trim();

            submitButton.disabled = true;
            showModal();

            try {
                const response = await axios.post('/api/create_invitation', {
                    order_sn: orderId
                });

                if (response.data.invitation_url) {
                    successIndicator.classList.remove('hidden');
                    loadingIndicator.classList.add('hidden');
                    modalTitle.textContent = 'Success';
                    modalMessage.innerHTML = `Your invitation link has been created!<br><a href="${response.data.invitation_url}" class="text-purple-600 hover:text-purple-800">Click here to join Canva Pro</a>`;
                } else {
                    throw new Error('No invitation URL received');
                }
            } catch (error) {
                errorIndicator.classList.remove('hidden');
                loadingIndicator.classList.add('hidden');
                modalTitle.textContent = 'Error';
                modalMessage.textContent = error.response?.data?.error || 'An error occurred while processing your request.';
            } finally {
                submitButton.disabled = false;
            }
        });

        function showModal() {
            modal.classList.remove('hidden');
            loadingIndicator.classList.remove('hidden');
            successIndicator.classList.add('hidden');
            errorIndicator.classList.add('hidden');
            modalTitle.textContent = 'Processing';
            modalMessage.textContent = 'Processing your request...';
        }

        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });
    </script>
</body>

</html>