#!/usr/bin/env python3
"""
Deployment script for ShopeeAPI.

This script handles:
1. Building the package
2. Uploading to a private repository or server
3. Installing/updating on the server
4. Restarting the service

Usage:
    python deploy.py [--build] [--upload] [--install] [--restart] [--all]
"""
import argparse
import os
import subprocess
import sys
import shutil
import datetime

# Configuration
SERVER_HOST = "your-server-hostname"  # Change to your server hostname or IP
SERVER_USER = "your-username"  # Change to your server username
SERVER_PATH = "/path/to/shopee-api"  # Change to your server deployment path
PACKAGE_NAME = "shopee-api"
VERSION_FILE = "version.txt"

def run_command(command, cwd=None):
    """Run a shell command and return the output."""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, check=True, text=True, 
                           stdout=subprocess.PIPE, stderr=subprocess.PIPE, cwd=cwd)
    return result.stdout.strip()

def build_package():
    """Build the Python package."""
    print("Building package...")
    
    # Update version number
    version = datetime.datetime.now().strftime("%Y.%m.%d.%H%M")
    with open(VERSION_FILE, "w") as f:
        f.write(version)
    
    # Update version in setup.py
    with open("setup.py", "r") as f:
        setup_content = f.read()
    
    setup_content = setup_content.replace(
        'version="1.0.0"', 
        f'version="{version}"'
    )
    
    with open("setup.py", "w") as f:
        f.write(setup_content)
    
    # Clean previous builds
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists(f"{PACKAGE_NAME}.egg-info"):
        shutil.rmtree(f"{PACKAGE_NAME}.egg-info")
    
    # Build the package
    run_command("python setup.py sdist bdist_wheel")
    
    print(f"Package built with version {version}")
    return version

def upload_package(version):
    """Upload the package to the server."""
    print("Uploading package to server...")
    
    # Create a deployment package with the wheel file and config
    os.makedirs("deploy", exist_ok=True)
    
    # Copy the wheel file
    wheel_file = f"dist/{PACKAGE_NAME.replace('-', '_')}-{version}-py3-none-any.whl"
    shutil.copy(wheel_file, "deploy/")
    
    # Copy deployment scripts
    with open("deploy/install.sh", "w") as f:
        f.write(f"""#!/bin/bash
# Installation script for ShopeeAPI
WHEEL_FILE="{os.path.basename(wheel_file)}"

# Activate virtual environment if it exists, or create it
if [ -d "venv" ]; then
    source venv/bin/activate
else
    python3 -m venv venv
    source venv/bin/activate
fi

# Install/upgrade the package
pip install --upgrade $WHEEL_FILE

# Copy config if it doesn't exist
if [ ! -f "config.json" ]; then
    cp venv/lib/python*/site-packages/ShopeeAPI/config.json.example config.json
    echo "Created config.json from example. Please edit it with your credentials."
fi

# Create systemd service file if it doesn't exist
if [ ! -f "/etc/systemd/system/shopee-api.service" ]; then
    echo "Creating systemd service file..."
    cat > shopee-api.service << EOL
[Unit]
Description=Shopee API Service
After=network.target

[Service]
User=$USER
WorkingDirectory=$(pwd)
ExecStart=$(pwd)/venv/bin/python -m ShopeeAPI.main
Restart=always
RestartSec=5
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=shopee-api

[Install]
WantedBy=multi-user.target
EOL

    sudo mv shopee-api.service /etc/systemd/system/
    sudo systemctl daemon-reload
    sudo systemctl enable shopee-api
fi

echo "Installation complete!"
""")
    
    # Make the install script executable
    os.chmod("deploy/install.sh", 0o755)
    
    # Create a restart script
    with open("deploy/restart.sh", "w") as f:
        f.write("""#!/bin/bash
# Restart the ShopeeAPI service
sudo systemctl restart shopee-api
echo "ShopeeAPI service restarted"
""")
    
    # Make the restart script executable
    os.chmod("deploy/restart.sh", 0o755)
    
    # Create a deployment package
    deployment_package = f"shopee-api-{version}-deploy.tar.gz"
    run_command(f"tar -czf {deployment_package} -C deploy .")
    
    # Upload to server using scp
    run_command(f"scp {deployment_package} {SERVER_USER}@{SERVER_HOST}:{SERVER_PATH}/")
    
    print(f"Package uploaded to server: {deployment_package}")
    return deployment_package

def install_package(deployment_package):
    """Install the package on the server."""
    print("Installing package on server...")
    
    # SSH to the server and run the installation
    run_command(f"""ssh {SERVER_USER}@{SERVER_HOST} "cd {SERVER_PATH} && \\
        tar -xzf {deployment_package} && \\
        ./install.sh"
    """)
    
    print("Package installed on server")

def restart_service():
    """Restart the service on the server."""
    print("Restarting service...")
    
    # SSH to the server and restart the service
    run_command(f"""ssh {SERVER_USER}@{SERVER_HOST} "cd {SERVER_PATH} && \\
        ./restart.sh"
    """)
    
    print("Service restarted")

def main():
    """Main function to handle deployment."""
    parser = argparse.ArgumentParser(description="Deploy ShopeeAPI")
    parser.add_argument("--build", action="store_true", help="Build the package")
    parser.add_argument("--upload", action="store_true", help="Upload the package to the server")
    parser.add_argument("--install", action="store_true", help="Install the package on the server")
    parser.add_argument("--restart", action="store_true", help="Restart the service")
    parser.add_argument("--all", action="store_true", help="Perform all actions")
    
    args = parser.parse_args()
    
    # If no arguments are provided, show help
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    # Perform actions based on arguments
    version = None
    deployment_package = None
    
    if args.build or args.all:
        version = build_package()
    
    if args.upload or args.all:
        if not version:
            with open(VERSION_FILE, "r") as f:
                version = f.read().strip()
        deployment_package = upload_package(version)
    
    if args.install or args.all:
        if not deployment_package:
            deployment_package = f"shopee-api-{version}-deploy.tar.gz"
        install_package(deployment_package)
    
    if args.restart or args.all:
        restart_service()
    
    print("Deployment completed successfully!")

if __name__ == "__main__":
    main()
