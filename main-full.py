from flask import Flask, jsonify, request, abort
from requests import Session
import uuid
import config
from typing import List, Dict, Any
from urllib.parse import quote

app = Flask(__name__)

# Initialize a global session with specified headers
session = Session()
session.headers.update({
    "User-Agent": config.USER_AGENT,
    "Authorization": config.AUTHORIZATION_CODE,
    "Content-Type": "application/json",
    "Cookie": config.COOKIE
})

def generate_request_id():
    return str(uuid.uuid4())

def get_initial_order_list(order_status: str, order_sn: str = None) -> Dict[str, Any]:
    base_filter = {
        "fulfillment_type": 0,
        "is_drop_off": 0,
        "fulfillment_source": 0,
        "prescription_filter": 0
    }
    
    if order_status == "to_ship":
        order_list_tab = config.TO_SHIP_TAB
        filter_params = {**base_filter, "order_to_ship_status": 1}
        sort_params = {"sort_type": 2, "ascending": True}
    elif order_status == "shipped":
        order_list_tab = config.SHIPPED_TAB
        filter_params = base_filter
        sort_params = {"sort_type": 3, "ascending": False}
    elif order_status == "completed":
        order_list_tab = config.COMPLETED_TAB
        filter_params = base_filter
        sort_params = {"sort_type": 3, "ascending": False}
    elif order_status == "all":
        order_list_tab = config.ALL_ORDERS_TAB
        filter_params = base_filter
        sort_params = {"sort_type": 3, "ascending": False}
    else:
        raise ValueError("Invalid order status")

    initial_payload = {
        "order_list_tab": order_list_tab,
        "entity_type": 1,
        "pagination": {"from_page_number": 1, "page_number": 1, "page_size": config.PAGE_SIZE},
        "filter": filter_params,
        "sort": sort_params
    }
    
    if order_sn:
        initial_payload["search_param"] = {"keyword": order_sn, "category": 1}

    initial_response = session.post(config.INITIAL_ORDER_LIST_URL, params=get_common_params(), json=initial_payload)
    return initial_response.json()

def get_order_details(order_ids: List[int], order_status: str) -> Dict[str, Any]:
    if order_status == "to_ship":
        order_list_tab = config.TO_SHIP_TAB
    elif order_status == "shipped":
        order_list_tab = config.SHIPPED_TAB
    elif order_status == "completed":
        order_list_tab = config.COMPLETED_TAB
    elif order_status == "all":
        order_list_tab = config.ALL_ORDERS_TAB
    else:
        raise ValueError("Invalid order status")

    details_payload = {
        "order_list_tab": order_list_tab,
        "order_param_list": [
            {"order_id": order_id, "shop_id": config.SHOP_ID, "region_id": config.REGION_ID}
            for order_id in order_ids
        ]
    }

    details_response = session.post(config.ORDER_DETAILS_URL, params=get_common_params(), json=details_payload)
    return details_response.json()

def get_common_params() -> Dict[str, str]:
    return {
        "SPC_CDS": config.SPC_CDS,
        "SPC_CDS_VER": config.SPC_CDS_VER
    }

def process_orders(initial_data: Dict[str, Any], order_status: str, status_filter: str = None) -> Dict[str, Any]:
    # Check if there are any orders in the initial data
    if not initial_data['data']['index_list']:
        return {"data": {"card_list": []}}

    order_ids = [order['order_id'] for order in initial_data['data']['index_list']]
    details_data = get_order_details(order_ids, order_status)

    # Check if there are any orders in the details data
    if not details_data.get('data', {}).get('card_list'):
        return {"data": {"card_list": []}}

    if status_filter and order_status != "all":
        filtered_orders = [
            order for order in details_data['data']['card_list']
            if (order_status == "to_ship" and order['package_card']['status_info']['status'] == status_filter) or
               (order_status != "to_ship" and order['order_card']['status_info']['status'] == status_filter)
        ]
        return {"data": {"card_list": filtered_orders}}
    
    return details_data


@app.route('/api/get_to_ship_orders', methods=['GET'])
def get_to_ship_orders():
    initial_data = get_initial_order_list("to_ship")
    return jsonify(process_orders(initial_data, "to_ship"))

@app.route('/api/get_shipped_orders', methods=['GET'])
def get_shipped_orders():
    initial_data = get_initial_order_list("shipped")
    return jsonify(process_orders(initial_data, "shipped", status_filter="Shipped"))

@app.route('/api/get_completed_orders', methods=['GET'])
def get_completed_orders():
    initial_data = get_initial_order_list("completed")
    return jsonify(process_orders(initial_data, "completed", status_filter="Completed"))

@app.route('/api/search_order', methods=['GET'])
def search_order():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn parameter is required"}), 400
    
    initial_data = get_initial_order_list("all", order_sn)
    return jsonify(process_orders(initial_data, "all"))

@app.route('/api/ship_order', methods=['GET'])
def ship_order():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn is required"}), 400

    # Search for the order
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return jsonify({"error": "Order not found"}), 404

    order = order_data['data']['card_list'][0]['order_card']
    order_id = order['order_ext_info']['order_id']
    package_number = order['package_ext_info_list'][0]['package_number']

    # Prepare the payload for shipping
    payload = {
        "order_id": order_id,
        "package_number": package_number,
        "shipping_proof": "",
        "integrated": 0,
        "shipping_carrier": None,
        "shipping_mode": "non_integrated"
    }

    # Send the POST request to init_order
    ship_response = session.post(
        config.INIT_ORDER_URL,
        params=get_common_params(),
        json=payload
    )

    if ship_response.status_code != 200:
        abort(500, description="Failed to initiate shipping")

    ship_data = ship_response.json()
    if ship_data['code'] != 0:
        return jsonify({"error": ship_data['message']}), 400

    return jsonify({"message": "Order shipped successfully", "data": ship_data['data']})

@app.route('/api/get_order_status', methods=['GET'])
def get_order_status():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn parameter is required"}), 400
    
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return jsonify({"error": "Order not found"}), 404

    order = order_data['data']['card_list'][0]['order_card']
    status = order['status_info']['status']

    return jsonify({
        "order_sn": order_sn,
        "status": status
    })

@app.route('/api/get_conversation_info_by_ordersn', methods=['GET'])
def get_conversation_info_by_ordersn():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn parameter is required"}), 400

    # Search for the order to get user_id
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return jsonify({"error": "Order not found"}), 404

    order = order_data['data']['card_list'][0]['order_card']
    user_id = order['order_ext_info']['buyer_user_id']

    # Prepare the payload and params for the conversation request
    payload = {
        "user_id": user_id,
        "shop_id": config.SHOP_ID
    }

    csrf_token = config.CSRF_TOKEN
    spc_cds_chat = config.SPC_CDS_CHAT

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": csrf_token,
        "SPC_CDS_CHAT": spc_cds_chat,
        "x-shop-region": config.REGION_ID,
        "_api_source": "sc"
    }
    # Send the POST request to get conversation info using the new session
    conversation_response = session.post(
        config.CONVERSATION_URL,
        params=params,
        json=payload
    )

    if conversation_response.status_code != 200:
        return jsonify({"error": "Failed to retrieve conversation info"}), 500

    conversation_data = conversation_response.json()
    return jsonify(conversation_data)

@app.route('/api/get_conversation_info_by_username', methods=['GET'])
def get_conversation_info_by_username():
    username = request.args.get('username')
    if not username:
        return jsonify({"error": "username parameter is required"}), 400

    # Prepare the params for the conversation search request
    params = {
        "per_page": "20",
        "keyword": username,
        "type": "3",
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }

    # Send the GET request to search for conversation info
    conversation_response = session.get(
        config.CONVERSATION_SEARCH_URL,
        params=params
    )

    if conversation_response.status_code != 200:
        return jsonify({"error": "Failed to retrieve conversation info"}), 500

    conversation_data = conversation_response.json()
    return jsonify(conversation_data)

def get_conversation_info_by_username_helper(username):
    if not username:
        return None, {"error": "username parameter is required"}

    # Prepare the params for the conversation search request
    params = {
        "per_page": "20",
        "keyword": username,
        "type": "3",
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }

    # Send the GET request to search for conversation info
    conversation_response = session.get(
        config.CONVERSATION_SEARCH_URL,
        params=params
    )

    if conversation_response.status_code != 200:
        return None, {"error": "Failed to retrieve conversation info"}

    return conversation_response.json(), None

def get_conversation_info_by_ordersn_helper(order_sn):
    if not order_sn:
        return None, {"error": "order_sn parameter is required"}

    # Search for the order to get user_id
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return None, {"error": "Order not found"}

    order = order_data['data']['card_list'][0]['order_card']
    user_id = order['order_ext_info']['buyer_user_id']

    # Prepare the payload and params for the conversation request
    payload = {
        "user_id": user_id,
        "shop_id": config.SHOP_ID
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": config.CSRF_TOKEN,
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "_api_source": "sc"
    }

    # Send the POST request to get conversation info using the new session
    conversation_response = session.post(
        config.CONVERSATION_URL,
        params=params,
        json=payload
    )

    if conversation_response.status_code != 200:
        return None, {"error": "Failed to retrieve conversation info"}

    return conversation_response.json(), None

@app.route('/api/send_chat_message', methods=['POST'])
def send_chat_message():
    payload = request.json
    if not payload:
        return jsonify({"error": "No payload provided"}), 400

    # Get conversation info based on username or order_sn
    conversation_info = None
    error = None
    if 'username' in payload:
        conversation_info, error = get_conversation_info_by_username_helper(payload['username'])
    elif 'order_sn' in payload:
        conversation_info, error = get_conversation_info_by_ordersn_helper(payload['order_sn'])
    else:
        return jsonify({"error": "Either username or order_sn is required"}), 400

    if error:
        return jsonify(error), 500

    if not conversation_info:
        return jsonify({"error": "Failed to retrieve conversation info"}), 500

    # Extract necessary information from conversation_info
    conversation_id = None
    to_id = None

    if 'order_sn' in payload:
        conversation_id = conversation_info.get('id')
        to_id = conversation_info.get('to_id')
    elif 'username' in payload:
        conversations = conversation_info.get('conversation_search_result', {}).get('conversations', [])
        if conversations:
            buyer_info = conversations[0]
            conversation_id = f"{buyer_info.get('shop_id')}_{buyer_info.get('buyer_id')}"
            to_id = buyer_info.get('buyer_id')

    if not conversation_id or not to_id:
        return jsonify({"error": "Failed to extract conversation_id or to_id"}), 500

    # Prepare the message payload
    message_payload = {
        "request_id": generate_request_id(),
        "to_id": to_id,
        "type": "text",
        "content": {
            "text": payload.get('text', ''),
            "uid": str(uuid.uuid4())
        },
        "shop_id": config.SHOP_ID,
        "chat_send_option": {
            "force_send_cancel_order_warning": payload.get('force_send_cancel_order_warning', False),
            "comply_cancel_order_warning": payload.get('comply_cancel_order_warning', False)
        },
        "entry_point": "direct_chat_entry_point",
        "choice_info": {
            "real_shop_id": None
        },
        "conversation_id": conversation_id,
        "re_policy": {
            "dfp_access_f": "P6HFksuWQ8Tap+DnhVxOqA==|5hMi/K0kBKyUzxNNpYITKExyqO91xRwAnZhXKuh6M1gdq6FwNBmbnbrseJFi5oNJb5t6y18W0D0YIw==|u0PcFqU736y8ZD/U|08|3"
        }
    }

    # Prepare query parameters
    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "uuid": str(uuid.uuid4())
    }

    # Send the POST request to send the chat message
    chat_response = session.post(
        config.CHAT_MESSAGE_URL,
        params=params,
        json=message_payload
    )

    if chat_response.status_code != 200:
        return jsonify({"error": "Failed to send chat message"}), 500

    chat_data = chat_response.json()
    return jsonify(chat_data)

if __name__ == '__main__':
    app.run(debug=True)