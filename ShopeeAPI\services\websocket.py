"""
WebSocket service for Shopee API.

This module provides WebSocket functionality for real-time message delivery.
It connects to Shopee's WebSocket server and forwards messages to connected clients.
It also caches messages when enabled in the configuration.
"""
import asyncio
import json
import logging
import os
import time
import websockets
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import WebSocket, WebSocketDisconnect

from ..core.session import ShopeeSession
from ..core.config import ShopeeConfig
from ..services.chat import ChatService
from ..core.cache import CacheManager
from ..utils.webhook import WebhookManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# Set this module's logger to INFO level (change to DEBUG for more detailed logs)
logger.setLevel(logging.INFO)


class WebSocketService:
    """
    WebSocket service for Shopee API.

    This service manages WebSocket connections to Shopee and forwards messages to connected clients.
    It also caches messages when enabled in the configuration.
    """

    def __init__(self, session: ShopeeSession, config: ShopeeConfig, chat_service: ChatService):
        """
        Initialize the WebSocket service.

        Args:
            session: ShopeeSession instance for authentication
            config: ShopeeConfig instance for configuration
            chat_service: ChatService instance for chat operations
        """
        self.session = session
        self.config = config
        self.chat_service = chat_service

        # Store connected clients
        self.connected_clients: Dict[str, WebSocket] = {}

        # Track connection state
        self.is_connected = False
        self.last_message_time: Optional[datetime] = None
        self.last_ping_time: Optional[datetime] = None
        self.last_pong_time: Optional[datetime] = None
        self.reconnect_attempt = 0
        self.ws_connection = None
        self.ws_task = None

        # Flag to indicate that a reconnection is needed after config update
        self.needs_reconnect_after_config_update = False

        # Initialize cache manager
        cache_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cache')
        # Create cache directory if it doesn't exist
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        self.cache_manager = CacheManager(self.config.cache, cache_dir)

        # Print the raw webhook config from the config object for debugging
        print(f"Raw webhook config from config object: {json.dumps(self.config.webhook, indent=2)}")

        # Convert webhook config to uppercase keys to match config.json format
        webhook_config = {
            "ENABLED": self.config.webhook["enabled"],  # Use the value from config.json
            "MESSAGE_RECEIVED": {
                "ENABLED": self.config.webhook["message_received"]["enabled"],  # Use the value from config.json
                "URL": self.config.webhook["message_received"]["url"],  # Use the URL from config without fallback
                "RETRY_COUNT": self.config.webhook["message_received"]["retry_count"],
                "RETRY_DELAY": self.config.webhook["message_received"]["retry_delay"]
            },
            "MESSAGE_SENT": {
                "ENABLED": self.config.webhook["message_sent"]["enabled"],
                "URL": self.config.webhook["message_sent"]["url"],
                "RETRY_COUNT": self.config.webhook["message_sent"]["retry_count"],
                "RETRY_DELAY": self.config.webhook["message_sent"]["retry_delay"]
            }
        }

        # Print webhook configuration for debugging
        print(f"WebSocketService webhook config: {json.dumps(webhook_config, indent=2)}")

        # Log webhook configuration for debugging
        logger.info(f"Webhook configuration from config.json: ENABLED={self.config.webhook['enabled']}")
        logger.info(f"MESSAGE_RECEIVED: ENABLED={self.config.webhook['message_received']['enabled']}, URL={self.config.webhook['message_received']['url']}")
        logger.info(f"MESSAGE_SENT: ENABLED={self.config.webhook['message_sent']['enabled']}, URL={self.config.webhook['message_sent']['url']}")

        # Initialize webhook manager with uppercase keys
        self.webhook_manager = WebhookManager(webhook_config)

    def _is_connection_open(self) -> bool:
        """
        Safely check if the WebSocket connection is open.
        
        This method handles compatibility between different versions of the websockets library.
        In websockets 11.x+, the 'open' attribute was removed and replaced with 'closed'.
        
        Returns:
            bool: True if connection is open, False otherwise
        """
        if not self.ws_connection:
            return False
            
        try:
            # Try the new way first (websockets 11.x+)
            if hasattr(self.ws_connection, 'closed'):
                return not self.ws_connection.closed
            # Fallback to old way (websockets 10.x and earlier)
            elif hasattr(self.ws_connection, 'open'):
                return self.ws_connection.open
            else:
                # If neither attribute exists, assume connection is closed
                return False
        except Exception as e:
            logger.warning(f"Error checking connection status: {e}")
            return False

    async def get_chat_login_info(self):
        """
        Get chat login information from Shopee API.

        This method calls the mini/login endpoint to get the chat token and other login information.

        Returns:
            Tuple[Dict[str, Any], int]: Tuple with login info data and HTTP status code
        """
        try:
            # Extract CSRF token using credential manager
            csrf_token = self.session.credential_manager.get_csrf_token()

            # Log token info (partial for security)
            if csrf_token and len(csrf_token) > 8:
                logger.info(f"Using CSRF token: {csrf_token[:4]}...{csrf_token[-4:]}")
            elif csrf_token:
                logger.info("Using CSRF token (hidden for security)")

            if not csrf_token:
                logger.warning("Could not extract CSRF token from cookies")
                return {"error": "Could not extract CSRF token from cookies"}, 400

            # Build request URL and data
            login_url = "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/mini/login"
            data = {
                "csrf_token": csrf_token,
                "source": "pcmall",
                "_api_source": "pcmall"
            }

            # Save original headers
            original_headers = {}
            for key in self.session.session.headers:
                original_headers[key] = self.session.session.headers[key]

            # Set specific headers
            headers_to_set = {
                "Cookie": self.session.credential_manager.cookie,
                "Authorization": self.session.credential_manager.authorization_code,
                "User-Agent": self.session.credential_manager.user_agent,
                "Referer": "https://seller.shopee.com.my/webchat/conversations",
                "X-Requested-With": "XMLHttpRequest",
                "Accept": "application/json",
                "Content-Type": "application/x-www-form-urlencoded"
            }

            self.session.session.headers.update(headers_to_set)

            # Send POST request
            try:
                logger.info("Sending POST request to mini/login endpoint")
                response = self.session.post(login_url, data=data)
                logger.info(f"Response status code: {response.status_code}")

                if response.status_code != 200:
                    logger.warning(f"Failed to get chat login info: HTTP {response.status_code}")
                    return {"error": f"Failed to get chat login info: HTTP {response.status_code}"}, response.status_code
            finally:
                # Restore original headers
                self.session.session.headers.clear()
                self.session.session.headers.update(original_headers)

            # Parse response
            try:
                data = response.json()
                logger.info("Successfully obtained chat login info")
                return data, 200
            except Exception as e:
                logger.error(f"Error parsing chat login info response: {e}")
                return {"error": f"Error parsing response: {str(e)}"}, 500

        except Exception as e:
            logger.error(f"Error getting chat login info: {e}")
            return {"error": f"Error getting chat login info: {str(e)}"}, 500

    async def get_chat_token(self):
        """
        Get the chat token from Shopee API.

        Returns:
            str: The chat token (p_token) if successful, None otherwise
        """
        try:
            login_info, status_code = await self.get_chat_login_info()

            if status_code != 200:
                logger.warning("Failed to get chat login info")
                return None

            # Use p_token instead of token for WebSocket authentication
            p_token = login_info.get("p_token")

            if p_token:
                logger.info("Successfully obtained chat token (p_token)")
                return p_token
            else:
                logger.warning("No p_token found in login info response")
                return None

        except Exception as e:
            logger.error(f"Error getting chat token: {e}")
            return None

    async def connect_to_shopee(self):
        """
        Connect to Shopee's WebSocket server.

        Returns:
            bool: True if connection was successful, False otherwise
        """
        if self.is_connected and self.ws_connection and self._is_connection_open():
            logger.info("Already connected to Shopee WebSocket server")
            return True

        try:
            # Get WebSocket URL
            base_ws_url = self.config.urls.get("websocket")
            if not base_ws_url:
                logger.error("WebSocket URL not configured")
                return False

            # Add timestamp
            timestamp = int(time.time() * 1000)
            ws_url = f"{base_ws_url}&t={timestamp}"

            # Get authorization info
            auth_token = self.session.credential_manager.authorization_code
            cookies = self.session.credential_manager.cookie

            # Set headers
            headers = {
                "Origin": "https://shopee.com.my",
                "Upgrade": "websocket",
                "Connection": "Upgrade",
                "Cookie": cookies,
                "Authorization": auth_token
            }

            logger.info("Connecting to Shopee WebSocket server")

            # Connect to Shopee WebSocket server
            try:
                # Create a custom connection with headers
                # Convert headers dict to list of tuples for websockets 10.x compatibility
                header_list = []
                for k, v in headers.items():
                    header_list.append((k, v))

                # Create a sanitized version of headers for logging (hide sensitive content)
                sanitized_headers = []
                for k, v in headers.items():
                    if k.lower() == 'cookie':
                        sanitized_headers.append((k, '[COOKIE CONTENT HIDDEN]'))
                    elif k.lower() == 'authorization':
                        # Show only the first part of the token
                        if v and v.startswith('Bearer ') and len(v) > 15:
                            sanitized_headers.append((k, f'Bearer {v[7:15]}...'))
                        else:
                            sanitized_headers.append((k, '[AUTH TOKEN HIDDEN]'))
                    else:
                        sanitized_headers.append((k, v))

                logger.info(f"Connecting with headers: {sanitized_headers}")

                # Try different websockets connection methods
                try:
                    # Method 1: Use the correct parameter format for websockets 10.x
                    logger.info("Trying websockets connection method 1 (extra_headers as list)")
                    self.ws_connection = await websockets.connect(
                        ws_url,
                        extra_headers=header_list
                    )
                except Exception as e1:
                    logger.warning(f"Method 1 failed: {e1}")
                    try:
                        # Method 2: Try with a different parameter format
                        logger.info("Trying websockets connection method 2 (extra_headers as dict)")
                        self.ws_connection = await websockets.connect(
                            ws_url,
                            extra_headers=headers
                        )
                    except Exception as e2:
                        logger.warning(f"Method 2 failed: {e2}")
                        try:
                            # Method 3: Try without extra_headers
                            logger.info("Trying websockets connection method 3 (no extra_headers)")
                            self.ws_connection = await websockets.connect(ws_url)

                            # If this works, we'll need to send headers differently
                            logger.info("Connected without headers, will try to authenticate differently")
                        except Exception as e3:
                            logger.error(f"All connection methods failed: {e3}")
                            import traceback
                            logger.error(f"Detailed error: {traceback.format_exc()}")
                            return False

                logger.info("Successfully connected to Shopee WebSocket server")
            except Exception as e:
                logger.error(f"Failed to connect to Shopee WebSocket: {e}")
                # Print more detailed error information
                import traceback
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return False

            # Mark as connected
            self.is_connected = True
            self.reconnect_attempt = 0

            # Start message listener task
            self.ws_task = asyncio.create_task(self._listen_for_messages())

            # Wait for listener to start
            await asyncio.sleep(0.5)

            try:
                # Wait for initial handshake
                await asyncio.sleep(2)

                # Check if connection is still open
                if not self.ws_connection or not self._is_connection_open():
                    logger.warning("WebSocket connection closed after initial wait")
                    return False

                # Send login credentials
                try:
                    # Get chat token from API
                    logger.info("Getting chat token...")
                    token = await self.get_chat_token()

                    if not token:
                        logger.error("Failed to get chat token")
                        return False

                    # Get user info to get the correct account_id (uid)
                    login_info, _ = await self.get_chat_login_info()
                    user_info = login_info.get("user", {})
                    account_id = user_info.get("uid")

                    if not account_id:
                        # Fallback to shop_id if uid is not available
                        account_id = f"0-{self.config.shop_id}"
                        logger.warning(f"User uid not found, using fallback account_id: {account_id}")
                    else:
                        logger.info(f"Using account_id from user info: {account_id}")

                    # Build login data
                    login_data = {
                        "token": token,
                        "account_id": account_id,
                        "app_id": "pmminichat"
                    }

                    logger.info(f"Preparing login data with token, account_id={account_id}, app_id=pmminichat")

                    # Create login message
                    login_message = f'420["login",{json.dumps(login_data)}]'

                    # Send login message
                    await self.ws_connection.send(login_message)
                    logger.info("Login credentials sent to WebSocket server")

                    # Wait for server to process login
                    await asyncio.sleep(1)

                    # Check if connection is still open
                    if not self.ws_connection or not self._is_connection_open():
                        logger.warning("WebSocket connection closed after sending login credentials")
                        return False

                except Exception as e:
                    logger.error(f"Error sending login credentials: {e}")
                    if not self.ws_connection or not self._is_connection_open():
                        logger.warning("WebSocket connection closed during login")
                        return False

                logger.info("WebSocket connection and login completed")
            except Exception as e:
                logger.warning(f"Failed to send initialization messages: {e}")

            return True
        except Exception as e:
            self.reconnect_attempt += 1
            wait_time = min(60, self.reconnect_attempt * 5)  # Exponential backoff with max 60 seconds
            logger.error(f"Failed to connect to Shopee WebSocket: {e}. Retrying in {wait_time}s")
            return False

    async def _listen_for_messages(self):
        """Listen for messages from the Shopee WebSocket server."""
        try:
            logger.info("Started listening for messages from Shopee WebSocket")

            # Track ping/pong for connection health
            last_ping_time = time.time()
            ping_interval = self.config.websocket.get("ping_interval", 25)  # Default to 25 seconds

            while True:
                if not self.ws_connection or not self._is_connection_open():
                    logger.warning("WebSocket connection closed or not available")
                    self.is_connected = False
                    break

                # Send Socket.IO ping and heartbeat every ping_interval seconds
                current_time = time.time()
                if current_time - last_ping_time > ping_interval:
                    try:
                        # Send Socket.IO ping
                        await self.ws_connection.send('2')  # Socket.IO ping

                        # Send Shopee heartbeat message
                        heartbeat_message = '42["heartbeat","pmminichat|0|0"]'
                        await self.ws_connection.send(heartbeat_message)

                        last_ping_time = current_time
                    except Exception as e:
                        logger.warning(f"Failed to send ping/heartbeat: {e}")
                        # Connection might be broken
                        if not self.ws_connection or not self._is_connection_open():
                            logger.warning("WebSocket connection closed after sending ping/heartbeat")
                            self.is_connected = False
                            break

                try:
                    # Receive message
                    message = await asyncio.wait_for(self.ws_connection.recv(), timeout=30)
                    self.last_message_time = datetime.now()

                    # Extract message type
                    msg_type = ""
                    data_start = 0

                    if message.startswith('0{'):  # Handshake message
                        msg_type = '0'
                        data_start = 1
                    elif message.startswith('2'):  # ping
                        msg_type = '2'
                    elif message.startswith('3'):  # pong
                        msg_type = '3'
                    elif message.startswith('40'):  # Namespace connection
                        msg_type = '40'
                        data_start = 2
                    elif message.startswith('42['):  # Event message
                        msg_type = '42'
                        data_start = 2
                    elif message.startswith('430['):  # Response message
                        msg_type = '430'
                        data_start = 3
                    else:
                        # Other message types
                        for i, char in enumerate(message):
                            if not (char.isdigit()):
                                msg_type = message[:i]
                                data_start = i
                                break

                    # Process different message types
                    if msg_type == '0':  # Handshake message
                        logger.info("Received handshake message")
                        try:
                            data = json.loads(message[data_start:])
                            # Extract ping interval
                            if 'pingInterval' in data:
                                ping_interval = data['pingInterval'] / 1000  # Convert to seconds
                                logger.info(f"Setting ping interval to {ping_interval} seconds")
                        except Exception as e:
                            logger.warning(f"Failed to parse handshake data: {e}")

                    elif msg_type == '2':  # ping
                        try:
                            # Reply with pong (3)
                            await self.ws_connection.send('3')
                            # Update last ping time
                            self.last_ping_time = datetime.now()
                        except Exception as e:
                            logger.error(f"Error sending pong: {e}")
                            if not self.ws_connection or not self._is_connection_open():
                                logger.warning("WebSocket connection closed after ping")
                                self.is_connected = False

                    elif msg_type == '3':  # pong
                        # Update last pong time
                        self.last_pong_time = datetime.now()

                    elif msg_type == '40':  # Namespace connection confirmation
                        logger.info("Received namespace connection confirmation")

                    elif msg_type == '42':  # Event message
                        try:
                            # Format: 42["event_name",data]
                            event_data = json.loads(message[data_start:])
                            if len(event_data) >= 2:
                                event_name = event_data[0]
                                event_args = event_data[1]

                                logger.debug(f"Received event: {event_name}")

                                # Handle specific event types
                                if event_name == "message":
                                    # Chat message event
                                    logger.debug("Received chat message event")

                                    # Cache the message if caching is enabled
                                    if self.config.cache["enabled"] and self.config.cache.get("conversation_messages", {}).get("enabled", False):
                                        try:
                                            # Only cache actual messages (message_type="message"), not notifications
                                            message_type = event_args.get("message_type", "")
                                            if message_type == "message":
                                                # Parse the message content
                                                message_content = json.loads(event_args.get("message_content", "{}"))
                                                conversation_id = message_content.get("conversation_id")

                                                if conversation_id:
                                                    logger.debug(f"Caching message for conversation {conversation_id}")

                                                    # Get existing cached messages or create new entry
                                                    cached_data = self.cache_manager.conversation_messages.get(conversation_id)
                                                    if not cached_data:
                                                        cached_data = {"messages": []}

                                                    # Add the new message to the cache
                                                    cached_data["messages"].insert(0, message_content)  # Add at the beginning (newest first)

                                                    # Update the cache
                                                    self.cache_manager.conversation_messages.set(conversation_id, cached_data)
                                                    logger.debug(f"Message cached for conversation {conversation_id}")

                                                    # Send webhook notification based on message sender
                                                    try:
                                                        # Check if the message was sent by the user or by another party
                                                        send_by_yourself = message_content.get("send_by_yourself", False)
                                                        logger.debug(f"Message sender info - send_by_yourself: {send_by_yourself}")
                                                        logger.debug(f"Full message content: {json.dumps(message_content, indent=2)}")

                                                        if send_by_yourself:
                                                            # Message sent by the user
                                                            logger.debug(f"Sending message_sent webhook for conversation {conversation_id}")
                                                            webhook_payload = {
                                                                "message": message_content,
                                                                "conversation_id": conversation_id,
                                                                "event": "message_sent"
                                                            }
                                                            logger.debug(f"Message sent webhook payload: {json.dumps(webhook_payload, indent=2)}")
                                                            webhook_task = asyncio.create_task(self.webhook_manager.send_message_sent_webhook(webhook_payload))
                                                            # Add callback to log result
                                                            webhook_task.add_done_callback(
                                                                lambda t: logger.debug(f"Message sent webhook task completed: {t.result() if not t.exception() else t.exception()}")
                                                            )
                                                        else:
                                                            # Message received from another party
                                                            logger.debug(f"Sending message_received webhook for conversation {conversation_id}")
                                                            webhook_payload = {
                                                                "message": message_content,
                                                                "conversation_id": conversation_id,
                                                                "event": "message_received"
                                                            }
                                                            logger.debug(f"Message received webhook payload: {json.dumps(webhook_payload, indent=2)}")
                                                            webhook_task = asyncio.create_task(self.webhook_manager.send_message_received_webhook(webhook_payload))
                                                            # Add callback to log result
                                                            webhook_task.add_done_callback(
                                                                lambda t: logger.debug(f"Message received webhook task completed: {t.result() if not t.exception() else t.exception()}")
                                                            )
                                                    except Exception as e:
                                                        logger.error(f"Error sending webhook notification: {e}")
                                                        import traceback
                                                        logger.debug(f"Webhook notification error details: {traceback.format_exc()}")
                                            else:
                                                logger.debug(f"Skipping caching for non-message type: {message_type}")
                                        except Exception as e:
                                            logger.error(f"Error caching message: {e}")

                                    # Forward message
                                    await self.broadcast_message({
                                        "type": "chat_message",
                                        "event": event_name,
                                        "data": event_args,
                                        "timestamp": datetime.now().isoformat()
                                    })
                                elif event_name == "notification":
                                    # Notification event
                                    logger.debug("Received notification event")
                                    # Forward notification
                                    await self.broadcast_message({
                                        "type": "notification",
                                        "event": event_name,
                                        "data": event_args,
                                        "timestamp": datetime.now().isoformat()
                                    })
                                elif event_name == "heartbeat":
                                    # Heartbeat event
                                    # Forward to clients
                                    await self.broadcast_message({
                                        "type": "heartbeat",
                                        "event": event_name,
                                        "data": event_args,
                                        "timestamp": datetime.now().isoformat()
                                    })
                                else:
                                    # Other event types
                                    logger.debug(f"Received other event: {event_name}")
                                    # Forward event
                                    await self.broadcast_message({
                                        "type": "event",
                                        "event": event_name,
                                        "data": event_args,
                                        "timestamp": datetime.now().isoformat()
                                    })
                        except Exception as e:
                            logger.error(f"Error parsing event data: {e}")

                    elif msg_type == '430':  # Response message
                        logger.info("Received response message")
                        try:
                            # Format: 430["{\"status\": 201}"]
                            response_data = json.loads(message[data_start:])

                            # Forward response
                            await self.broadcast_message({
                                "type": "response",
                                "data": response_data,
                                "timestamp": datetime.now().isoformat()
                            })
                        except Exception as e:
                            logger.error(f"Error parsing response data: {e}")

                    else:
                        # Unknown message type
                        logger.warning(f"Unknown message type: {msg_type}")
                        # Try to extract JSON data
                        try:
                            json_start = message.find('{')
                            if json_start >= 0:
                                json_data = json.loads(message[json_start:])
                                await self.broadcast_message({
                                    "type": "unknown",
                                    "raw_type": msg_type,
                                    "data": json_data,
                                    "timestamp": datetime.now().isoformat()
                                })
                            else:
                                # If no JSON, forward raw message
                                await self.broadcast_message({
                                    "type": "unknown",
                                    "raw_type": msg_type,
                                    "raw_message": message,
                                    "timestamp": datetime.now().isoformat()
                                })
                        except Exception:
                            # Forward raw message
                            await self.broadcast_message({
                                "type": "unknown",
                                "raw_message": message,
                                "timestamp": datetime.now().isoformat()
                            })
                except websockets.exceptions.ConnectionClosed as e:
                    logger.warning(f"WebSocket connection closed: {e}")
                    self.is_connected = False
                    break
                except Exception as e:
                    logger.error(f"Error receiving message: {e}")
                    # Continue listening, don't break the loop

            logger.info("Stopped listening for messages from Shopee WebSocket")
        except Exception as e:
            logger.error(f"Error in message listener: {e}")
        finally:
            self.is_connected = False

    async def reconnect_after_config_update(self):
        """
        Reconnect to Shopee's WebSocket server after a config update.

        This method is called when the config.json file is updated.
        It closes the existing connection and reconnects with the new credentials.
        """
        logger.info("Reconnecting WebSocket after config update")

        # Close existing connection
        if self.ws_connection:
            try:
                await self.ws_connection.close()
            except Exception as e:
                logger.error(f"Error closing WebSocket connection: {e}")
            self.ws_connection = None

        # Cancel existing task
        if self.ws_task and not self.ws_task.done():
            self.ws_task.cancel()
            try:
                await self.ws_task
            except asyncio.CancelledError:
                pass

        # Mark as disconnected
        self.is_connected = False

        # Reset reconnect attempt counter
        self.reconnect_attempt = 0

        # Try to reconnect
        success = await self.connect_to_shopee()

        if success:
            logger.info("Successfully reconnected WebSocket after config update")
            # Broadcast reconnection status to all clients
            await self.broadcast_message({
                "type": "status_update",
                "message": "WebSocket reconnected after config update",
                "connected_to_shopee": True,
                "timestamp": datetime.now().isoformat()
            })
        else:
            logger.warning("Failed to reconnect WebSocket after config update")
            # Broadcast reconnection status to all clients
            await self.broadcast_message({
                "type": "status_update",
                "message": "Failed to reconnect WebSocket after config update",
                "connected_to_shopee": False,
                "timestamp": datetime.now().isoformat()
            })

        return success

    async def maintain_connection(self):
        """
        Maintain connection to Shopee's WebSocket server.

        This method runs in a loop and ensures the connection is maintained.
        """
        logger.info("Starting WebSocket connection maintenance task")

        # Track initial state
        connection_attempts = 0
        last_status_log = datetime.now()

        while True:
            try:
                # Log connection status once per minute
                now = datetime.now()
                if (now - last_status_log).total_seconds() > 60:
                    last_status_log = now

                # Check connection status
                connection_active = (self.is_connected and
                                    self.ws_connection and
                                    self._is_connection_open())

                # Check if we need to reconnect after config update
                if self.needs_reconnect_after_config_update:
                    logger.info("Config update detected, reconnecting WebSocket...")
                    # Reset the flag
                    self.needs_reconnect_after_config_update = False

                    # Force reconnection
                    if connection_active:
                        # Close existing connection
                        if self.ws_connection:
                            try:
                                await self.ws_connection.close()
                            except:
                                pass
                            self.ws_connection = None

                        # Cancel existing task
                        if self.ws_task and not self.ws_task.done():
                            self.ws_task.cancel()
                            try:
                                await self.ws_task
                            except asyncio.CancelledError:
                                pass

                        # Mark as disconnected
                        self.is_connected = False
                        connection_active = False

                        # Reset reconnect attempt counter
                        self.reconnect_attempt = 0

                        # Broadcast reconnection status to all clients
                        await self.broadcast_message({
                            "type": "status_update",
                            "message": "WebSocket reconnecting after config update",
                            "connected_to_shopee": False,
                            "timestamp": datetime.now().isoformat()
                        })

                if not connection_active:
                    # Cancel task if still running
                    if self.ws_task and not self.ws_task.done():
                        self.ws_task.cancel()
                        try:
                            await self.ws_task
                        except asyncio.CancelledError:
                            pass

                    # Close connection if it exists
                    if self.ws_connection:
                        try:
                            await self.ws_connection.close()
                        except:
                            pass
                        self.ws_connection = None

                    # Try to reconnect
                    logger.info(f"WebSocket not connected. Attempting to connect (attempt {connection_attempts + 1})...")
                    connection_attempts += 1
                    success = await self.connect_to_shopee()

                    if success:
                        logger.info(f"Successfully connected to Shopee WebSocket after {connection_attempts} attempts")
                        connection_attempts = 0
                    else:
                        logger.warning(f"Failed to connect to Shopee WebSocket (attempt {connection_attempts})")
                else:
                    # Reset connection attempt counter
                    connection_attempts = 0

                # Check if no messages received for a long time
                if (self.last_message_time and
                    (datetime.now() - self.last_message_time).total_seconds() > 300):  # 5 minutes
                    logger.warning("No messages received for 5 minutes, reconnecting...")

                    # Close existing connection
                    if self.ws_connection:
                        try:
                            await self.ws_connection.close()
                        except:
                            pass
                        self.ws_connection = None

                    # Cancel existing task
                    if self.ws_task and not self.ws_task.done():
                        self.ws_task.cancel()
                        try:
                            await self.ws_task
                        except asyncio.CancelledError:
                            pass

                    self.is_connected = False
                    continue

                # Check if connection is still active
                if connection_active:
                    if not self.ws_connection or not self._is_connection_open():
                        logger.warning("WebSocket connection is no longer open")
                        self.is_connected = False

                # Wait before next check
                interval = self.config.websocket["reconnect_interval"]
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"Error maintaining WebSocket connection: {e}")
                await asyncio.sleep(5)  # Wait 5 seconds after error

    async def register_client(self, client_id: str, websocket: WebSocket):
        """
        Register a new client connection.

        Args:
            client_id: Unique identifier for the client
            websocket: WebSocket connection for the client
        """
        # Check if we've reached the maximum number of clients
        if len(self.connected_clients) >= self.config.websocket["client_max_size"]:
            logger.warning(f"Maximum number of clients reached ({self.config.websocket['client_max_size']})")
            await websocket.close(code=1008, reason="Maximum number of clients reached")
            return

        # Add client to connected clients
        self.connected_clients[client_id] = websocket
        logger.info(f"Client {client_id} connected. Total clients: {len(self.connected_clients)}")

    async def unregister_client(self, client_id: str):
        """
        Unregister a client connection.

        Args:
            client_id: Unique identifier for the client
        """
        if client_id in self.connected_clients:
            del self.connected_clients[client_id]
            logger.info(f"Client {client_id} disconnected. Total clients: {len(self.connected_clients)}")

    def is_caching_enabled(self) -> bool:
        """
        Check if message caching is enabled and WebSocket is connected.

        Returns:
            bool: True if caching is enabled and WebSocket is connected, False otherwise
        """
        # Check if caching is enabled in config
        cache_enabled = self.config.cache.get("enabled", False)
        messages_cache_enabled = self.config.cache.get("conversation_messages", {}).get("enabled", False)
        websocket_only = self.config.cache.get("conversation_messages", {}).get("websocket_only", True)

        # If websocket_only is True, only cache when WebSocket is connected
        if websocket_only and not self.is_connected:
            return False

        return cache_enabled and messages_cache_enabled

    def get_cached_messages(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get cached messages for a conversation.

        Args:
            conversation_id: Conversation ID to get messages for

        Returns:
            Optional[Dict[str, Any]]: Cached messages or None if not found
        """
        if not self.is_caching_enabled():
            return None

        return self.cache_manager.conversation_messages.get(conversation_id)

    async def close(self):
        """
        Close the WebSocket connection and clean up resources.
        """
        logger.info("Closing WebSocket connection and cleaning up resources")

        # 取消监听任务
        if self.ws_task and not self.ws_task.done():
            logger.debug("Cancelling WebSocket listener task")
            self.ws_task.cancel()
            try:
                await self.ws_task
            except asyncio.CancelledError:
                pass

        # 关闭WebSocket连接
        if self.ws_connection and self._is_connection_open():
            logger.debug("Closing WebSocket connection")
            try:
                await self.ws_connection.close()
            except Exception as e:
                logger.warning(f"Error closing WebSocket connection: {e}")

        # 关闭所有客户端连接
        for client_id, websocket in list(self.connected_clients.items()):
            logger.debug(f"Closing client connection: {client_id}")
            try:
                await websocket.close()
            except Exception as e:
                logger.warning(f"Error closing client connection {client_id}: {e}")

        # 关闭webhook管理器
        try:
            await self.webhook_manager.close()
        except Exception as e:
            logger.warning(f"Error closing webhook manager: {e}")

        self.connected_clients.clear()
        self.is_connected = False
        self.ws_connection = None
        logger.info("WebSocket resources cleaned up")

    async def broadcast_message(self, message: Any):
        """
        Broadcast a message to all connected clients.

        Args:
            message: Message to broadcast
        """
        if not self.connected_clients:
            return

        # Convert message to JSON if it's not already a string
        if not isinstance(message, str):
            try:
                message = json.dumps(message)
            except Exception as e:
                logger.error(f"Failed to serialize message: {e}")
                return

        # Send message to all connected clients
        disconnected_clients = []
        for client_id, websocket in self.connected_clients.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Failed to send message to client {client_id}: {e}")
                disconnected_clients.append(client_id)

        # Remove disconnected clients
        for client_id in disconnected_clients:
            await self.unregister_client(client_id)

    async def handle_client(self, client_id: str, websocket: WebSocket):
        """
        Handle a client connection.

        Args:
            client_id: Unique identifier for the client
            websocket: WebSocket connection for the client
        """
        logger.info(f"New client connection: {client_id}")

        # 发送欢迎消息
        try:
            welcome_message = {
                "type": "connection_established",
                "client_id": client_id,
                "timestamp": time.time(),
                "server_time": datetime.now().isoformat(),
                "websocket_status": {
                    "connected_to_shopee": self.is_connected,
                    "client_count": len(self.connected_clients)
                }
            }
            await websocket.send_json(welcome_message)
            logger.debug(f"Sent welcome message to client {client_id}")
        except Exception as e:
            logger.error(f"Error sending welcome message to client {client_id}: {e}")

        await self.register_client(client_id, websocket)

        try:
            # 如果连接到 Shopee 的 WebSocket 尚未建立，尝试连接
            if not self.is_connected:
                logger.info(f"Client {client_id} connected but not connected to Shopee yet. Attempting to connect...")
                await self.connect_to_shopee()

            # 发送当前状态信息
            status_message = {
                "type": "status_update",
                "connected_to_shopee": self.is_connected,
                "last_message_time": self.last_message_time.isoformat() if self.last_message_time else None,
                "client_count": len(self.connected_clients),
                "timestamp": time.time()
            }
            await websocket.send_json(status_message)

            # 保持连接并处理客户端消息
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                logger.debug(f"Received message from client {client_id}: {data}")

                # 尝试解析消息
                try:
                    message = json.loads(data)

                    # 处理特殊命令
                    if message.get("command") == "status":
                        # 发送状态更新
                        await websocket.send_json({
                            "type": "status_response",
                            "connected_to_shopee": self.is_connected,
                            "last_message_time": self.last_message_time.isoformat() if self.last_message_time else None,
                            "client_count": len(self.connected_clients),
                            "timestamp": time.time()
                        })
                    elif message.get("command") == "reconnect":
                        # 尝试重新连接到 Shopee
                        logger.info(f"Client {client_id} requested reconnection to Shopee")

                        # 关闭现有连接
                        if self.ws_connection:
                            try:
                                await self.ws_connection.close()
                                self.ws_connection = None
                            except:
                                pass

                        # 取消现有任务
                        if self.ws_task and not self.ws_task.done():
                            try:
                                self.ws_task.cancel()
                                await asyncio.sleep(0.1)  # 给任务一点时间取消
                            except:
                                pass

                        # 重置连接状态
                        self.is_connected = False

                        # 重新连接
                        success = await self.connect_to_shopee()
                        await websocket.send_json({
                            "type": "reconnect_response",
                            "success": success,
                            "connected": self.is_connected,
                            "timestamp": time.time()
                        })
                except json.JSONDecodeError:
                    # 不是 JSON 格式，忽略
                    pass
                except Exception as e:
                    logger.error(f"Error processing message from client {client_id}: {e}")

        except WebSocketDisconnect:
            logger.info(f"Client {client_id} disconnected")
            await self.unregister_client(client_id)
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
            import traceback
            logger.debug(f"Client handling error details: {traceback.format_exc()}")
            await self.unregister_client(client_id)
