import uuid
from typing import List, Dict, Any
from utils.session import session
import config
import time

def generate_request_id():
    return str(uuid.uuid4())

def get_initial_order_list(order_status: str, order_sn: str = None) -> Dict[str, Any]:
    base_filter = {
        "fulfillment_type": 0,
        "is_drop_off": 0,
        "fulfillment_source": 0,
        "prescription_filter": 0
    }
    
    if order_status == "to_ship":
        order_list_tab = config.TO_SHIP_TAB
        filter_params = {**base_filter, "order_to_ship_status": 1}
        sort_params = {"sort_type": 2, "ascending": True}
    elif order_status == "shipped":
        order_list_tab = config.SHIPPED_TAB
        filter_params = base_filter
        sort_params = {"sort_type": 3, "ascending": False}
    elif order_status == "completed":
        order_list_tab = config.COMPLETED_TAB
        filter_params = base_filter
        sort_params = {"sort_type": 3, "ascending": False}
    elif order_status == "all":
        order_list_tab = config.ALL_ORDERS_TAB
        filter_params = base_filter
        sort_params = {"sort_type": 3, "ascending": False}
    else:
        raise ValueError("Invalid order status")

    initial_payload = {
        "order_list_tab": order_list_tab,
        "entity_type": 1,
        "pagination": {"from_page_number": 1, "page_number": 1, "page_size": config.PAGE_SIZE},
        "filter": filter_params,
        "sort": sort_params
    }
    
    if order_sn:
        initial_payload["search_param"] = {"keyword": order_sn, "category": 1}

    try:
        initial_response = session.post(config.INITIAL_ORDER_LIST_URL, params=get_common_params(), json=initial_payload)
        initial_response.raise_for_status() 
        return initial_response.json()
    except Exception as e:
        print(f"Error fetching initial order list: {str(e)}")
        return {"error": str(e)}

def get_order_details(order_ids: List[int], order_status: str) -> Dict[str, Any]:
    # Set a very small batch size to avoid response size issues
    BATCH_SIZE = 1  # Reduced from 50 to 10
    
    if order_status == "to_ship":
        order_list_tab = config.TO_SHIP_TAB
    elif order_status == "shipped":
        order_list_tab = config.SHIPPED_TAB
    elif order_status == "completed":
        order_list_tab = config.COMPLETED_TAB
    elif order_status == "all":
        order_list_tab = config.ALL_ORDERS_TAB
    else:
        raise ValueError("Invalid order status")

    all_results = []
    for i in range(0, len(order_ids), BATCH_SIZE):
        batch_order_ids = order_ids[i:i + BATCH_SIZE]
        
        details_payload = {
            "order_list_tab": order_list_tab,
            "order_param_list": [
                {"order_id": order_id, "shop_id": config.SHOP_ID, "region_id": config.REGION_ID}
                for order_id in batch_order_ids
            ]
        }

        try:
            details_response = session.post(config.ORDER_DETAILS_URL, params=get_common_params(), json=details_payload)
            details_response.raise_for_status()
            response_data = details_response.json()
            print(response_data)
            if 'data' in response_data and 'card_list' in response_data['data']:
                all_results.extend(response_data['data']['card_list'])
            
            # Add a small delay between requests to avoid rate limiting
            time.sleep(0.5)
            
        except Exception as e:
            print(f"Error processing batch {i//BATCH_SIZE + 1}: {str(e)}")
            continue

    return {'data': {'card_list': all_results}}

def get_common_params() -> Dict[str, str]:
    return {
        "SPC_CDS": config.extract_spc_cds(config.COOKIE),
        "SPC_CDS_VER": config.extract_spc_cds_ver(config.COOKIE)
    }

def process_orders(initial_data: Dict[str, Any], order_status: str, status_filter: str = None) -> Dict[str, Any]:
    if not isinstance(initial_data, dict) or 'error' in initial_data:
        print(f"Error in initial data: {initial_data}")
        return {"data": {"card_list": []}, "error": "Invalid Initial Data"}
        
    if 'data' not in initial_data or not initial_data.get('data', {}).get('index_list'):
        return {"data": {"card_list": []}}

    order_ids = [order['order_id'] for order in initial_data['data']['index_list']]
    details_data = get_order_details(order_ids, order_status)

    if not details_data.get('data', {}).get('card_list'):
        return {"data": {"card_list": []}}

    if status_filter and order_status != "all":
        filtered_orders = [
            order for order in details_data['data']['card_list']
            if (order_status == "to_ship" and order['package_card']['status_info']['status'] == status_filter) or
               (order_status != "to_ship" and order['order_card']['status_info']['status'] == status_filter)
        ]
        return {"data": {"card_list": filtered_orders}}
    
    return details_data