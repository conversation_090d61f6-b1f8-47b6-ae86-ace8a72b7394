{% extends "base.html" %}

{% block title %}Steam Settings{% endblock %}
{% block header %}Steam Settings{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="steamConfigData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Main content -->
        <div class="flex-1">
            <!-- Advanced Settings Section -->
            <div class="section-content">
                <h2 class="text-2xl font-bold mb-4">Steam Advanced Settings</h2>
                
                <div class="mb-4 config-item">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="mb-6 flex items-center">
                            <input type="checkbox" 
                                   id="send_chat_on_auth_success" 
                                   x-model="config.SEND_CHAT_ON_AUTH_SUCCESS"
                                   class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            <label for="send_chat_on_auth_success" 
                                   class="ml-2 block text-sm font-medium text-gray-700">
                                Send Chat on Auth Code Success
                            </label>
                        </div>

                        <div class="mb-6 flex items-center">
                            <input type="checkbox" 
                                   id="auto_ship_order" 
                                   x-model="config.AUTO_SHIP_ORDER"
                                   class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            <label for="auto_ship_order" 
                                   class="ml-2 block text-sm font-medium text-gray-700">
                                Automatically Ship Order Before Showing Auth Code
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function steamConfigData() {
        return {
            config: {},
            isLoaded: false,
            init() {
                this.loadConfig();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            saveConfig() {
                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    this.animateSaveButton();
                })
                .catch((error) => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the configuration.');
                });
            },
            animateInitialLoad() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}