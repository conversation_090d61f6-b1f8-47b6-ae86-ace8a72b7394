{% extends "base.html" %}

{% block title %}Auto Reply Settings{% endblock %}
{% block header %}Auto Reply Settings{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="autoReplyData()" x-init="init()">
    <div class="bg-white shadow rounded-lg p-6" x-show="isLoaded">
        <!-- Online/Offline Toggle -->
        <div class="mb-6">
            <div class="flex items-center">
                <button 
                    @click="toggleStatus"
                    :class="{'bg-green-500': config.AUTO_REPLY_ENABLED, 'bg-gray-300': !config.AUTO_REPLY_ENABLED}"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out">
                    <span 
                        :class="{'translate-x-5': config.AUTO_REPLY_ENABLED, 'translate-x-0': !config.AUTO_REPLY_ENABLED}"
                        class="inline-block h-5 w-5 transform rounded-full bg-white shadow transition duration-200 ease-in-out">
                    </span>
                </button>
                <span class="ml-3 text-sm font-medium text-gray-900">
                    <span x-text="config.AUTO_REPLY_ENABLED ? 'Offline (Auto Reply Enabled)' : 'Online'"></span>
                </span>
            </div>
        </div>

        <!-- Auto Reply Settings -->
        <div x-show="config.AUTO_REPLY_ENABLED">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Auto Reply Message</label>
                <textarea 
                    x-model="config.AUTO_REPLY_MESSAGE"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    rows="4">
                </textarea>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Reply Delay (minutes)</label>
                <input 
                    type="number" 
                    x-model.number="config.AUTO_REPLY_DELAY_MINUTES"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-sm text-gray-500">Wait this long after customer's message before sending auto-reply</p>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Cooldown Period (minutes)</label>
                <input 
                    type="number" 
                    x-model.number="config.AUTO_REPLY_COOLDOWN_MINUTES"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-sm text-gray-500">Minimum time between auto-replies to the same customer</p>
            </div>
        </div>

        <!-- Save Button -->
        <div class="mt-4">
            <button 
                @click="saveConfig"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Settings
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function autoReplyData() {
    return {
        config: {},
        isLoaded: false,
        init() {
            this.loadConfig();
        },
        loadConfig() {
            fetch('/admin/get_config')
                .then(response => response.json())
                .then(data => {
                    this.config = data;
                    this.isLoaded = true;
                })
                .catch(error => {
                    console.error('Error loading config:', error);
                    alert('Failed to load configuration');
                });
        },
        toggleStatus() {
            this.config.AUTO_REPLY_ENABLED = !this.config.AUTO_REPLY_ENABLED;
        },
        saveConfig() {
            fetch('/admin/update_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.config),
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to save configuration');
            });
        }
    }
}
</script>
{% endblock %}