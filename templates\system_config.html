{% extends "base.html" %}

{% block title %}System Configuration{% endblock %}
{% block header %}System Configuration{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" x-data="configData()" x-init="init()">
    <div class="flex" x-show="isLoaded">
        <!-- Sidebar -->
        <div class="w-64 mr-8">
            <nav class="space-y-1">
                <template
                    x-for="(section, index) in ['General', 'API', 'Notification', 'Performance', 'Advanced', 'Auto Chat', 'Self Redeem Text SKUs', 'Manual Orders', 'Netflix Accounts']"
                    :key="index">
                    <a href="#" @click.prevent="currentSection = section; animateSection()"
                        :class="{'bg-gray-100 text-gray-900': currentSection === section, 'text-gray-600 hover:bg-gray-50 hover:text-gray-900': currentSection !== section}"
                        class="group flex items-center px-3 py-2 text-sm font-medium rounded-md sidebar-item">
                        <span x-text="section"></span>
                    </a>
                </template>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1">
            <div x-show="currentSection === 'General'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">General Settings</h2>
                <div class="mb-4 config-item">
                    <label for="api_key" class="block text-sm font-medium text-gray-700">API Key</label>
                    <input id="api_key" name="api_key" type="text" x-model="config.API_KEY"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="mb-4 config-item">
                    <label for="shop_id" class="block text-sm font-medium text-gray-700">Shop ID</label>
                    <input id="shop_id" name="shop_id" type="number" x-model="config.SHOP_ID"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <!-- 新增的时区配置项 -->
                <div class="mb-4 config-item">
                    <label for="time_zone" class="block text-sm font-medium text-gray-700">Time Zone</label>
                    <select id="time_zone" name="time_zone" x-model="config.TIME_ZONE"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="Asia/Kuala_Lumpur">Asia/Kuala_Lumpur</option>
                        <option value="Asia/Singapore">Asia/Singapore</option>
                        <option value="Asia/Jakarta">Asia/Jakarta</option>
                        <option value="Asia/Bangkok">Asia/Bangkok</option>
                        <option value="Asia/Manila">Asia/Manila</option>
                        <option value="Asia/Tokyo">Asia/Tokyo</option>
                        <option value="Europe/Tirane">Europe/Tirane</option>
                        <option value="UTC">UTC</option>
                    </select>
                </div>
            </div>

            <div x-show="currentSection === 'API'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">API Settings</h2>
                <div class="mb-4 config-item">
                    <label for="cookie" class="block text-sm font-medium text-gray-700">Cookie</label>
                    <textarea id="cookie" name="cookie" x-model="config.COOKIE"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        rows="4"></textarea>
                </div>
                <div class="mb-4 config-item">
                    <label for="authorization_code" class="block text-sm font-medium text-gray-700">Authorization
                        Code</label>
                    <input id="authorization_code" name="authorization_code" type="text"
                        x-model="config.AUTHORIZATION_CODE"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <!-- Payment Gateway Settings -->
                <div class="mt-6">
                    <h3 class="text-xl font-semibold mb-4">Payment Gateway Settings</h3>
                    
                    <!-- Curlec Settings -->
                    <div class="mb-4 config-item">
                        <label class="block text-sm font-medium text-gray-700">Curlec API Credentials</label>
                        <div class="mt-2 space-y-2">
                            <input type="text" 
                                   x-model="config.CURLEC_API_KEY" 
                                   placeholder="Curlec API Key"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <input type="text" 
                                   x-model="config.CURLEC_SECRET_KEY" 
                                   placeholder="Curlec Secret Key"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                    </div>
                </div>
            </div>

            <div x-show="currentSection === 'Notification'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Notification Settings</h2>
                <div class="mb-4 config-item">
                    <label for="notification_email" class="block text-sm font-medium text-gray-700">Notification
                        Email</label>
                    <input id="notification_email" name="notification_email" type="email"
                        x-model="config.NOTIFICATION_EMAIL.address"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="mb-4 config-item">
                    <label for="notification_app_password" class="block text-sm font-medium text-gray-700">Notification
                        App Password</label>
                    <input id="notification_app_password" name="notification_app_password" type="password"
                        x-model="config.NOTIFICATION_EMAIL.app_password"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
            </div>

            <div x-show="currentSection === 'Performance'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Performance Settings</h2>
                <div class="mb-4 config-item">
                    <label for="request_timeout" class="block text-sm font-medium text-gray-700">Request Timeout
                        (seconds)</label>
                    <input id="request_timeout" name="request_timeout" type="number" x-model="config.REQUEST_TIMEOUT"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="mb-4 config-item">
                    <label for="session_cooldown_time" class="block text-sm font-medium text-gray-700">Session Cooldown
                        Time (seconds)</label>
                    <input id="session_cooldown_time" name="session_cooldown_time" type="number"
                        x-model="config.SESSION_COOLDOWN_TIME"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
            </div>

            <div x-show="currentSection === 'Advanced'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Advanced Settings</h2>
                <div class="mb-4 config-item flex items-center">
                    <input type="checkbox" id="send_chat_on_auth_success" x-model="config.SEND_CHAT_ON_AUTH_SUCCESS"
                        class="mr-2">
                    <label for="send_chat_on_auth_success" class="text-sm font-medium text-gray-700">Send Chat on Auth
                        Code Success</label>
                </div>
                <div class="mb-4 config-item flex items-center">
                    <input type="checkbox" id="auto_ship_order" x-model="config.AUTO_SHIP_ORDER" class="mr-2">
                    <label for="auto_ship_order" class="text-sm font-medium text-gray-700">Automatically Ship Order
                        Before Showing Auth Code</label>
                </div>
            </div>

            <div x-show="currentSection === 'Auto Chat'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Auto Chat Settings</h2>
                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Auto Chat VAR_SKUs</label>
                    <div class="mt-2">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        VAR_SKU</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Custom Message</th>
                                    <th
                                        class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(sku, index) in config.AUTO_REDEEM_VAR_SKUS" :key="index">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" x-model="sku.sku"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <textarea x-model="sku.message"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                rows="3" placeholder="Leave blank to use global message"></textarea>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click.prevent="removeVarSKU(index)"
                                                class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <button @click.prevent="addVarSKU"
                            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add VAR_SKU
                        </button>
                    </div>
                </div>
                <div class="mb-4 config-item">
                    <label for="auto_redeem_message" class="block text-sm font-medium text-gray-700">Global Auto Chat
                        Message</label>
                    <textarea id="auto_redeem_message" name="auto_redeem_message" x-model="config.AUTO_REDEEM_MESSAGE"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        rows="10" style="white-space: pre-wrap;"></textarea>
                    <div class="mt-2 text-sm text-gray-600">
                        <p>Available variables for message customization:</p>
                        <ul class="list-disc pl-5 mt-2">
                            <li>{order_sn} - Order serial number</li>
                            <li>{buyer_username} - Buyer's username</li>
                            <li>{item_name} - Name of the item</li>
                            <li>{item_price} - Price of the item</li>
                            <li>{buyer_name} - Buyer's name</li>
                            <li>{buyer_phone} - Buyer's phone number</li>
                            <li>{create_time} - Order creation time</li>
                            <li>{shipping_address} - Shipping address</li>
                            <li>{item_sku} - Item SKU</li>
                            <li>{item_quantity} - Quantity of items</li>
                            <li>{payment_method} - Payment method</li>
                            <li>{shop_name} - Shop name</li>
                            <li>{escrow_release_time} - Escrow release time</li>
                            <li>{buyer_rating} - Buyer's rating</li>
                            <li>{order_status} - Order status</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Auto Redeem Text SKUs Section -->
            <div x-show="currentSection === 'Self Redeem Text SKUs'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Self Redeem Text SKUs Management</h2>

                <!-- Add New SKU -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Add New SKU</h3>
                    <div class="config-item">
                        <label for="new_sku" class="block text-sm font-medium text-gray-700">SKU</label>
                        <input id="new_sku" name="new_sku" type="text" x-model="newTextSku.sku"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div class="config-item mt-4">
                        <label for="new_message" class="block text-sm font-medium text-gray-700">Redeem Message
                            Template</label>
                        <textarea id="new_message" name="new_message" x-model="newTextSku.message"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            rows="3" placeholder="Use {result} to include the stock item"></textarea>
                    </div>
                    <div class="config-item mt-4 flex items-center">
                        <input type="checkbox" id="new_is_unlimited" x-model="newTextSku.is_unlimited_stock"
                            class="mr-2">
                        <label for="new_is_unlimited" class="text-sm font-medium text-gray-700">Is Unlimited
                            Stock?</label>
                    </div>
                    <button @click="addTextSku"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Add SKU
                    </button>
                </div>

                <!-- Existing SKUs -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Existing SKUs</h3>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                    SKU</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                                    Redeem Message Template</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                                    ♾️ Stock</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(sku, index) in config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" x-model="sku.sku"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <textarea x-model="sku.message"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            rows="2" placeholder="Use {result} to include the stock item"></textarea>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" x-model="sku.is_unlimited_stock" class="mr-2">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click.prevent="deleteTextSku(index)"
                                            class="text-red-600 hover:text-red-900">Delete</button>
                                        <button x-show="!sku.is_unlimited_stock" @click="selectTextSku(sku)"
                                            class="ml-2 text-blue-600 hover:text-blue-900">Manage Stock</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- Manage Stock for Selected SKU -->
                <div class="mb-6" x-show="selectedTextSku !== null && !selectedTextSku.is_unlimited_stock">
                    <h3 class="text-xl font-semibold mb-2">Manage Stock for SKU: <span
                            x-text="selectedTextSku.sku"></span></h3>

                    <div>
                        <div class="mb-4">
                            <label for="new_stock_item" class="block text-sm font-medium text-gray-700">Add New Stock
                                Items</label>
                            <textarea id="new_stock_item" name="new_stock_item" x-model="newStockItem"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                rows="5" placeholder="Enter one item per line"></textarea>
                            <button @click="addStockItems"
                                class="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Add Stock Items
                            </button>
                        </div>

                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stock Item</th>
                                    <th
                                        class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(stock, sIndex) in getStockItems(selectedTextSku.sku)" :key="sIndex">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" x-model="stock"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click.prevent="deleteStockItem(selectedTextSku.sku, sIndex)"
                                                class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>

                    <button @click="selectedTextSku = null"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        Close
                    </button>
                </div>

                <!-- Add the new SHIP_SUCCESS_MESSAGE_TEMPLATE configuration item -->
                <div class="mb-4 config-item">
                    <label for="ship_success_message" class="block text-sm font-medium text-gray-700">Ship Success
                        Message Template</label>
                    <textarea id="ship_success_message" name="ship_success_message"
                        x-model="config.SHIP_SUCCESS_MESSAGE_TEMPLATE"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        rows="3"></textarea>
                    <p class="mt-2 text-sm text-gray-600">
                        Available variables: {buyer_username}, {order_sn}
                    </p>
                </div>
            </div>

            <!-- 在主内容区域添加新的部分 -->
            <div x-show="currentSection === 'Manual Orders'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Manual Order Management</h2>

                <!-- 创建手动订单的表单 -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Create Manual Order</h3>
                    <div class="config-item">
                        <label for="manual_order_sn" class="block text-sm font-medium text-gray-700">Order SN</label>
                        <input id="manual_order_sn" name="manual_order_sn" type="text" x-model="manualOrder.order_sn"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div class="config-item mt-4">
                        <label for="manual_var_sku" class="block text-sm font-medium text-gray-700">VAR SKU</label>
                        <input id="manual_var_sku" name="manual_var_sku" type="text" x-model="manualOrder.var_sku"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <button @click="createManualOrder"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Create Manual Order
                    </button>
                </div>

                <!-- 显示手动创建的订单列表 -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold mb-2">Manual Orders</h3>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Order SN</th>
                                <th
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    VAR SKU</th>
                                <th
                                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(order, index) in manualOrders" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.order_sn"></td>
                                    <td class="px-6 py-4 whitespace-nowrap" x-text="order.var_sku"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <button @click.prevent="deleteManualOrder(index)"
                                            class="text-red-600 hover:text-red-900">Delete</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <div x-show="currentSection === 'Netflix Accounts'" class="section-content">
                <h2 class="text-2xl font-bold mb-4">Netflix Accounts</h2>
                <!-- 添加 Netflix Account Limit 设置 -->
                <div class="mb-4 config-item">
                    <label for="netflix_account_limit" class="block text-sm font-medium text-gray-700">Netflix Account
                        Limit</label>
                    <input type="number" id="netflix_account_limit" x-model="config.NETFLIX_ACCOUNT_LIMIT"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <p class="mt-1 text-sm text-gray-500">Maximum number of users allowed per Netflix account.</p>
                </div>
                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Accounts (Email Only)</label>
                    <div class="mt-2">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Email</th>
                                    <th
                                        class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(email, index) in config.NETFLIX_ACCOUNTS" :key="index">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="email" :value="email"
                                                @input="updateNetflixEmail(index, $event.target.value)"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click.prevent="removeNetflixAccount(index)"
                                                class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <button @click.prevent="addNetflixAccount"
                            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Netflix Account
                        </button>
                    </div>
                </div>
                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Sign-in Code Email</label>
                    <div class="mt-2">
                        <input type="email" x-model="config.NETFLIX_SIGNIN_EMAIL.email"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Email for receiving Netflix sign-in codes">
                    </div>
                </div>
                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Sign-in Code App Password</label>
                    <div class="mt-2">
                        <input type="password" x-model="config.NETFLIX_SIGNIN_EMAIL.app_password"
                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="App password for the sign-in code email">
                    </div>
                </div>
                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Session Cooldown Time
                        (seconds)</label>
                    <input type="number" x-model="config.NETFLIX_SESSION_COOLDOWN_TIME"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>

                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Request Timeout (seconds)</label>
                    <input type="number" x-model="config.NETFLIX_REQUEST_TIMEOUT"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>

                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Max Retries</label>
                    <input type="number" x-model="config.NETFLIX_MAX_RETRIES"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>

                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Retry Delay (seconds)</label>
                    <input type="number" x-model="config.NETFLIX_RETRY_DELAY"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>

                <div class="mb-4 config-item">
                    <label class="block text-sm font-medium text-gray-700">Netflix Redeem Periods</label>
                    <div class="mt-2">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        SKU</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Period (days)</th>
                                    <th
                                        class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="(period, sku) in config.NETFLIX_REDEEM_PERIODS" :key="sku">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="text" x-model="sku"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="number" x-model="config.NETFLIX_REDEEM_PERIODS[sku]"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <button @click.prevent="delete config.NETFLIX_REDEEM_PERIODS[sku]"
                                                class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <button @click.prevent="addNetflixRedeemPeriod"
                            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add Netflix Redeem Period
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <button @click="saveConfig"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 save-button">
                    Save Configuration
                </button>
            </div>
        </div>
    </div>
    <div x-show="!isLoaded" class="text-center py-8">
        <p class="text-lg">Loading configuration...</p>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    function configData() {
        return {
            config: {
                TIME_ZONE: 'Asia/Kuala_Lumpur',
            },
            autoRedeemSKUs: '',
            currentSection: 'General',
            emailChanges: {},
            isLoaded: false,
            newTextSku: {
                sku: '',
                message: '',
                is_unlimited_stock: false
            },
            selectedTextSku: null,
            newStockItem: '',
            init() {
                this.loadConfig();
            },
            loadConfig() {
                fetch('/admin/get_config')
                    .then(response => response.json())
                    .then(data => {
                        this.config = data;
                        if (!this.config.NETFLIX_ACCOUNTS) {
                            this.config.NETFLIX_ACCOUNTS = [];
                        }
                        // 存储初始值
                        this.initialNetflixAccounts = [...this.config.NETFLIX_ACCOUNTS];
                        this.isLoaded = true;
                        this.animateInitialLoad();
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                        alert('Failed to load configuration. Please try refreshing the page.');
                    });
            },
            updateNetflixEmail(index, newEmail) {
                const initialEmail = this.initialNetflixAccounts[index];
                const currentEmail = this.config.NETFLIX_ACCOUNTS[index];

                if (initialEmail !== newEmail) {
                    this.emailChanges[initialEmail] = newEmail;
                } else {
                    delete this.emailChanges[initialEmail];
                }

                this.config.NETFLIX_ACCOUNTS[index] = newEmail;
            },
            addNetflixRedeemPeriod() {
                if (!this.config.NETFLIX_REDEEM_PERIODS) {
                    this.config.NETFLIX_REDEEM_PERIODS = {};
                }
                const newSku = prompt("Enter the SKU for the new Netflix redeem period:");
                if (newSku) {
                    this.config.NETFLIX_REDEEM_PERIODS[newSku] = 30; // 使用数字类型
                }
            },
            addNetflixAccount() {
                if (!this.config.NETFLIX_ACCOUNTS) {
                    this.config.NETFLIX_ACCOUNTS = [];
                }
                this.config.NETFLIX_ACCOUNTS.push({ email: '', password: '' });
            },

            removeNetflixAccount(index) {
                if (confirm('Are you sure you want to delete this Netflix account?')) {
                    this.config.NETFLIX_ACCOUNTS.splice(index, 1);
                }
            },
            updateNetflixSessions() {
                fetch('/admin/update_netflix_emails', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.emailChanges),
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log('Netflix emails updated successfully');
                            this.initialNetflixAccounts = [...this.config.NETFLIX_ACCOUNTS]; // 更新初始值
                            this.emailChanges = {}; // 清空变更记录
                        } else {
                            console.error('Failed to update Netflix emails');
                        }
                    })
                    .catch(error => {
                        console.error('Error updating Netflix emails:', error);
                    });
            },
            saveConfig() {
                // Convert numeric fields to numbers
                const numericFields = ['NETFLIX_MAX_RETRIES', 'NETFLIX_REQUEST_TIMEOUT', 'NETFLIX_RETRY_DELAY', 'NETFLIX_SESSION_COOLDOWN_TIME', 'REQUEST_TIMEOUT', 'SESSION_COOLDOWN_TIME', 'SHOP_ID', 'NETFLIX_ACCOUNT_LIMIT'];
                
                numericFields.forEach(field => {
                    if (this.config[field] !== undefined) {
                        this.config[field] = Number(this.config[field]);
                    }
                });

                // Convert NETFLIX_REDEEM_PERIODS values to numbers
                if (this.config.NETFLIX_REDEEM_PERIODS) {
                    for (let key in this.config.NETFLIX_REDEEM_PERIODS) {
                        this.config.NETFLIX_REDEEM_PERIODS[key] = Number(this.config.NETFLIX_REDEEM_PERIODS[key]);
                    }
                }
                fetch('/admin/update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.config),
                })
                    .then(response => response.json())
                    .then(data => {
                        // 清理 emailChanges 对象
                        Object.keys(this.emailChanges).forEach(oldEmail => {
                            if (!this.config.NETFLIX_ACCOUNTS.includes(this.emailChanges[oldEmail])) {
                                delete this.emailChanges[oldEmail];
                            }
                        });
                        if (Object.keys(this.emailChanges).length > 0) {
                            this.updateNetflixSessions();
                        }

                        alert(data.message);
                        this.animateSaveButton();
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                        alert('An error occurred while saving the configuration.');
                    });
            },
            addTextSku() {
                if (this.newTextSku.sku.trim() === '') {
                    alert('SKU cannot be empty.');
                    return;
                }
                this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.push({
                    sku: this.newTextSku.sku.trim(),
                    message: this.newTextSku.message.trim(),
                    is_unlimited_stock: this.newTextSku.is_unlimited_stock
                });
                if (!this.newTextSku.is_unlimited_stock) {
                    if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                    }
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push({
                        sku: this.newTextSku.sku.trim(),
                        stock: []
                    });
                }
                this.newTextSku = { sku: '', message: '', is_unlimited_stock: false };
            },
            deleteTextSku(index) {
                if (confirm('Are you sure you want to delete this SKU?')) {
                    const sku = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY[index];
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.splice(index, 1);
                    if (!sku.is_unlimited_stock && this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        const stockIndex = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.findIndex(s => s.sku === sku.sku);
                        if (stockIndex !== -1) {
                            this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.splice(stockIndex, 1);
                        }
                    }
                }
            },
            addVarSKU() {
                if (!this.config.AUTO_REDEEM_VAR_SKUS) {
                    this.config.AUTO_REDEEM_VAR_SKUS = [];
                }
                this.config.AUTO_REDEEM_VAR_SKUS.push({ sku: '', message: '' });
            },

            removeVarSKU(index) {
                this.config.AUTO_REDEEM_VAR_SKUS.splice(index, 1);
            },
            selectTextSku(sku) {
                this.selectedTextSku = sku;
            },
            manualOrder: {
                order_sn: '',
                var_sku: ''
            },
            manualOrders: [],
            createManualOrder() {
                if (this.manualOrder.order_sn.trim() === '' || this.manualOrder.var_sku.trim() === '') {
                    alert('Order SN and VAR SKU cannot be empty.');
                    return;
                }
                this.manualOrders.push({ ...this.manualOrder });
                this.manualOrder = { order_sn: '', var_sku: '' };
                this.saveManualOrders();
            },
            deleteManualOrder(index) {
                if (confirm('Are you sure you want to delete this manual order?')) {
                    this.manualOrders.splice(index, 1);
                    this.saveManualOrders();
                }
            },
            saveManualOrders() {
                fetch('/admin/update_manual_orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.manualOrders),
                })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                        alert('An error occurred while saving manual orders.');
                    });
            },
            loadManualOrders() {
                fetch('/admin/get_manual_orders')
                    .then(response => response.json())
                    .then(data => {
                        this.manualOrders = data;
                    })
                    .catch(error => {
                        console.error('Error loading manual orders:', error);
                    });
            },
            init() {
                this.loadConfig();
                this.loadManualOrders();
            },
            addTextSku() {
                if (this.newTextSku.sku.trim() === '') {
                    alert('SKU cannot be empty.');
                    return;
                }
                this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.push({
                    sku: this.newTextSku.sku.trim(),
                    message: this.newTextSku.message.trim(),
                    is_unlimited_stock: this.newTextSku.is_unlimited_stock
                });
                if (!this.newTextSku.is_unlimited_stock) {
                    if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                    }
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push({
                        sku: this.newTextSku.sku.trim(),
                        stock: []
                    });
                }
                this.newTextSku = { sku: '', message: '', is_unlimited_stock: false };
            },

            deleteTextSku(index) {
                if (confirm('Are you sure you want to delete this SKU?')) {
                    const sku = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY[index];
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY.splice(index, 1);
                    if (!sku.is_unlimited_stock && this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                        const stockIndex = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.findIndex(s => s.sku === sku.sku);
                        if (stockIndex !== -1) {
                            this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.splice(stockIndex, 1);
                        }
                    }
                }
            },

            getStockItems(sku) {
                const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === sku);
                return stockEntry ? stockEntry.stock : [];
            },

            addStockItems() {
                if (this.newStockItem.trim() === '') {
                    alert('Stock items cannot be empty.');
                    return;
                }
                if (!this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK) {
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = [];
                }
                let stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === this.selectedTextSku.sku);
                if (!stockEntry) {
                    stockEntry = { sku: this.selectedTextSku.sku, stock: [] };
                    this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.push(stockEntry);
                }

                // Split the input by newlines and add each non-empty item
                const newItems = this.newStockItem.split('\n').map(item => item.trim()).filter(item => item !== '');
                stockEntry.stock.push(...newItems);

                this.newStockItem = '';
                // Force Alpine to recognize the change
                this.config = { ...this.config };
            },

            deleteStockItem(sku, index) {
                const stockEntry = this.config.AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK.find(s => s.sku === sku);
                if (stockEntry) {
                    stockEntry.stock.splice(index, 1);
                }
            },
            animateInitialLoad() {
                anime({
                    targets: '.sidebar-item',
                    translateX: [-50, 0],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutQuad'
                });

                this.animateSection();
            },
            animateSection() {
                anime({
                    targets: '.section-content',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    easing: 'easeOutQuad',
                    duration: 500
                });

                anime({
                    targets: '.config-item',
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    easing: 'easeOutQuad'
                });
            },
            animateSaveButton() {
                anime({
                    targets: '.save-button',
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }
        }
    }
</script>
{% endblock %}