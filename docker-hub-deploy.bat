@echo off
REM Docker Hub deployment script for ShopeeAPI
REM This script pulls the ShopeeAPI Docker image from Docker Hub and deploys it

REM Configuration
set DOCKER_HUB_USERNAME=your-dockerhub-username
set DOCKER_HUB_REPO=shopeeapi
set IMAGE_NAME=%DOCKER_HUB_USERNAME%/%DOCKER_HUB_REPO%
set CONTAINER_NAME=shopeeapi
set CONFIG_PATH=D:\path\to\config.json
set LOGS_PATH=D:\path\to\logs

REM Parse command line arguments
set PULL=false
set DEPLOY=false
set RESTART=false
set ALL=false
set VERSION=latest

:parse_args
if "%~1"=="" goto check_args
if "%~1"=="--pull" (
    set PULL=true
    shift
    goto parse_args
)
if "%~1"=="--deploy" (
    set DEPLOY=true
    shift
    goto parse_args
)
if "%~1"=="--restart" (
    set RESTART=true
    shift
    goto parse_args
)
if "%~1"=="--all" (
    set ALL=true
    shift
    goto parse_args
)
if "%~1"=="--version" (
    set VERSION=%~2
    shift
    shift
    goto parse_args
)
echo Unknown option: %~1
echo Usage: %0 [--pull] [--deploy] [--restart] [--all] [--version VERSION]
exit /b 1

:check_args
REM If no arguments are provided, show help
if "%PULL%"=="false" if "%DEPLOY%"=="false" if "%RESTART%"=="false" if "%ALL%"=="false" (
    echo Usage: %0 [--pull] [--deploy] [--restart] [--all] [--version VERSION]
    echo   --pull: Pull the Docker image from Docker Hub
    echo   --deploy: Deploy the Docker image
    echo   --restart: Restart the container
    echo   --all: Pull, deploy, and restart
    echo   --version VERSION: Specify a version (default: latest)
    exit /b 0
)

REM Pull the Docker image from Docker Hub
if "%PULL%"=="true" goto pull
if "%ALL%"=="true" goto pull
goto check_deploy

:pull
echo Logging in to Docker Hub...
docker login

echo Pulling Docker image from Docker Hub: %IMAGE_NAME%:%VERSION%
docker pull %IMAGE_NAME%:%VERSION%

:check_deploy
REM Deploy the Docker image
if "%DEPLOY%"=="true" goto deploy
if "%ALL%"=="true" goto deploy
goto check_restart

:deploy
echo Stopping existing container if running...
docker stop %CONTAINER_NAME% 2>nul
docker rm %CONTAINER_NAME% 2>nul

echo Creating directories if they don't exist...
if not exist "%CONFIG_PATH%" (
    mkdir "%CONFIG_PATH%"
)
if not exist "%LOGS_PATH%" (
    mkdir "%LOGS_PATH%"
)

echo Deploying Docker container...
docker run -d ^
  --name %CONTAINER_NAME% ^
  -p 8000:8000 ^
  -v "%CONFIG_PATH%:/app/ShopeeAPI/config.json" ^
  -v "%LOGS_PATH%:/app/logs" ^
  --restart unless-stopped ^
  -e PORT=8000 ^
  -e ENVIRONMENT=production ^
  %IMAGE_NAME%:%VERSION%

echo Container deployed: %CONTAINER_NAME%

:check_restart
REM Restart the container
if "%RESTART%"=="true" goto restart
if "%ALL%"=="true" goto restart
goto end

:restart
echo Restarting container...
docker restart %CONTAINER_NAME%
echo Container restarted: %CONTAINER_NAME%

:end
echo Process completed!
