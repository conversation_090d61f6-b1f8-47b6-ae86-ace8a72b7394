# ShopeeAPI Deployment Guide

This guide provides instructions for deploying ShopeeAPI privately on your server with easy update capabilities.

## Deployment Options

There are several ways to deploy ShopeeAPI:

1. **Python Package Deployment**: Install as a Python package with systemd service
2. **Docker Deployment**: Deploy using Docker and Docker Compose
3. **Manual Deployment**: Simple copy and run approach

Choose the method that best fits your server environment and technical preferences.

## 1. Python Package Deployment

This method packages ShopeeAPI as a Python package and installs it on your server.

### Prerequisites

- Python 3.8+ on your server
- pip and venv
- systemd (for service management)
- SSH access to your server

### Deployment Steps

1. **Configure the deployment script**:

   Edit `deploy.py` and update the following variables:
   ```python
   SERVER_HOST = "your-server-hostname"  # Change to your server hostname or IP
   SERVER_USER = "your-username"         # Change to your server username
   SERVER_PATH = "/path/to/shopee-api"   # Change to your server deployment path
   ```

2. **Build and deploy**:
   ```bash
   python deploy.py --all
   ```

   This will:
   - Build the Python package
   - Upload it to your server
   - Install it in a virtual environment
   - Create and enable a systemd service
   - Start the service

3. **For future updates**:
   ```bash
   python deploy.py --all
   ```

   The script will handle versioning automatically.

## 2. Docker Deployment

There are two Docker deployment options available:

1. **Direct Server Deployment**: Build locally and deploy directly to your server
2. **Docker Hub Deployment**: Push to a private Docker Hub repository and pull on your server

### 2.1 Direct Server Deployment

This method builds the Docker image locally and deploys it directly to your server.

#### Prerequisites

- Docker and Docker Compose on your local machine and server
- SSH access to your server

#### Deployment Steps

1. **Configure the deployment script**:

   Edit `docker-deploy.sh` and update the following variables:
   ```bash
   SERVER_HOST="your-server-hostname"  # Change to your server hostname or IP
   SERVER_USER="your-username"         # Change to your server username
   SERVER_PATH="/path/to/shopee-api"   # Change to your server deployment path
   ```

2. **Build and deploy**:
   ```bash
   ./docker-deploy.sh --all
   ```

   This will:
   - Build the Docker image
   - Save and upload it to your server
   - Deploy it using Docker Compose
   - Start the container

3. **For future updates**:
   ```bash
   ./docker-deploy.sh --all
   ```

   The script will handle versioning automatically.

### 2.2 Docker Hub Deployment (Private Repository)

This method pushes the Docker image to a private Docker Hub repository and pulls it on your server.

#### Prerequisites

- Docker and Docker Compose on your local machine and server
- Docker Hub account with a private repository
- SSH access to your server (optional, for remote deployment)

#### Setup Docker Hub Private Repository

1. **Create a Docker Hub account** if you don't have one already at [Docker Hub](https://hub.docker.com/)

2. **Create a private repository**:
   - Log in to Docker Hub
   - Click on "Create Repository"
   - Name it (e.g., "shopeeapi")
   - Set visibility to "Private"
   - Click "Create"

3. **Configure the push script**:

   Edit `docker-hub-push.sh` and update the following variables:
   ```bash
   DOCKER_HUB_USERNAME="your-dockerhub-username"  # Change to your Docker Hub username
   DOCKER_HUB_REPO="shopeeapi"                    # Change to your repository name
   ```

#### Local Deployment Steps

##### For Linux/macOS:

1. **Build and push to Docker Hub**:
   ```bash
   # Make the script executable
   chmod +x docker-hub-push.sh

   # Build and push
   ./docker-hub-push.sh --all
   ```

   This will:
   - Build the Docker image
   - Tag it with a version and latest
   - Push it to your private Docker Hub repository

2. **For future updates**:
   ```bash
   ./docker-hub-push.sh --all
   ```

   The script will handle versioning automatically.

##### For Windows:

1. **Build and push to Docker Hub**:
   ```cmd
   # Build and push
   docker-hub-push.bat --all
   ```

   This will:
   - Build the Docker image
   - Tag it with a version and latest
   - Push it to your private Docker Hub repository

2. **For future updates**:
   ```cmd
   docker-hub-push.bat --all
   ```

   The script will handle versioning automatically.

#### Server Deployment Steps

##### For Linux/macOS Servers:

1. **Configure the server deployment script**:

   Edit `docker-hub-deploy.sh` and update the following variables:
   ```bash
   DOCKER_HUB_USERNAME="your-dockerhub-username"  # Change to your Docker Hub username
   DOCKER_HUB_REPO="shopeeapi"                    # Change to your repository name
   CONFIG_PATH="/path/to/config.json"             # Change to your config.json path
   LOGS_PATH="/path/to/logs"                      # Change to your logs directory path
   ```

2. **Deploy on your server**:
   ```bash
   # Make the script executable
   chmod +x docker-hub-deploy.sh

   # Pull and deploy
   ./docker-hub-deploy.sh --all
   ```

   This will:
   - Pull the Docker image from your private Docker Hub repository
   - Deploy it as a container
   - Start the container

3. **For future updates**:
   ```bash
   ./docker-hub-deploy.sh --pull --restart
   ```

##### For Windows Servers:

1. **Configure the server deployment script**:

   Edit `docker-hub-deploy.bat` and update the following variables:
   ```cmd
   set DOCKER_HUB_USERNAME=your-dockerhub-username
   set DOCKER_HUB_REPO=shopeeapi
   set CONFIG_PATH=D:\path\to\config.json
   set LOGS_PATH=D:\path\to\logs
   ```

2. **Deploy on your server**:
   ```cmd
   # Pull and deploy
   docker-hub-deploy.bat --all
   ```

   This will:
   - Pull the Docker image from your private Docker Hub repository
   - Deploy it as a container
   - Start the container

3. **For future updates**:
   ```cmd
   docker-hub-deploy.bat --pull --restart
   ```

#### Using Docker Compose on Server

Alternatively, you can use Docker Compose for deployment:

1. **Copy the production docker-compose file to your server**:
   ```bash
   scp ShopeeAPI/docker-compose.prod.yml your-username@your-server-hostname:/path/to/shopee-api/docker-compose.yml
   ```

2. **Update the docker-compose.yml file on your server**:
   ```yaml
   version: '3'

   services:
     shopee-api:
       image: your-dockerhub-username/shopeeapi:latest
       container_name: shopeeapi
       ports:
         - "8000:8000"
       volumes:
         - ./config.json:/app/config.json
         - ./logs:/app/logs
       restart: unless-stopped
       environment:
         - PORT=8000
         - ENVIRONMENT=production
   ```

3. **Deploy using Docker Compose**:
   ```bash
   # SSH into your server
   ssh your-username@your-server-hostname

   # Navigate to your deployment directory
   cd /path/to/shopee-api

   # Login to Docker Hub
   docker login

   # Pull and deploy
   docker-compose pull
   docker-compose up -d
   ```

4. **For future updates**:
   ```bash
   docker-compose pull
   docker-compose up -d
   ```

## 3. Manual Deployment

This is the simplest method, suitable for small servers or when you want full control.

### Prerequisites

- Python 3.8+ on your server
- pip and venv
- SSH access to your server

### Deployment Steps

1. **Prepare your server**:
   ```bash
   # SSH into your server
   ssh your-username@your-server-hostname

   # Create a directory for the application
   mkdir -p /path/to/shopee-api
   cd /path/to/shopee-api

   # Create a virtual environment
   python3 -m venv venv
   source venv/bin/activate
   ```

2. **Copy the code to your server**:
   ```bash
   # From your local machine
   tar -czf shopee-api.tar.gz ShopeeAPI/
   scp shopee-api.tar.gz your-username@your-server-hostname:/path/to/shopee-api/

   # On your server
   cd /path/to/shopee-api
   tar -xzf shopee-api.tar.gz
   rm shopee-api.tar.gz
   ```

3. **Install dependencies**:
   ```bash
   # On your server
   source venv/bin/activate
   pip install -r ShopeeAPI/requirements.txt
   ```

4. **Create a configuration file**:
   ```bash
   cp ShopeeAPI/config.json.example ShopeeAPI/config.json
   # Edit the config.json file with your credentials
   nano ShopeeAPI/config.json
   ```

5. **Create a systemd service**:
   ```bash
   # Create a service file
   sudo nano /etc/systemd/system/shopee-api.service
   ```

   Add the following content (adjust paths as needed):
   ```
   [Unit]
   Description=Shopee API Service
   After=network.target

   [Service]
   User=your-username
   WorkingDirectory=/path/to/shopee-api
   ExecStart=/path/to/shopee-api/venv/bin/python -m ShopeeAPI.main
   Restart=always
   RestartSec=5
   StandardOutput=syslog
   StandardError=syslog
   SyslogIdentifier=shopee-api

   [Install]
   WantedBy=multi-user.target
   ```

6. **Enable and start the service**:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable shopee-api
   sudo systemctl start shopee-api
   ```

7. **For future updates**:
   ```bash
   # From your local machine
   tar -czf shopee-api.tar.gz ShopeeAPI/
   scp shopee-api.tar.gz your-username@your-server-hostname:/path/to/shopee-api/

   # On your server
   cd /path/to/shopee-api
   source venv/bin/activate
   sudo systemctl stop shopee-api
   tar -xzf shopee-api.tar.gz
   rm shopee-api.tar.gz
   pip install -r ShopeeAPI/requirements.txt
   sudo systemctl start shopee-api
   ```

## Monitoring and Maintenance

### Checking Service Status

```bash
# For systemd service
sudo systemctl status shopee-api

# For Docker
docker ps
docker logs shopee-api
```

### Viewing Logs

```bash
# For systemd service
sudo journalctl -u shopee-api -f

# For Docker
docker logs -f shopee-api
```

### Backup Configuration

Always backup your `config.json` file before updates:

```bash
cp ShopeeAPI/config.json ShopeeAPI/config.json.backup
```

## Security Considerations

1. **Protect your credentials**: Ensure your `config.json` file has restricted permissions
   ```bash
   chmod 600 ShopeeAPI/config.json
   ```

2. **Use HTTPS**: Consider setting up a reverse proxy with HTTPS
   ```bash
   # Example with Nginx
   sudo apt install nginx
   # Configure Nginx with SSL
   ```

3. **Firewall**: Only expose the necessary ports
   ```bash
   sudo ufw allow 22/tcp  # SSH
   sudo ufw allow 443/tcp # HTTPS
   sudo ufw enable
   ```

## Troubleshooting

### Service Won't Start

Check the logs:
```bash
sudo journalctl -u shopee-api -e
```

### API Returns Errors

Check if the configuration is correct:
```bash
# Test the API status endpoint
curl http://localhost:8000/status
```

### Docker Container Crashes

Check the logs:
```bash
docker logs shopee-api
```

## Docker Deployment Guide for Baota Panel

This section provides detailed instructions for deploying ShopeeAPI using the Baota Docker panel.

### Prerequisites

- Baota Panel installed on your server
- Docker installed and configured in Baota
- Access to a private Docker Hub repository with ShopeeAPI image

### Required Configuration

#### Port Configuration

ShopeeAPI requires the following port to be exposed:

- **8000**: Main API port (HTTP)

#### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| PORT | No | 8000 | The port on which the API will listen |
| ENVIRONMENT | No | production | Deployment environment (production/development) |
| LOG_LEVEL | No | info | Logging level (debug/info/warning/error) |

#### Volume Mounts

ShopeeAPI requires the following volumes to be mounted:

1. **Configuration File**:
   - Container path: `/app/config.json`
   - Host path: Path to your configuration file on the host

2. **Logs Directory**:
   - Container path: `/app/logs`
   - Host path: Directory where you want to store logs

### Deployment Steps in Baota Panel

1. **Access Docker Manager in Baota Panel**:
   - Log in to your Baota Panel
   - Navigate to "Docker Manager" or "Docker" section

2. **Pull the Docker Image**:
   - Click "Pull Image"
   - Enter your Docker Hub image URL: `limjianhui789/shopee-api:latest`
   - Enter your Docker Hub credentials if prompted

3. **Create Container**:
   - Click "Create Container"
   - Select the pulled image: `limjianhui789/shopee-api:latest`
   - Configure the following settings:

4. **Basic Settings**:
   - Container Name: `shopeeapi`
   - Restart Policy: `Unless stopped`
   - Network Mode: `Bridge`

5. **Port Mapping**:
   - Host Port: `8000` (or your preferred port)
   - Container Port: `8000`
   - Protocol: `TCP`

6. **Volume Mapping**:
   - Add the following volume mappings:
     - Host Path: `/path/to/config.json` → Container Path: `/app/config.json`
     - Host Path: `/path/to/logs` → Container Path: `/app/logs`

7. **Environment Variables**:
   - Add the following environment variables:
     - `PORT=8000`
     - `ENVIRONMENT=production`
     - `LOG_LEVEL=info` (optional)
     - `PYTHONPATH=/app` (important for module resolution)

8. **Resource Limits** (Optional):
   - Memory Limit: `512MB` (recommended minimum)
   - CPU Limit: `1.0` (recommended minimum)

9. **Create and Start the Container**:
   - Click "Create" or "Submit"
   - The container should start automatically

### Configuration File Setup

Before starting the container, you need to create a `config.json` file on your host system:

1. **Create the configuration file**:
   ```bash
   mkdir -p /path/to/config
   touch /path/to/config.json
   ```

2. **Edit the configuration file** with your Shopee credentials:
   ```json
   {
     "authorization_code": "your-shopee-authorization-code",
     "cookies": {
       "SPC_EC": "your-spc-ec-cookie",
       "SPC_U": "your-spc-u-cookie",
       "SPC_ST": "your-spc-st-cookie"
     },
     "webhook": {
       "enabled": false,
       "url": "https://your-webhook-url",
       "events": ["message_received", "message_sent"]
     },
     "cache": {
       "enabled": true,
       "max_messages": 100
     }
   }
   ```

3. **Set proper permissions**:
   ```bash
   chmod 600 /path/to/config.json
   ```

### Verifying Deployment

1. **Check Container Status**:
   - In Baota Panel, go to Docker Manager
   - Verify the container is running (green status)

2. **Check Container Logs**:
   - Click on the container name
   - Select "Logs" to view the container logs
   - Verify there are no errors during startup

3. **Test the API**:
   - Open a web browser or use curl to test the API:
   ```bash
   curl http://your-server-ip:8000/status
   ```
   - You should receive a successful response

### Troubleshooting

1. **Container fails to start**:
   - Check the container logs for error messages
   - Verify the configuration file exists and has correct permissions
   - Ensure the ports are not already in use

2. **API returns authentication errors**:
   - Check your `config.json` file for correct credentials
   - Update the authorization code and cookies if they've expired

3. **Volume mount issues**:
   - Verify the paths on your host system exist
   - Check permissions on the host directories

4. **Network connectivity issues**:
   - Ensure the port is open in your firewall
   - Check if Baota's firewall is blocking the port

5. **ImportError: attempted relative import with no known parent package** or **ModuleNotFoundError: No module named 'ShopeeAPI'**:
   - These errors occur when the application is not properly installed as a package or the Python path is not set correctly
   - If you're using the latest Docker image from Docker Hub, these issues should be fixed
   - If you're building your own image, make sure to:
     1. Set the PYTHONPATH environment variable: `ENV PYTHONPATH=/app`
     2. Use a run script that ensures the correct working directory and Python path:
        ```bash
        #!/bin/bash
        export PYTHONPATH=/app
        cd /app
        exec uvicorn ShopeeAPI.api:app --host 0.0.0.0 --port $PORT
        ```
   - If you're using Baota Panel with a custom command, use the included run script:
     ```
     /app/run_in_baota.sh
     ```
   - Alternatively, you can set these environment variables in Baota Panel:
     ```
     PYTHONPATH=/app
     ```
     And use this command:
     ```
     cd /app && uvicorn ShopeeAPI.api:app --host 0.0.0.0 --port 8000
     ```

### Updating the Container

When a new version of ShopeeAPI is available:

1. **Pull the new image**:
   - In Baota Panel, go to Docker Manager
   - Click "Pull Image"
   - Enter the same image URL with the new tag or `:latest`

2. **Recreate the container**:
   - Stop and remove the existing container
   - Create a new container with the same settings but using the new image
   - Start the new container

## Conclusion

Choose the deployment method that best suits your needs and server environment. The Docker method provides the most isolation and easiest updates, while the Python package method offers better integration with the system. For Baota Panel users, the Docker deployment provides a convenient way to manage the application through the panel's interface.
