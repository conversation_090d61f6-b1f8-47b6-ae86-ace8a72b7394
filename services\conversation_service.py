from utils.api_utils import (
    get_initial_order_list, process_orders, get_common_params
)
from utils.session import session
import config
from urllib.parse import quote

def get_conversation_info_by_ordersn(order_sn):
    initial_data = get_initial_order_list("all", order_sn)
    order_data = process_orders(initial_data, "all")

    if not order_data['data']['card_list']:
        return {"error": "Order not found"}, 404

    order = order_data['data']['card_list'][0]['order_card']
    user_id = order['order_ext_info']['buyer_user_id']

    payload = {
        "user_id": user_id,
        "shop_id": config.SHOP_ID
    }

    params = {
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": config.CSRF_TOKEN,
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID,
        "_api_source": "sc"
    }

    conversation_response = session.post(
        config.CONVERSATION_URL,
        params=params,
        json=payload
    )

    if conversation_response.status_code != 200:
        return {"error": "Failed to retrieve conversation info"}, 500

    return conversation_response.json()

def get_conversation_info_by_username(username):
    params = {
        "per_page": "20",
        "keyword": username,
        "type": "3",
        "_uid": f"0-{config.SHOP_ID}",
        "_v": "8.5.6",
        "csrf_token": quote(config.CSRF_TOKEN),
        "SPC_CDS_CHAT": config.SPC_CDS_CHAT,
        "x-shop-region": config.REGION_ID
    }

    conversation_response = session.get(
        config.CONVERSATION_SEARCH_URL,
        params=params
    )

    if conversation_response.status_code != 200:
        return {"error": "Failed to retrieve conversation info"}, 500

    return conversation_response.json()