# config.py
import re
import os
import json
import threading
import time
from services.deepseek_service import DEFAULT_SYSTEM_PROMPT

def extract_spc_cds(cookie_string):
    cookies = cookie_string.split('; ')
    for cookie in cookies:
        if cookie.startswith('SPC_CDS='):
            return cookie.split('=')[1]
    return None

def extract_spc_cds_ver(cookie_string):
    cookies = cookie_string.split('; ')
    for cookie in cookies:
        if cookie.startswith('SPC_CDS_VER='):
            return cookie.split('=')[1]
    return None

def extract_csrf_token(cookie_string):
    match = re.search(r'CTOKEN=([^;]+)', cookie_string)
    if match:
        return match.group(1)
    return None

def extract_spc_cds_chat(cookie_string):
    match = re.search(r'SPC_CDS_CHAT=([^;]+)', cookie_string)
    if match:
        return match.group(1)
    return None

# Configuration settings for the Flask application

# AI Reply Settings
AI_REPLY_ENABLED = True
AI_REPLY_COOLDOWN_MINUTES = 1
AI_TEMPERATURE = 1.0
AI_SYSTEM_PROMPT = DEFAULT_SYSTEM_PROMPT
DEEPSEEK_API_KEY = "***********************************"  # Default key, will be updated from config


USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0"
AUTHORIZATION_CODE = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVhdGVfdGltZSI6MTcyNzUwODIyMywiaWQiOiIyMzM4ZjMzZi03OTc4LTExZWYtYjBjNi1kMjE5NjgxZmEwYjkifQ.5CZ0TC4-fo9Vb3qpTHLqTwuEXnGDRILPTU0qdJRUZfA"
COOKIE = "SPC_F=0FxmoD74qPyeVzgH6pbb8znnfL2q7rfa; REC_T_ID=06fe01c0-d49c-11ee-a01e-4ec34638d93f; SPC_CLIENTID=MEZ4bW9ENzRxUHllclxbtmzhgkgjasoo; SC_DFP=aHHpCraQVHazZKaEUoqWBrlzNeIQhGSQ; fulfillment-language=en-my; __stripe_mid=f4f86998-6665-4845-8f4f-eafb237a20f8807981; _QPWSDCXHZQA=08e75837-d93b-4add-c0d1-26c7388505f0; __utma=148007157.2043454369.1708947711.1719834747.1719834747.1; __utmz=148007157.1719834747.1.1.utmcsr=seller.shopee.com.my|utmccn=(referral)|utmcmd=referral|utmcct=/; REC7iLP4Q=fa6171d6-d879-4e1b-a724-888b8f5eb56d; _med=refer; _gcl_au=1.1.1714795151.1724832786; _ga_YE6LVF7NTK=GS1.1.1725195957.11.0.1725195957.60.0.0; SPC_SI=jWOfZgAAAABIYWlFeDFaQtSf5wYAAAAAZ3hlUjg1TjQ=; _gid=GA1.3.1736937987.1726470259; _ga_9JFP65TWFK=GS1.1.1726471102.3.1.1726471806.59.0.0; _med=refer; SPC_EC=.Mk8yTjVsbXpVZVBsT0J0ddrhT1Iwe9oJftTzDBHlEgr00K7X3domc1inUv3wr9l3LsjkBWgHvRYSDu80SEjcpNtjGjcGZPikTx/o6lPfYYMQuRXLpVSPb+V2td/GBkp8/kC0S+oh8KZkFzfvqJtGzV0/vtEHBOkIiZx59PHKKAeNgU51mqPtfIo6c2Dv37r9xfG4TnAuEDTTNDHeRhBjcs7jkEGsqXqVqUIJJk0/XdFda1Nk+KQu7wFmoQrtup7q; SPC_ST=.Mk8yTjVsbXpVZVBsT0J0ddrhT1Iwe9oJftTzDBHlEgr00K7X3domc1inUv3wr9l3LsjkBWgHvRYSDu80SEjcpNtjGjcGZPikTx/o6lPfYYMQuRXLpVSPb+V2td/GBkp8/kC0S+oh8KZkFzfvqJtGzV0/vtEHBOkIiZx59PHKKAeNgU51mqPtfIo6c2Dv37r9xfG4TnAuEDTTNDHeRhBjcs7jkEGsqXqVqUIJJk0/XdFda1Nk+KQu7wFmoQrtup7q; SPC_SC_TK=dec22fe6379c89e4bf2bff9780aa07e4; SPC_SC_UD=345622437; SPC_SC_SESSION=82e2b04d35e8c3e46ca2b83eab7cd86b_1_345622437; SPC_STK=EXP4+BBdDRAxcW482YZRRpPdBuVHjRlqWNWRTB6fhJMF+cX8uRRyq+2PSRm6M+aMdVWhzxYuuO8CzEjOJggOGTJlOI9agk/+eTq5H6HHCZb5/48sNcttC09EzcFWvME7EpT5iouKqLFicRvRXGHq+Wnup9nhrQNuR/lLPWgPd5KU9pm9j5dKIG9vGRrzxQoIHqSlNk9p6jxbdP4YLQcGXlMXFzFy1jYfZe3Oe/ZgLtk=; SPC_U=345622437; SPC_R_T_ID=weB+StqM8YssNmUmf1I3M5+ucp99z3WoNnp/FFzoHtzaYi+WFQKaOQOTahrCSlwm9GGhtBmPp2NAN9MnhQJeagO2IIk4LD7AhHRH+NffAFmlsKqKTkXW1wNRBv/f+gzoTSK5s6jpJTLyZM1bX6dKWRmqgJV/PztluEY9fBTP7Ls=; SPC_R_T_IV=dHZxZmM3T01Xd21JMFB4bQ==; SPC_T_ID=weB+StqM8YssNmUmf1I3M5+ucp99z3WoNnp/FFzoHtzaYi+WFQKaOQOTahrCSlwm9GGhtBmPp2NAN9MnhQJeagO2IIk4LD7AhHRH+NffAFmlsKqKTkXW1wNRBv/f+gzoTSK5s6jpJTLyZM1bX6dKWRmqgJV/PztluEY9fBTP7Ls=; SPC_T_IV=dHZxZmM3T01Xd21JMFB4bQ==; language=en; _ga=GA1.3.2043454369.1708947711; _ga_NEYMG30JL4=GS1.1.1727453985.345.1.1727455622.60.0.0; SPC_SEC_SI=v1-bmFqTjVabndKUlpEZWREbxN/MbpfyJS0MYoF0zt/siNHYTIIAn2ZhYNFezFh/psDiUlEDtmrXcJcF0JagLDegmOcn+f01NOSO94FBcTOefQ=; SPC_CDS=8b9877ff-99ac-4eb8-8920-eaf7249c2a3a; SPC_CDS_CHAT=97d9cb15-3010-4733-a146-c400a27e01ab; _sapid=a43910091836e16fa164dc0b73e680f0ecbd528118e5660f0a363d84; CTOKEN=SV%2F9yn1dEe%2BxHHJ2rRHYOg%3D%3D; shopee_webUnique_ccd=99iJxrqTky9ihLRbhr6KJA%3D%3D%7ClxMi%2FK0kBKyUzxNNpYITKExyqO91xRwAnZhXKtIgH1cdq6FwNBmbnbrseJFi5oNJb5t6y18W0D0%3D%7Cu0PcFqU736y8ZD%2FU%7C08%7C3; ds=a2e562920cffac8a72220e17f89d79b8"

SPC_CDS = extract_spc_cds(COOKIE)
SPC_CDS_VER = extract_spc_cds_ver(COOKIE)
CSRF_TOKEN = extract_csrf_token(COOKIE)
SPC_CDS_CHAT = extract_spc_cds_chat(COOKIE)

# Constants
PAGE_SIZE = 40
SHOP_ID = 101806022
REGION_ID = "MY"

# API URLs
INITIAL_ORDER_LIST_URL = "https://seller.shopee.com.my/api/v3/order/search_order_list_index"
ORDER_DETAILS_URL = "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list"
ORDER_DETAILS_URL_SPECIFIC_ORDER = "https://seller.shopee.com.my/api/v3/order/get_one_order"
INIT_ORDER_URL = "https://seller.shopee.com.my/api/v3/shipment/init_order"
CONVERSATION_URL = "https://seller.shopee.com.my/webchat/api/v1.2/mini/conversation/redirection"
CONVERSATION_SEARCH_URL = "https://seller.shopee.com.my/webchat/api/coreapi/v1.2/search/combined_search"
CHAT_MESSAGE_URL = "https://seller.shopee.com.my/webchat/api/v1.2/messages"
RECENT_CONVERSATIONS_URL = "https://seller.shopee.com.my/webchat/api/v1.2/conversations"
CONVERSATION_MESSAGES_URL = "https://seller.shopee.com.my/webchat/api/v1.2/conversations"

# Order list tab values
ALL_ORDERS_TAB = 100
TO_SHIP_TAB = 300
SHIPPED_TAB = 400
COMPLETED_TAB = 500

# New Configuration Variables
SEND_CHAT_ON_AUTH_SUCCESS = True
AUTO_SHIP_ORDER = False
CHECK_NEW_ORDERS = True  # New feature toggle
ENABLE_AUTO_REDEEM = False
AUTO_REDEEM_VAR_SKUS = []
AUTO_REDEEM_VAR_SKUS_TEXT_ONLY = []
AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = []

AUTO_REPLY_ENABLED = False
AUTO_REPLY_DELAY_MINUTES = 1
AUTO_REPLY_COOLDOWN_MINUTES = 60
AUTO_REPLY_MESSAGE = "Currently Im Offline, I will reply you as soon as possible!"

sent_orders = set()
EMAIL_CONFIGS = {
    "mtyb_developer": {
        "email": "<EMAIL>",
        "app_password": "nnqy dxjg qxrd zsxg"
    }
}

STEAM_CREDENTIALS = {
    "mtyb_developer": {
        "password": "@Whitepaperh0817"
    }
}

CHAT_MESSAGES = {
    "steam_auth_code_success": "✉️ Successfully Redeem The Steam Auth Code\n\n🎖️Code: {auth_code}\n🎖️Next redeem time: {next_redeem_time}",
    "netflix_signin_code_success": "✉️ Successfully Retrieve Netflix Sign-In Code\n\n🎖️Email: {email}\n🎖️Code: {code}"
}

NOTIFICATION_EMAIL = {
    "address": "",
    "app_password": ""
}

ADMIN_CREDENTIALS = {
    "username": "admin",
    "password": "admin"
}

API_KEY = "MTYB_OFFICIAL"

SESSION_COOLDOWN_TIME = 600 # After successfully request, how long should wait
REQUEST_TIMEOUT = 60  # New timeout for each request
SEND_MESSAGE_ON_SHIP = False
SHIP_SUCCESS_MESSAGE_TEMPLATE = ""
AUTH_CODE_DELAY = 3
CURLEC_API_KEY = ""
CURLEC_SECRET_KEY = ""

# Sent Orders Tracking
SENT_ORDERS_FILE = os.path.join(os.path.dirname(__file__), 'sent_orders.json')

def load_sent_orders():
    if os.path.exists(SENT_ORDERS_FILE):
        with open(SENT_ORDERS_FILE, 'r') as f:
            return set(json.load(f))
    return set()

def save_sent_orders(sent_orders):
    with open(SENT_ORDERS_FILE, 'w') as f:
        json.dump(list(sent_orders), f, indent=2)

# User-configurable settings
config_path = os.path.join(os.path.dirname(__file__), 'config.json')

def save_config(new_config):
    with open(config_path, 'w') as config_file:
        json.dump(new_config, config_file, indent=2)

def load_config():
    with open(config_path, 'r') as config_file:
        return json.load(config_file)

user_config = load_config()

def update_session_manager(new_config):
    from services.session_service import session_manager
    session_manager.update_config(new_config)

def update_config():
    global user_config, SPC_CDS, ADMIN_CREDENTIALS, AUTO_REPLY_MESSAGE, AUTO_REPLY_ENABLED, AUTO_REPLY_DELAY_MINUTES, AUTO_REPLY_COOLDOWN_MINUTES, SPC_CDS_VER, CSRF_TOKEN, SPC_CDS_CHAT, API_KEY, AUTH_CODE_DELAY, SEND_MESSAGE_ON_SHIP, SHIP_SUCCESS_MESSAGE_TEMPLATE, EMAIL_CONFIGS, AUTO_REDEEM_VAR_SKUS_TEXT_ONLY, AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK, AUTO_REDEEM_MESSAGE, STEAM_CREDENTIALS, COOKIE, AUTHORIZATION_CODE, SHOP_ID, NOTIFICATION_EMAIL, REQUEST_TIMEOUT, SESSION_COOLDOWN_TIME, SEND_CHAT_ON_AUTH_SUCCESS, AUTO_SHIP_ORDER, AUTO_REDEEM_VAR_SKUS, ENABLE_AUTO_REDEEM, CHECK_NEW_ORDERS, sent_orders, CURLEC_API_KEY, CURLEC_SECRET_KEY, AI_REPLY_ENABLED, AI_REPLY_COOLDOWN_MINUTES, AI_TEMPERATURE, AI_SYSTEM_PROMPT, DEEPSEEK_API_KEY

    new_config = load_config()
    user_config = new_config

    # Update SessionManager config
    update_session_manager(user_config)

    # Update credential manager with new values from config
    from utils.credential_manager import credential_manager
    AUTHORIZATION_CODE = new_config.get('AUTHORIZATION_CODE', AUTHORIZATION_CODE)
    COOKIE = new_config.get('COOKIE', COOKIE)

    # Check if JSON cookies are available
    cookie_data = COOKIE
    if 'COOKIE_JSON' in new_config:
        cookie_data = new_config.get('COOKIE_JSON')
        print("Using JSON cookie format from config")

    credential_manager.update_credentials(
        authorization_code=AUTHORIZATION_CODE,
        cookie=cookie_data
    )

    # Update cookie-derived values after potential cookie update
    SPC_CDS = extract_spc_cds(COOKIE)
    SPC_CDS_VER = extract_spc_cds_ver(COOKIE)
    CSRF_TOKEN = extract_csrf_token(COOKIE)
    SPC_CDS_CHAT = extract_spc_cds_chat(COOKIE)

    # Update AI Chat settings
    AI_REPLY_ENABLED = new_config.get('AI_REPLY_ENABLED', False)
    AI_REPLY_COOLDOWN_MINUTES = new_config.get('AI_REPLY_COOLDOWN_MINUTES', 60)
    AI_TEMPERATURE = new_config.get('AI_TEMPERATURE', 1.0)
    AI_SYSTEM_PROMPT = new_config.get('AI_SYSTEM_PROMPT', DEFAULT_SYSTEM_PROMPT)
    DEEPSEEK_API_KEY = new_config.get('DEEPSEEK_API_KEY', DEEPSEEK_API_KEY)

    # Update other existing settings
    API_KEY = user_config.get('API_KEY', API_KEY)
    EMAIL_CONFIGS = user_config.get('EMAIL_CONFIGS', EMAIL_CONFIGS)
    STEAM_CREDENTIALS = user_config.get('STEAM_CREDENTIALS', STEAM_CREDENTIALS)
    SHOP_ID = user_config.get('SHOP_ID', SHOP_ID)
    NOTIFICATION_EMAIL = user_config.get('NOTIFICATION_EMAIL', NOTIFICATION_EMAIL)
    REQUEST_TIMEOUT = user_config.get('REQUEST_TIMEOUT', REQUEST_TIMEOUT)
    SESSION_COOLDOWN_TIME = user_config.get('SESSION_COOLDOWN_TIME', SESSION_COOLDOWN_TIME)
    SEND_CHAT_ON_AUTH_SUCCESS = user_config.get('SEND_CHAT_ON_AUTH_SUCCESS', SEND_CHAT_ON_AUTH_SUCCESS)
    AUTO_SHIP_ORDER = user_config.get('AUTO_SHIP_ORDER', AUTO_SHIP_ORDER)
    ENABLE_AUTO_REDEEM = user_config.get('ENABLE_AUTO_REDEEM', False)
    AUTO_REPLY_ENABLED = user_config.get('AUTO_REPLY_ENABLED', False)
    AUTO_REPLY_DELAY_MINUTES = user_config.get('AUTO_REPLY_DELAY_MINUTES', AUTO_REPLY_DELAY_MINUTES)
    AUTO_REPLY_COOLDOWN_MINUTES = user_config.get('AUTO_REPLY_COOLDOWN_MINUTES', AUTO_REPLY_COOLDOWN_MINUTES)
    AUTO_REPLY_MESSAGE = user_config.get('AUTO_REPLY_MESSAGE', AUTO_REPLY_MESSAGE)
    AUTO_REDEEM_VAR_SKUS = user_config.get('AUTO_REDEEM_VAR_SKUS', [])
    CHECK_NEW_ORDERS = user_config.get('CHECK_NEW_ORDERS', CHECK_NEW_ORDERS)
    AUTO_REDEEM_MESSAGE = user_config.get('AUTO_REDEEM_MESSAGE', "Hello, this is a default auto-redeem message.")
    AUTO_REDEEM_VAR_SKUS_TEXT_ONLY = user_config.get('AUTO_REDEEM_VAR_SKUS_TEXT_ONLY', [])
    AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK = user_config.get('AUTO_REDEEM_VAR_SKUS_TEXT_ONLY_STOCK', [])
    SEND_MESSAGE_ON_SHIP = user_config.get('SEND_MESSAGE_ON_SHIP', SEND_MESSAGE_ON_SHIP)
    SHIP_SUCCESS_MESSAGE_TEMPLATE = user_config.get('SHIP_SUCCESS_MESSAGE_TEMPLATE', SHIP_SUCCESS_MESSAGE_TEMPLATE)
    AUTH_CODE_DELAY = user_config.get('AUTH_CODE_DELAY', AUTH_CODE_DELAY)
    ADMIN_CREDENTIALS = user_config.get('ADMIN_CREDENTIALS', {
        "username": "admin",
        "password": "whitepaperh0817"
    })
    sent_orders = load_sent_orders()

    # Add these lines to update payment gateway credentials
    CURLEC_API_KEY = user_config.get('CURLEC_API_KEY')
    CURLEC_SECRET_KEY = user_config.get('CURLEC_SECRET_KEY')

def periodic_config_update():
    while True:
        update_config()
        time.sleep(60)  # Check for updates every 60 seconds

# Start the periodic update in a separate thread
update_thread = threading.Thread(target=periodic_config_update, daemon=True)
update_thread.start()

# Initial configuration load
update_config()