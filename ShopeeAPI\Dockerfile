FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8000
ENV PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    sudo \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create log directory
RUN mkdir -p /app/logs
RUN mkdir -p /app/ShopeeAPI

# Copy requirements first for better caching
COPY ShopeeAPI/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional packages
RUN pip install --no-cache-dir watchdog websockets==10.4

# Copy the entire project for context
COPY . /app/

# Make sure documentation is available in the container
RUN echo "Documentation is available at /app/ShopeeAPI/DOCKER.md" >> /app/README.md

# Copy our entrypoint and permission fix scripts
COPY ShopeeAPI/docker-entrypoint.sh /app/docker-entrypoint.sh
COPY ShopeeAPI/fix_permissions.sh /app/fix_permissions.sh
RUN chmod +x /app/docker-entrypoint.sh /app/fix_permissions.sh

# Make the check_env.py script executable
RUN chmod +x /app/ShopeeAPI/check_env.py

# Create a non-root user to run the application
RUN groupadd -r shopeeapi && useradd -r -g shopeeapi shopeeapi
# Add shopeeapi to sudoers for the permission fix script
RUN echo "shopeeapi ALL=(root) NOPASSWD: /app/fix_permissions.sh" > /etc/sudoers.d/shopeeapi
RUN chmod 0440 /etc/sudoers.d/shopeeapi

# Set proper ownership
RUN chown -R shopeeapi:shopeeapi /app

# Switch to non-root user
USER shopeeapi

# Expose the port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8000/status || exit 1

# Run the application using our entrypoint script
ENTRYPOINT ["/app/docker-entrypoint.sh"]