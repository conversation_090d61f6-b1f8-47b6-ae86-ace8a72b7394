"""
Authentication middleware for Shopee API.
This middleware checks for authentication errors and provides consistent responses.
"""
from typing import Callable, Dict, Any
from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..core.exceptions import Authentication<PERSON>rror, RequestError


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle authentication errors consistently across all endpoints.
    """
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request and catch authentication errors.
        
        Args:
            request: The incoming request
            call_next: The next middleware or endpoint handler
            
        Returns:
            Response: The response from the endpoint or an error response
        """
        # Skip authentication check for certain endpoints
        skip_auth_check = [
            "/docs", 
            "/redoc", 
            "/openapi.json", 
            "/auth", 
            "/auth/check", 
            "/status",
            "/static",
            "/ws-docs",
            "/favicon.ico"
        ]
        
        # Check if the path should skip auth check
        path = request.url.path
        if any(path.startswith(prefix) for prefix in skip_auth_check):
            return await call_next(request)
        
        try:
            # Process the request normally
            return await call_next(request)
        except AuthenticationError as e:
            # Handle authentication errors
            return self._create_auth_error_response(e)
        except RequestError as e:
            # Check if it's a 403 error (likely expired credentials)
            if hasattr(e, 'status_code') and e.status_code == 403:
                return self._create_auth_error_response(e)
            elif '403' in str(e) or 'Forbidden' in str(e) or 'unauthorized' in str(e).lower():
                return self._create_auth_error_response(e)
            # For other request errors, re-raise
            raise
        except Exception as e:
            # Check if the exception message indicates an authentication issue
            error_msg = str(e).lower()
            if ('unauthorized' in error_msg or 
                'forbidden' in error_msg or 
                'authentication' in error_msg or
                'credentials' in error_msg and ('invalid' in error_msg or 'expired' in error_msg)):
                return self._create_auth_error_response(e)
            # For other exceptions, re-raise
            raise
    
    def _create_auth_error_response(self, error: Exception) -> JSONResponse:
        """
        Create a consistent error response for authentication errors.
        
        Args:
            error: The exception that was raised
            
        Returns:
            JSONResponse: A formatted error response
        """
        error_response: Dict[str, Any] = {
            "status": "error",
            "error": "Authentication failed",
            "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
            "code": "CREDENTIALS_EXPIRED"
        }
        
        # Add original error message for debugging
        if hasattr(error, 'message'):
            error_response["original_error"] = error.message
        else:
            error_response["original_error"] = str(error)
        
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content=error_response
        )
