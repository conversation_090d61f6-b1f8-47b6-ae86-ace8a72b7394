"""
Data models for chat-related operations.
"""
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field


class ChatMessagePayload(BaseModel):
    """Model for chat message payload."""
    text: str
    username: str
    force_send_cancel_order_warning: Optional[bool] = False
    comply_cancel_order_warning: Optional[bool] = False


class ImageMessagePayload(BaseModel):
    """Model for image message payload."""
    username: str
    image_url: str


class OrderMessagePayload(BaseModel):
    """Model for order message payload."""
    order_sn: str


class ChatMessage(BaseModel):
    """Model for a chat message."""
    message_id: str
    conversation_id: str
    from_id: str
    to_id: str
    type: str
    content: Dict[str, Any]
    create_time: int
    status: str


class Conversation(BaseModel):
    """Model for a conversation."""
    conversation_id: str
    to_id: str
    to_name: str
    to_avatar: Optional[str] = None
    last_message: Optional[ChatMessage] = None
    unread_count: int
    create_time: int
    update_time: int


class ChatResponse(BaseModel):
    """Model for API responses containing chat data."""
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
