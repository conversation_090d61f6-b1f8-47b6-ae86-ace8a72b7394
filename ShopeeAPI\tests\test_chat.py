"""
Tests for the chat functionality of the ShopeeAPI.
"""
import unittest
import requests
import json
import sys
import os
import time
from unittest.mock import patch, MagicMock

# Add parent directory to path to allow imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from ShopeeAPI.client import ShopeeAPI
except ImportError:
    from client import ShopeeAPI


class TestChatAPI(unittest.TestCase):
    """Test cases for the chat API endpoints."""
    
    API_URL = "http://localhost:8000"
    
    def setUp(self):
        """Set up test environment."""
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        # Default test order number
        self.test_order_sn = "2412064GY1714Y"
    
    def test_send_order_message_endpoint(self):
        """Test the send_order_message endpoint."""
        # Skip this test if the API server is not running
        try:
            requests.get(f"{self.API_URL}/status", timeout=1)
        except requests.RequestException:
            self.skipTest("API server is not running")
            
        url = f"{self.API_URL}/chat/send_order_message"
        payload = {
            "order_sn": self.test_order_sn
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            
            # Print response for debugging
            print(f"Response status code: {response.status_code}")
            try:
                response_json = response.json()
                print(f"Response JSON: {json.dumps(response_json, indent=2)}")
            except json.JSONDecodeError:
                print(f"Response text (not JSON): {response.text}")
                
            # Check if the request was successful
            self.assertIn(response.status_code, [200, 201, 202])
        except Exception as e:
            self.fail(f"Exception during test: {str(e)}")


class TestChatClient(unittest.TestCase):
    """Test cases for the chat functionality in the ShopeeAPI client."""
    
    def setUp(self):
        """Set up test environment."""
        # Create a mock ShopeeAPI instance
        self.api = MagicMock(spec=ShopeeAPI)
        
        # Default test order number
        self.test_order_sn = "2412064GY1714Y"
    
    @patch('ShopeeAPI.client.ShopeeAPI')
    def test_send_order_message_client(self, mock_api):
        """Test sending an order message directly using the ShopeeAPI client."""
        # Configure the mock
        mock_api.return_value.send_order_message.return_value = ({"success": True}, 200)
        
        # Create an instance of the mocked API
        api = mock_api()
        
        # Call the method
        response, status_code = api.send_order_message(self.test_order_sn)
        
        # Assertions
        self.assertEqual(status_code, 200)
        self.assertTrue(response.get("success"))
        
        # Verify the method was called with the correct parameters
        api.send_order_message.assert_called_once_with(self.test_order_sn)


if __name__ == "__main__":
    unittest.main()
