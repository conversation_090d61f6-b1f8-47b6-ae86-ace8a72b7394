"""
Cache management for Shopee API.
"""
import time
import json
import os
from typing import Dict, Any, Optional, Tuple, List
from collections import OrderedDict


class CacheEntry:
    """
    Represents a single cache entry with expiration.
    """

    def __init__(self, value: Any, expiry_seconds: int = 86400):
        """
        Initialize a cache entry.

        Args:
            value: The value to cache
            expiry_seconds: Time in seconds until the entry expires
        """
        self.value = value
        self.created_at = time.time()
        self.expiry_seconds = expiry_seconds

    def is_expired(self) -> bool:
        """
        Check if the entry has expired.

        Returns:
            True if expired, False otherwise
        """
        return time.time() > (self.created_at + self.expiry_seconds)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert entry to dictionary for serialization.

        Returns:
            Dictionary representation of the entry
        """
        return {
            "value": self.value,
            "created_at": self.created_at,
            "expiry_seconds": self.expiry_seconds
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """
        Create a cache entry from a dictionary.

        Args:
            data: Dictionary with entry data

        Returns:
            New CacheEntry instance
        """
        entry = cls(data["value"], data.get("expiry_seconds", 86400))
        entry.created_at = data.get("created_at", time.time())
        return entry


class LRUCache:
    """
    LRU (Least Recently Used) cache implementation with expiration.
    """

    def __init__(self, max_size: int = 1000, expiry_seconds: int = 86400, cache_file: Optional[str] = None):
        """
        Initialize the LRU cache.

        Args:
            max_size: Maximum number of items to store
            expiry_seconds: Default expiration time in seconds
            cache_file: Optional path to persist cache to disk
        """
        self.max_size = max_size
        self.expiry_seconds = expiry_seconds
        self.cache_file = cache_file
        self.cache = OrderedDict()  # type: OrderedDict[str, CacheEntry]

        # Load from file if provided
        if cache_file and os.path.exists(cache_file):
            self.load_from_file()

    def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found or expired
        """
        if key not in self.cache:
            return None

        entry = self.cache[key]

        # Check if expired
        if entry.is_expired():
            del self.cache[key]
            return None

        # Move to end (most recently used)
        self.cache.move_to_end(key)

        return entry.value

    def set(self, key: str, value: Any, expiry_seconds: Optional[int] = None) -> None:
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache
            expiry_seconds: Optional custom expiration time
        """
        # Use default expiry if not specified
        if expiry_seconds is None:
            expiry_seconds = self.expiry_seconds

        # Remove if already exists
        if key in self.cache:
            del self.cache[key]

        # Check if we need to remove the oldest item
        if len(self.cache) >= self.max_size:
            self.cache.popitem(last=False)  # Remove oldest

        # Add new entry
        self.cache[key] = CacheEntry(value, expiry_seconds)

        # Save to file if configured
        if self.cache_file:
            self.save_to_file()

    def delete(self, key: str) -> bool:
        """
        Delete an item from the cache.

        Args:
            key: Cache key

        Returns:
            True if deleted, False if not found
        """
        if key in self.cache:
            del self.cache[key]

            # Save to file if configured
            if self.cache_file:
                self.save_to_file()

            return True

        return False

    def clear(self) -> None:
        """Clear all items from the cache."""
        self.cache.clear()

        # Save to file if configured
        if self.cache_file:
            self.save_to_file()

    def cleanup_expired(self) -> int:
        """
        Remove all expired entries.

        Returns:
            Number of entries removed
        """
        expired_keys = [k for k, v in self.cache.items() if v.is_expired()]

        for key in expired_keys:
            del self.cache[key]

        # Save to file if configured and items were removed
        if expired_keys and self.cache_file:
            self.save_to_file()

        return len(expired_keys)

    def save_to_file(self) -> None:
        """Save cache to file."""
        if not self.cache_file:
            return

        # Convert to serializable format
        data = {
            "max_size": self.max_size,
            "expiry_seconds": self.expiry_seconds,
            "entries": {
                k: v.to_dict() for k, v in self.cache.items()
            }
        }

        try:
            with open(self.cache_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving cache to file: {e}")

    def load_from_file(self) -> None:
        """Load cache from file."""
        if not self.cache_file or not os.path.exists(self.cache_file):
            return

        try:
            with open(self.cache_file, 'r') as f:
                data = json.load(f)

            # Update settings
            self.max_size = data.get("max_size", self.max_size)
            self.expiry_seconds = data.get("expiry_seconds", self.expiry_seconds)

            # Load entries
            self.cache.clear()
            for key, entry_data in data.get("entries", {}).items():
                entry = CacheEntry.from_dict(entry_data)
                if not entry.is_expired():
                    self.cache[key] = entry
        except Exception as e:
            print(f"Error loading cache from file: {e}")
            # Start with empty cache on error
            self.cache.clear()


class CacheManager:
    """
    Manages different caches for the Shopee API.
    """

    def __init__(self, config: Dict[str, Any], cache_dir: Optional[str] = None):
        """
        Initialize the cache manager.

        Args:
            config: Cache configuration
            cache_dir: Directory to store cache files
        """
        self.config = config
        self.cache_dir = cache_dir

        # Create cache directory if it doesn't exist
        if cache_dir and not os.path.exists(cache_dir):
            os.makedirs(cache_dir)

        # Initialize caches
        self.username_to_conversation_id = self._create_username_cache()
        self.conversation_messages = self._create_conversation_messages_cache()

    def _create_username_cache(self) -> LRUCache:
        """
        Create the username to conversation ID cache.

        Returns:
            Configured LRU cache
        """
        enabled = self.config.get("enabled", True)
        if not enabled:
            return LRUCache(max_size=1, expiry_seconds=1)  # Dummy cache

        username_config = self.config.get("username_to_conversation_id", {})
        username_enabled = username_config.get("enabled", True)

        if not username_enabled:
            return LRUCache(max_size=1, expiry_seconds=1)  # Dummy cache

        max_size = username_config.get("max_size", 1000)
        expiry_seconds = username_config.get("expiry_seconds", 86400)

        cache_file = None
        if self.cache_dir:
            cache_file = os.path.join(self.cache_dir, "username_conversation_cache.json")

        return LRUCache(max_size=max_size, expiry_seconds=expiry_seconds, cache_file=cache_file)

    def _create_conversation_messages_cache(self) -> LRUCache:
        """
        Create the conversation messages cache.

        Returns:
            Configured LRU cache
        """
        enabled = self.config.get("enabled", True)
        if not enabled:
            return LRUCache(max_size=1, expiry_seconds=1)  # Dummy cache

        messages_config = self.config.get("conversation_messages", {})
        messages_enabled = messages_config.get("enabled", True)

        if not messages_enabled:
            return LRUCache(max_size=1, expiry_seconds=1)  # Dummy cache

        max_size = messages_config.get("max_size", 50)
        expiry_seconds = messages_config.get("expiry_seconds", 3600)

        cache_file = None
        if self.cache_dir:
            cache_file = os.path.join(self.cache_dir, "conversation_messages_cache.json")

        return LRUCache(max_size=max_size, expiry_seconds=expiry_seconds, cache_file=cache_file)
