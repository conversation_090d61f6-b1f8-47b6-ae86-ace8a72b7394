#!/bin/bash
# Docker entrypoint script for ShopeeAPI

# Create config.json if it doesn't exist
CONFIG_PATH="/app/ShopeeAPI/config.json"
ALT_CONFIG_PATH="/app/config.json"

if [ ! -f "$CONFIG_PATH" ] && [ ! -f "$ALT_CONFIG_PATH" ]; then
    echo "No config.json found, creating a default one"
    echo '{
  "AUTHORIZATION_CODE": "",
  "COOKIE": "",
  "SHOP_ID": 0,
  "REGION_ID": "MY",
  "PAGE_SIZE": 40,
  "REQUEST_TIMEOUT": 60,
  "WEBSOCKET": {
    "ENABLED": true,
    "RECONNECT_INTERVAL": 30,
    "MAX_RECONNECT_ATTEMPTS": 10,
    "PING_INTERVAL": 25,
    "PING_TIMEOUT": 5,
    "CLIENT_MAX_SIZE": 100
  },
  "WEBHOOK": {
    "ENABLED": false,
    "MESSAGE_RECEIVED": {
      "ENABLED": false,
      "URL": "http://your-webhook-url/message-received",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    },
    "MESSAGE_SENT": {
      "ENABLED": false,
      "URL": "http://your-webhook-url/message-sent",
      "RETRY_COUNT": 3,
      "RETRY_DELAY": 5
    }
  },
  "CACHE": {
    "ENABLED": true,
    "USERNAME_TO_CONVERSATION_ID": {
      "ENABLED": true,
      "EXPIRY_SECONDS": 86400,
      "MAX_SIZE": 1000
    },
    "CONVERSATION_MESSAGES": {
      "ENABLED": true,
      "EXPIRY_SECONDS": 3600,
      "MAX_SIZE": 50,
      "WEBSOCKET_ONLY": true
    }
  }
}' > "$CONFIG_PATH"
fi

# Start the application
export PYTHONPATH=/app
cd /app
echo "Starting ShopeeAPI on port: ${PORT:-8000}"
echo "Environment check:"
python /app/ShopeeAPI/check_env.py
exec uvicorn ShopeeAPI.api:app --host 0.0.0.0 --port ${PORT:-8000}
