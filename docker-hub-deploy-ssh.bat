@echo off
REM Docker Hub deployment script for ShopeeAPI on Linux servers via SSH
REM This script connects to a Linux server and deploys the ShopeeAPI Docker image

REM Configuration
set DOCKER_HUB_USERNAME=limjianhui789
set DOCKER_HUB_REPO=shopee-api
set IMAGE_NAME=%DOCKER_HUB_USERNAME%/%DOCKER_HUB_REPO%
set SERVER_HOST=your-server-hostname
set SERVER_USER=your-username
set SERVER_PATH=/path/to/shopee-api
set VERSION=latest

REM Check if SSH is available
where ssh >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: SSH client not found. Please install OpenSSH client or Git Bash.
    exit /b 1
)

REM Parse command line arguments
set DEPLOY=false
set RESTART=false
set ALL=false

:parse_args
if "%~1"=="" goto check_args
if "%~1"=="--deploy" (
    set DEPLOY=true
    shift
    goto parse_args
)
if "%~1"=="--restart" (
    set RESTART=true
    shift
    goto parse_args
)
if "%~1"=="--all" (
    set ALL=true
    shift
    goto parse_args
)
if "%~1"=="--version" (
    set VERSION=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--server" (
    set SERVER_HOST=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--user" (
    set SERVER_USER=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--path" (
    set SERVER_PATH=%~2
    shift
    shift
    goto parse_args
)
echo Unknown option: %~1
echo Usage: %0 [--deploy] [--restart] [--all] [--version VERSION] [--server SERVER] [--user USER] [--path PATH]
exit /b 1

:check_args
REM If no arguments are provided, show help
if "%DEPLOY%"=="false" if "%RESTART%"=="false" if "%ALL%"=="false" (
    echo Usage: %0 [--deploy] [--restart] [--all] [--version VERSION] [--server SERVER] [--user USER] [--path PATH]
    echo   --deploy: Deploy the Docker image on the server
    echo   --restart: Restart the container on the server
    echo   --all: Deploy and restart
    echo   --version VERSION: Specify a version (default: latest)
    echo   --server SERVER: Specify the server hostname
    echo   --user USER: Specify the server username
    echo   --path PATH: Specify the server path
    exit /b 0
)

REM Check if server details are provided
if "%SERVER_HOST%"=="your-server-hostname" (
    echo Error: Server hostname not specified. Use --server option or edit this script.
    exit /b 1
)
if "%SERVER_USER%"=="your-username" (
    echo Error: Server username not specified. Use --user option or edit this script.
    exit /b 1
)

echo Server: %SERVER_USER%@%SERVER_HOST%:%SERVER_PATH%
echo Image: %IMAGE_NAME%:%VERSION%

REM Deploy the Docker image on the server
if "%DEPLOY%"=="true" goto deploy
if "%ALL%"=="true" goto deploy
goto check_restart

:deploy
echo Deploying Docker image on server...

REM Create a temporary deployment script
echo #!/bin/bash > deploy-temp.sh
echo echo "Pulling Docker image from Docker Hub: %IMAGE_NAME%:%VERSION%" >> deploy-temp.sh
echo docker login >> deploy-temp.sh
echo docker pull %IMAGE_NAME%:%VERSION% >> deploy-temp.sh
echo echo "Stopping existing container if running..." >> deploy-temp.sh
echo docker stop shopeeapi 2^>/dev/null ^|^| true >> deploy-temp.sh
echo docker rm shopeeapi 2^>/dev/null ^|^| true >> deploy-temp.sh
echo echo "Creating directories if they don't exist..." >> deploy-temp.sh
echo mkdir -p %SERVER_PATH%/config >> deploy-temp.sh
echo mkdir -p %SERVER_PATH%/logs >> deploy-temp.sh
echo echo "Setting proper permissions for config file..." >> deploy-temp.sh
echo touch %SERVER_PATH%/config/config.json >> deploy-temp.sh
echo chmod 666 %SERVER_PATH%/config/config.json >> deploy-temp.sh
echo echo "Deploying Docker container..." >> deploy-temp.sh
echo docker run -d --name shopeeapi -p 8000:8000 -v %SERVER_PATH%/config/config.json:/app/ShopeeAPI/config.json -v %SERVER_PATH%/logs:/app/logs --restart unless-stopped -e PORT=8000 -e ENVIRONMENT=production --init %IMAGE_NAME%:%VERSION% >> deploy-temp.sh
echo echo "Container deployed: shopeeapi" >> deploy-temp.sh

REM Upload and execute the deployment script
echo Uploading deployment script to server...
ssh %SERVER_USER%@%SERVER_HOST% "mkdir -p %SERVER_PATH%"
scp deploy-temp.sh %SERVER_USER%@%SERVER_HOST%:%SERVER_PATH%/deploy.sh
ssh %SERVER_USER%@%SERVER_HOST% "chmod +x %SERVER_PATH%/deploy.sh && cd %SERVER_PATH% && ./deploy.sh"

REM Clean up the temporary script
del deploy-temp.sh
echo Deployment completed.

:check_restart
REM Restart the container on the server
if "%RESTART%"=="true" goto restart
if "%ALL%"=="true" goto restart
goto end

:restart
echo Restarting container on server...
ssh %SERVER_USER%@%SERVER_HOST% "docker restart shopeeapi"
echo Container restarted.

:end
echo Process completed!
