<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>

<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-3xl font-bold mb-6 text-center">Admin Panel</h1>

        <div class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">Current Configuration</h2>
            <div id="configEditor" class="space-y-4">
                <!-- Configuration fields will be dynamically added here -->
            </div>
            <button onclick="updateAllConfig()"
                class="mt-4 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">Update All</button>
        </div>

        <div class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">Email Configurations</h2>
            <table class="w-full border-collapse border border-gray-300">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="border border-gray-300 p-2">Steam Username</th>
                        <th class="border border-gray-300 p-2">Email</th>
                        <th class="border border-gray-300 p-2">App Password</th>
                        <th class="border border-gray-300 p-2">Actions</th>
                    </tr>
                </thead>
                <tbody id="emailConfigTable">
                    <!-- Email configurations will be dynamically added here -->
                </tbody>
            </table>
            <button onclick="showAddEmailModal()"
                class="mt-4 bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600">Add Email Config</button>
        </div>

        <div class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">Steam Credentials</h2>
            <table class="w-full border-collapse border border-gray-300">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="border border-gray-300 p-2">Steam Username</th>
                        <th class="border border-gray-300 p-2">Password</th>
                        <th class="border border-gray-300 p-2">Actions</th>
                    </tr>
                </thead>
                <tbody id="steamCredentialTable">
                    <!-- Steam credentials will be dynamically added here -->
                </tbody>
            </table>
            <button onclick="showAddSteamModal()"
                class="mt-4 bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600">Add Steam
                Credential</button>
        </div>

        <!-- Add Email Modal -->
        <div id="addEmailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center">
            <div class="bg-white p-8 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Add Email Configuration</h2>
                <input id="emailKey" type="text" placeholder="Key" class="w-full p-2 mb-2 border rounded-md">
                <input id="email" type="email" placeholder="Email" class="w-full p-2 mb-2 border rounded-md">
                <input id="appPassword" type="password" placeholder="App Password"
                    class="w-full p-2 mb-2 border rounded-md">
                <div class="flex justify-end space-x-2">
                    <button onclick="hideAddEmailModal()"
                        class="bg-gray-300 text-black px-4 py-2 rounded-md hover:bg-gray-400">Cancel</button>
                    <button onclick="addEmailConfig()"
                        class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600">Add</button>
                </div>
            </div>
        </div>

        <!-- Add Steam Modal -->
        <div id="addSteamModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center">
            <div class="bg-white p-8 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Add Steam Credential</h2>
                <input id="steamKey" type="text" placeholder="Key" class="w-full p-2 mb-2 border rounded-md">
                <input id="steamPassword" type="password" placeholder="Password"
                    class="w-full p-2 mb-2 border rounded-md">
                <div class="flex justify-end space-x-2">
                    <button onclick="hideAddSteamModal()"
                        class="bg-gray-300 text-black px-4 py-2 rounded-md hover:bg-gray-400">Cancel</button>
                    <button onclick="addSteamCredential()"
                        class="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600">Add</button>
                </div>
            </div>
        </div>

        <!-- Update Notification -->
        <div id="updateNotification" class="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md hidden">
            Configuration will update in the next 30 seconds
        </div>

        <script>
            let config = {};

            function renderConfig(config) {
                const configEditor = document.getElementById('configEditor');
                configEditor.innerHTML = '';

                for (const [key, value] of Object.entries(config)) {
                    if (key !== 'EMAIL_CONFIGS' && key !== 'STEAM_CREDENTIALS') {
                        const field = document.createElement('div');
                        field.className = 'flex items-center space-x-2';

                        if (typeof value === 'object' && value !== null) {
                            // Handle object values (like NOTIFICATION_EMAIL)
                            field.innerHTML = `
                    <label class="w-1/3">${key}:</label>
                    <div class="w-2/3">
                        ${Object.entries(value).map(([subKey, subValue]) => `
                            <div class="flex items-center space-x-2 mt-2">
                                <label class="w-1/3">${subKey}:</label>
                                <input type="text" value="${subValue}" class="w-2/3 p-2 border rounded-md" 
                                       onchange="updateConfigField('${key}', '${subKey}', this.value)">
                            </div>
                        `).join('')}
                    </div>
                `;
                        } else {
                            // Handle simple string/number values
                            field.innerHTML = `
                    <label class="w-1/3">${key}:</label>
                    <input type="text" value="${value}" class="w-2/3 p-2 border rounded-md" 
                           onchange="updateConfigField('${key}', null, this.value)">
                `;
                        }
                        configEditor.appendChild(field);
                    }
                }
            }

            function renderEmailConfigs(emailConfigs) {
                const table = document.getElementById('emailConfigTable');
                table.innerHTML = '';

                for (const [key, config] of Object.entries(emailConfigs)) {
                    const row = table.insertRow();
                    row.innerHTML = `
                    <td class="border border-gray-300 p-2">${key}</td>
                    <td class="border border-gray-300 p-2">${config.email}</td>
                    <td class="border border-gray-300 p-2">********</td>
                    <td class="border border-gray-300 p-2">
                        <button onclick="editEmailConfig('${key}')" class="text-blue-500 hover:text-blue-700">Edit</button>
                        <button onclick="deleteEmailConfig('${key}')" class="text-red-500 hover:text-red-700 ml-2">Delete</button>
                    </td>
                `;
                }
            }

            function renderSteamCredentials(steamCredentials) {
                const table = document.getElementById('steamCredentialTable');
                table.innerHTML = '';

                for (const [key, credential] of Object.entries(steamCredentials)) {
                    const row = table.insertRow();
                    row.innerHTML = `
                <td class="border border-gray-300 p-2">${key}</td>
                <td class="border border-gray-300 p-2">********</td>
                <td class="border border-gray-300 p-2">
                    <button onclick="editSteamCredential('${key}')" class="text-blue-500 hover:text-blue-700">Edit</button>
                    <button onclick="deleteSteamCredential('${key}')" class="text-red-500 hover:text-red-700 ml-2">Delete</button>
                </td>
            `;
                }
            }

            function showAddEmailModal() {
                document.getElementById('addEmailModal').style.display = 'flex';
            }

            function hideAddEmailModal() {
                document.getElementById('addEmailModal').style.display = 'none';
            }

            function showAddSteamModal() {
                document.getElementById('addSteamModal').style.display = 'flex';
            }

            function hideAddSteamModal() {
                document.getElementById('addSteamModal').style.display = 'none';
            }

            function updateConfigField(key, subKey, value) {
                if (subKey) {
                    if (!config[key]) {
                        config[key] = {};
                    }
                    config[key][subKey] = value;
                } else {
                    config[key] = value;
                }
            }

            function updateAllConfig() {
                axios.post('/admin/update_config', config)
                    .then(response => {
                        showUpdateNotification();
                    })
                    .catch(error => {
                        console.error('Error updating config:', error);
                    });
            }

            function addEmailConfig() {
                const key = document.getElementById('emailKey').value;
                const email = document.getElementById('email').value;
                const appPassword = document.getElementById('appPassword').value;

                axios.post('/admin/add_email_config', { key, email, app_password: appPassword })
                    .then(response => {
                        hideAddEmailModal();
                        loadConfig();
                        showUpdateNotification();
                    })
                    .catch(error => {
                        console.error('Error adding email config:', error);
                    });
            }

            function addSteamCredential() {
                const key = document.getElementById('steamKey').value;
                const password = document.getElementById('steamPassword').value;

                axios.post('/admin/add_steam_credential', { key, password })
                    .then(response => {
                        hideAddSteamModal();
                        loadConfig();
                        showUpdateNotification();
                    })
                    .catch(error => {
                        console.error('Error adding steam credential:', error);
                    });
            }

            function editEmailConfig(key) {
                const emailConfig = config.EMAIL_CONFIGS[key];
                document.getElementById('emailKey').value = key;
                document.getElementById('email').value = emailConfig.email;
                document.getElementById('appPassword').value = emailConfig.app_password;
                showAddEmailModal();
                // 修改添加按钮为更新按钮
                const updateButton = document.querySelector('#addEmailModal button:last-child');
                updateButton.textContent = 'Update';
                updateButton.onclick = () => updateEmailConfig(key);
            }

            function updateEmailConfig(key) {
                const email = document.getElementById('email').value;
                const appPassword = document.getElementById('appPassword').value;

                axios.post('/admin/update_email_config', { key, email, app_password: appPassword })
                    .then(response => {
                        hideAddEmailModal();
                        loadConfig();
                        showUpdateNotification();
                    })
                    .catch(error => {
                        console.error('Error updating email config:', error);
                    });
            }

            function deleteEmailConfig(key) {
                if (confirm(`Are you sure you want to delete the email config for ${key}?`)) {
                    axios.post('/admin/delete_email_config', { key })
                        .then(response => {
                            loadConfig();
                            showUpdateNotification();
                        })
                        .catch(error => {
                            console.error('Error deleting email config:', error);
                        });
                }
            }

            function editSteamCredential(key) {
                const credential = config.STEAM_CREDENTIALS[key];
                document.getElementById('steamKey').value = key;
                document.getElementById('steamPassword').value = credential.password;
                showAddSteamModal();
                // 修改添加按钮为更新按钮
                const updateButton = document.querySelector('#addSteamModal button:last-child');
                updateButton.textContent = 'Update';
                updateButton.onclick = () => updateSteamCredential(key);
            }

            function updateSteamCredential(key) {
                const password = document.getElementById('steamPassword').value;

                axios.post('/admin/update_steam_credential', { key, password })
                    .then(response => {
                        hideAddSteamModal();
                        loadConfig();
                        showUpdateNotification();
                    })
                    .catch(error => {
                        console.error('Error updating steam credential:', error);
                    });
            }
            function deleteSteamCredential(key) {
                if (confirm(`Are you sure you want to delete the steam credential for ${key}?`)) {
                    axios.post('/admin/delete_steam_credential', { key })
                        .then(response => {
                            loadConfig();
                            showUpdateNotification();
                        })
                        .catch(error => {
                            console.error('Error deleting steam credential:', error);
                        });
                }
            }

            function showUpdateNotification() {
                const notification = document.getElementById('updateNotification');
                notification.classList.remove('hidden');
                setTimeout(() => {
                    notification.classList.add('hidden');
                }, 5000);
            }

            function loadConfig() {
                axios.get('/admin/get_config')
                    .then(response => {
                        config = response.data;  // 更新全局 config 对象
                        renderConfig(config);
                        renderEmailConfigs(config.EMAIL_CONFIGS);
                        renderSteamCredentials(config.STEAM_CREDENTIALS);
                    })
                    .catch(error => {
                        console.error('Error loading config:', error);
                    });
            }

            // Load initial data
            loadConfig();
        </script>
</body>

</html>