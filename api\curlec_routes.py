from flask import Blueprint, jsonify, request
from services.curlec_service import CurlecService
from http import HTTPStatus
from config import API_KEY
import json

curlec_blueprint = Blueprint('curlec', __name__)
curlec_service = CurlecService()

@curlec_blueprint.route('/api/curlec/customers', methods=['GET'])
def get_all_customers():
    # Verify API Key
    api_key = request.headers.get('X-API-Key')
    if not api_key or api_key != API_KEY:
        error_response = {
            "error": {
                "code": "UNAUTHORIZED",
                "description": "Invalid or missing API key",
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.UNAUTHORIZED

    try:
        # Get query parameters with defaults
        count = request.args.get('count', default=10, type=int)
        skip = request.args.get('skip', default=0, type=int)
        
        customers = curlec_service.get_all_customers(count=count, skip=skip)
        
        response = {
            "entity": "collection",
            "count": len(customers),
            "items": customers
        }
        
        return jsonify(response), HTTPStatus.OK
        
    except Exception as e:
        error_response = {
            "error": {
                "code": "BAD_REQUEST_ERROR",
                "description": str(e),
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.BAD_REQUEST

@curlec_blueprint.route('/api/curlec/customers', methods=['POST'])
def create_customer():
    # Verify API Key
    api_key = request.headers.get('X-API-Key')
    if not api_key or api_key != API_KEY:
        error_response = {
            "error": {
                "code": "UNAUTHORIZED",
                "description": "Invalid or missing API key",
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.UNAUTHORIZED

    try:
        # Get JSON data from request
        data = request.get_json()
        
        # Extract required fields
        name = data.get('name')
        contact = data.get('contact')
        email = data.get('email')
        
        # Extract optional fields
        fail_existing = data.get('fail_existing', '0')
        notes = data.get('notes')
        
        # Validate required fields
        if not all([name, contact, email]):
            raise ValueError("Missing required fields: name, contact, and email are required")
        
        # Create customer using service
        customer = curlec_service.create_customer(
            name=name,
            contact=contact,
            email=email,
            fail_existing=fail_existing,
            notes=notes
        )
        
        return jsonify(customer), HTTPStatus.OK
        
    except Exception as e:
        error_response = {
            "error": {
                "code": "BAD_REQUEST_ERROR",
                "description": str(e),
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.BAD_REQUEST

@curlec_blueprint.route('/api/curlec/customers/<customer_id>', methods=['PUT'])
def update_customer(customer_id):
    # Verify API Key
    api_key = request.headers.get('X-API-Key')
    if not api_key or api_key != API_KEY:
        error_response = {
            "error": {
                "code": "UNAUTHORIZED",
                "description": "Invalid or missing API key",
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.UNAUTHORIZED

    try:
        # Get JSON data from request
        data = request.get_json()
        
        # Extract optional fields
        name = data.get('name')
        contact = data.get('contact')
        email = data.get('email')
        notes = data.get('notes')
        
        # Update customer using service
        customer = curlec_service.edit_customer(
            customer_id=customer_id,
            name=name,
            contact=contact,
            email=email,
            notes=notes
        )
        
        return jsonify(customer), HTTPStatus.OK
        
    except Exception as e:
        error_response = {
            "error": {
                "code": "BAD_REQUEST_ERROR",
                "description": str(e),
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.BAD_REQUEST

@curlec_blueprint.route('/api/curlec/customers/search', methods=['GET'])
def get_customer_by_name():
    # Verify API Key
    api_key = request.headers.get('X-API-Key')
    if not api_key or api_key != API_KEY:
        error_response = {
            "error": {
                "code": "UNAUTHORIZED",
                "description": "Invalid or missing API key",
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.UNAUTHORIZED

    try:
        # Get name parameter
        name = request.args.get('name')
        if not name:
            raise ValueError("Name parameter is required")
        
        # Get customer using service
        customer = curlec_service.get_customer_by_name(name=name)
        
        return jsonify(customer), HTTPStatus.OK
        
    except Exception as e:
        error_response = {
            "error": {
                "code": "BAD_REQUEST_ERROR",
                "description": str(e),
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.BAD_REQUEST

@curlec_blueprint.route('/api/curlec/customers/id', methods=['GET'])
def get_customer_id_by_name():
    # Verify API Key
    api_key = request.headers.get('X-API-Key')
    if not api_key or api_key != API_KEY:
        error_response = {
            "error": {
                "code": "UNAUTHORIZED",
                "description": "Invalid or missing API key",
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.UNAUTHORIZED

    try:
        # Get name parameter
        name = request.args.get('name')
        if not name:
            raise ValueError("Name parameter is required")
        
        # Get customer using service
        customer = curlec_service.get_customer_by_name(name=name)
        
        response = {
            "customer_id": customer.get('id'),
            "name": customer.get('name')
        }
        
        return jsonify(response), HTTPStatus.OK
        
    except Exception as e:
        error_response = {
            "error": {
                "code": "BAD_REQUEST_ERROR",
                "description": str(e),
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.BAD_REQUEST

@curlec_blueprint.route('/api/curlec/payment-links', methods=['POST'])
def create_payment_link():
    # Verify API Key
    api_key = request.headers.get('X-API-Key')
    if not api_key or api_key != API_KEY:
        error_response = {
            "error": {
                "code": "UNAUTHORIZED",
                "description": "Invalid or missing API key",
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.UNAUTHORIZED

    try:
        data = request.get_json()
        
        # Validate required fields including var_sku
        required_fields = ['amount', 'currency', 'customer', 'description', 'reference_id', 'var_sku']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
        
        # Get checkout name from options if provided
        options = data.get('options', {})
        checkout = options.get('checkout', {})
        checkout_name = checkout.get('name')
        
        # Create payment link using service
        payment_link = curlec_service.create_payment_link(
            amount=data['amount'],
            currency=data['currency'],
            customer=data['customer'],
            description=data['description'],
            reference_id=data['reference_id'],
            var_sku=data['var_sku'],
            accept_partial=data.get('accept_partial', False),
            first_min_partial_amount=data.get('first_min_partial_amount'),
            expire_by=data.get('expire_by'),
            notify=data.get('notify'),
            reminder_enable=data.get('reminder_enable', False),
            notes=data.get('notes'),
            callback_url=data.get('callback_url'),
            callback_method=data.get('callback_method', 'get'),
            checkout_name=checkout_name
        )
        
        return jsonify(payment_link), HTTPStatus.OK
        
    except ValueError as e:
        error_response = {
            "error": {
                "code": "INVALID_REQUEST",
                "description": str(e),
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.BAD_REQUEST
    except Exception as e:
        error_response = {
            "error": {
                "code": "SERVER_ERROR",
                "description": str(e),
                "source": "NA",
                "step": "NA",
                "reason": "NA",
                "metadata": {}
            }
        }
        return jsonify(error_response), HTTPStatus.INTERNAL_SERVER_ERROR

@curlec_blueprint.route('/admin/get_payment_links', methods=['GET'])
def get_payment_links():
    try:
        with open('manual_invoice.json', 'r') as f:
            data = json.load(f)
        return jsonify(data)
    except FileNotFoundError:
        return jsonify({})
