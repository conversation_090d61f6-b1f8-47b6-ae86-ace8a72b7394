"""
Custom exceptions for the Shopee API.
"""
from typing import Optional, Dict, Any


class ShopeeAPIError(Exception):
    """Base exception for Shopee API errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.response = response
        super().__init__(self.message)


class AuthenticationError(ShopeeAPIError):
    """Raised when authentication fails."""
    pass


class ConfigurationError(ShopeeAPIError):
    """Raised when there's an issue with the configuration."""
    pass


class RequestError(ShopeeAPIError):
    """Raised when a request to the Shopee API fails."""
    pass


class ResourceNotFoundError(ShopeeAPIError):
    """Raised when a requested resource is not found."""
    pass


class RateLimitError(ShopeeAPIError):
    """Raised when rate limits are exceeded."""
    pass


class ValidationError(ShopeeAPIError):
    """Raised when input validation fails."""
    pass
