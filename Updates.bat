@echo off
setlocal enabledelayedexpansion

:: Set the name of the zip file
set "zipfile=app_updates.zip"

:: Delete the zip file if it already exists
if exist "%zipfile%" del "%zipfile%"

:: Create a new zip file
powershell Compress-Archive -Path "api", "scheduler", "services", "static", "templates", "utils", "config.py" -DestinationPath "%zipfile%"

:: Check if the zip file was created successfully
if exist "%zipfile%" (
    echo Package created successfully: %zipfile%
) else (
    echo Failed to create package.
)

pause