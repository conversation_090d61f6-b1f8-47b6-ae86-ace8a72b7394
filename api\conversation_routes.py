from flask import Blueprint, jsonify, request
from services.conversation_service import (
    get_conversation_info_by_ordersn, get_conversation_info_by_username
)
from utils.auth import require_api_key

conversation_bp = Blueprint('conversation', __name__)

@conversation_bp.route('/get_conversation_info_by_ordersn', methods=['GET'])
@require_api_key
def api_get_conversation_info_by_ordersn():
    order_sn = request.args.get('order_sn')
    if not order_sn:
        return jsonify({"error": "order_sn parameter is required"}), 400
    return jsonify(get_conversation_info_by_ordersn(order_sn))

@conversation_bp.route('/get_conversation_info_by_username', methods=['GET'])
@require_api_key
def api_get_conversation_info_by_username():
    username = request.args.get('username')
    if not username:
        return jsonify({"error": "username parameter is required"}), 400
    return jsonify(get_conversation_info_by_username(username))